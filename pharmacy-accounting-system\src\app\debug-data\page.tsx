'use client'

import { useState } from 'react'
import AppLayout from '@/components/AppLayout'
import { Search, Database, Bug } from 'lucide-react'

export default function DebugDataPage() {
  const [debugInfo, setDebugInfo] = useState<any>(null)
  const [selectedInvoiceId, setSelectedInvoiceId] = useState('')
  const [availableInvoices, setAvailableInvoices] = useState<any[]>([])
  const [availableMedicines, setAvailableMedicines] = useState<any[]>([])

  const analyzeData = () => {
    try {
      const salesInvoices = JSON.parse(localStorage.getItem('sales_invoices') || '[]')
      const salesItems = JSON.parse(localStorage.getItem('sales_invoice_items') || '[]')
      const medicines = JSON.parse(localStorage.getItem('medicines') || '[]')
      const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')

      console.log('📊 تحليل البيانات:')
      console.log('الفواتير:', salesInvoices)
      console.log('عناصر الفواتير:', salesItems)
      console.log('الأدوية:', medicines)
      console.log('الدفعات:', batches)

      // Analyze the structure
      const analysis = {
        invoicesCount: salesInvoices.length,
        itemsCount: salesItems.length,
        medicinesCount: medicines.length,
        batchesCount: batches.length,
        sampleInvoice: salesInvoices[0] || null,
        sampleItem: salesItems[0] || null,
        sampleMedicine: medicines[0] || null,
        sampleBatch: batches[0] || null,
        itemsWithNames: salesItems.filter((item: any) => 
          item.medicine_name && item.medicine_name !== 'غير محدد'
        ).length,
        itemsWithBatchStructure: salesItems.filter((item: any) => 
          item.medicine_batches?.medicines?.name
        ).length,
        batchMedicineMapping: batches.map((batch: any) => {
          const medicine = medicines.find((m: any) => m.id === batch.medicine_id)
          return {
            batchId: batch.id,
            batchCode: batch.batch_code,
            medicineId: batch.medicine_id,
            medicineName: medicine?.name || 'غير موجود'
          }
        }).slice(0, 5), // First 5 for sample
        itemMedicineMapping: salesItems.map((item: any) => {
          const batch = batches.find((b: any) => b.id === item.medicine_batch_id)
          const medicine = medicines.find((m: any) => m.id === batch?.medicine_id)
          return {
            itemId: item.id,
            batchId: item.medicine_batch_id,
            currentName: item.medicine_name || item.medicineName,
            batchStructureName: item.medicine_batches?.medicines?.name,
            actualMedicineName: medicine?.name,
            batchCode: batch?.batch_code
          }
        }).slice(0, 10) // First 10 for sample
      }

      setDebugInfo(analysis)
      setAvailableInvoices(salesInvoices.slice(0, 10)) // Show first 10 invoices
      setAvailableMedicines(medicines.slice(0, 10)) // Show first 10 medicines
    } catch (error) {
      console.error('خطأ في تحليل البيانات:', error)
      setDebugInfo({ error: error.toString() })
    }
  }

  const analyzeSpecificInvoice = () => {
    if (!selectedInvoiceId) return

    try {
      const salesInvoices = JSON.parse(localStorage.getItem('sales_invoices') || '[]')
      const salesItems = JSON.parse(localStorage.getItem('sales_invoice_items') || '[]')
      const medicines = JSON.parse(localStorage.getItem('medicines') || '[]')
      const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')

      console.log('🔍 تحليل فاتورة محددة:', selectedInvoiceId)
      console.log('جميع الفواتير:', salesInvoices.map(inv => ({ id: inv.id, number: inv.invoice_number })))
      console.log('جميع العناصر:', salesItems.map(item => ({ id: item.id, invoice_id: item.invoice_id })))

      const invoice = salesInvoices.find((inv: any) => inv.id === selectedInvoiceId)
      const items = salesItems.filter((item: any) => item.invoice_id === selectedInvoiceId)

      console.log('الفاتورة الموجودة:', invoice)
      console.log('العناصر المطابقة:', items)
      console.log('عدد العناصر المطابقة:', items.length)

      // If no items found, try different matching strategies
      if (items.length === 0) {
        console.log('⚠️ لم يتم العثور على عناصر، جرب استراتيجيات أخرى...')

        // Try matching by invoice number
        const itemsByNumber = salesItems.filter((item: any) =>
          item.invoice_number === invoice?.invoice_number
        )
        console.log('العناصر بالرقم:', itemsByNumber)

        // Try partial matching
        const itemsByPartialId = salesItems.filter((item: any) =>
          item.invoice_id && item.invoice_id.includes(selectedInvoiceId.slice(-5))
        )
        console.log('العناصر بالمطابقة الجزئية:', itemsByPartialId)

        // Show all items for manual inspection
        console.log('جميع العناصر للفحص اليدوي:', salesItems)
      }

      const detailedAnalysis = {
        searchedId: selectedInvoiceId,
        invoice,
        itemsFound: items.length,
        allInvoiceIds: salesInvoices.map(inv => inv.id),
        allItemInvoiceIds: [...new Set(salesItems.map(item => item.invoice_id))],
        items: items.map((item: any) => {
          const batch = batches.find((b: any) => b.id === item.medicine_batch_id)
          const medicine = medicines.find((m: any) => m.id === batch?.medicine_id)

          return {
            ...item,
            debug: {
              batchFound: !!batch,
              medicineFound: !!medicine,
              batchData: batch,
              medicineData: medicine,
              currentMedicineName: item.medicine_name || item.medicineName,
              batchStructureName: item.medicine_batches?.medicines?.name,
              calculatedName: medicine?.name || 'غير موجود'
            }
          }
        })
      }

      setDebugInfo({ specificInvoice: detailedAnalysis })
      console.log('تحليل مفصل:', detailedAnalysis)
    } catch (error) {
      console.error('خطأ في تحليل الفاتورة:', error)
    }
  }

  const fixDataStructure = () => {
    try {
      const salesItems = JSON.parse(localStorage.getItem('sales_invoice_items') || '[]')
      const medicines = JSON.parse(localStorage.getItem('medicines') || '[]')
      const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')

      console.log('🔧 إصلاح هيكل البيانات...')
      console.log('📦 عناصر الفواتير:', salesItems.length)
      console.log('💊 الأدوية:', medicines.length)
      console.log('📋 الدفعات:', batches.length)

      // Find missing batches and try to recreate them
      const missingBatches = new Set()
      const fixedItems = salesItems.map((item: any) => {
        const batch = batches.find((b: any) => b.id === item.medicine_batch_id)

        if (!batch) {
          missingBatches.add(item.medicine_batch_id)
          console.log(`⚠️ دفعة مفقودة: ${item.medicine_batch_id}`)

          // Try to find medicine by name if available
          let medicine = null
          if (item.medicine_name && item.medicine_name !== 'غير محدد') {
            medicine = medicines.find((m: any) => m.name === item.medicine_name)
          }

          if (!medicine && medicines.length > 0) {
            // If no medicine found by name, use the first available medicine as fallback
            medicine = medicines[0]
            console.log(`🔄 استخدام دواء افتراضي: ${medicine.name}`)
          }

          if (medicine) {
            // Create a new batch for this medicine
            const newBatch = {
              id: item.medicine_batch_id,
              medicine_id: medicine.id,
              batch_code: `BATCH-${Date.now()}`,
              quantity: 1000, // Default quantity
              expiry_date: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 1 year from now
              created_at: new Date().toISOString()
            }

            batches.push(newBatch)
            console.log(`✅ تم إنشاء دفعة جديدة: ${newBatch.batch_code} للدواء ${medicine.name}`)

            return {
              ...item,
              medicine_name: medicine.name,
              medicineName: medicine.name,
              medicine_batches: {
                id: newBatch.id,
                batch_code: newBatch.batch_code,
                expiry_date: newBatch.expiry_date,
                medicine_id: newBatch.medicine_id,
                medicines: {
                  id: medicine.id,
                  name: medicine.name,
                  category: medicine.category || '',
                  manufacturer: medicine.manufacturer || '',
                  strength: medicine.strength || '',
                  form: medicine.form || ''
                }
              }
            }
          } else {
            console.log(`❌ لا توجد أدوية متاحة لإنشاء دفعة للعنصر ${item.id}`)
            return item
          }
        } else {
          // Batch exists, find medicine
          const medicine = medicines.find((m: any) => m.id === batch.medicine_id)

          if (medicine?.name) {
            console.log(`✅ إصلاح: ${medicine.name} للعنصر ${item.id}`)

            return {
              ...item,
              medicine_name: medicine.name,
              medicineName: medicine.name,
              medicine_batches: {
                id: batch.id,
                batch_code: batch.batch_code || '',
                expiry_date: batch.expiry_date || '',
                medicine_id: batch.medicine_id,
                medicines: {
                  id: medicine.id,
                  name: medicine.name,
                  category: medicine.category || '',
                  manufacturer: medicine.manufacturer || '',
                  strength: medicine.strength || '',
                  form: medicine.form || ''
                }
              }
            }
          } else {
            console.log(`❌ لم يتم العثور على الدواء للدفعة ${batch.id}`)
            return item
          }
        }
      })

      // Save updated batches and items
      localStorage.setItem('medicine_batches', JSON.stringify(batches))
      localStorage.setItem('sales_invoice_items', JSON.stringify(fixedItems))

      console.log(`✅ تم حفظ البيانات المصلحة`)
      console.log(`📋 تم إنشاء ${missingBatches.size} دفعة جديدة`)

      // Re-analyze after fix
      analyzeData()
    } catch (error) {
      console.error('خطأ في إصلاح البيانات:', error)
    }
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center gap-3 mb-6">
            <div className="bg-red-50 p-3 rounded-lg">
              <Bug className="h-6 w-6 text-red-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">تشخيص مشكلة البيانات</h1>
              <p className="text-gray-600">فحص مفصل لبيانات localStorage وهيكل الفواتير</p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <button
              onClick={analyzeData}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 flex items-center gap-2 justify-center"
            >
              <Database className="h-5 w-5" />
              تحليل البيانات العام
            </button>
            
            <div className="flex gap-2">
              <input
                type="text"
                value={selectedInvoiceId}
                onChange={(e) => setSelectedInvoiceId(e.target.value)}
                placeholder="معرف الفاتورة"
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg"
              />
              <button
                onClick={analyzeSpecificInvoice}
                className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700"
              >
                <Search className="h-5 w-5" />
              </button>
            </div>
            
            <button
              onClick={fixDataStructure}
              className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 flex items-center gap-2 justify-center"
            >
              <Bug className="h-5 w-5" />
              إصلاح فوري
            </button>
          </div>

          {availableInvoices.length > 0 && (
            <div className="bg-blue-50 rounded-lg p-4 mb-4">
              <h3 className="font-medium text-gray-900 mb-3">الفواتير المتاحة (أول 10):</h3>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-2">
                {availableInvoices.map((invoice) => (
                  <button
                    key={invoice.id}
                    onClick={() => setSelectedInvoiceId(invoice.id)}
                    className="bg-white p-2 rounded border text-sm hover:bg-blue-100 text-left"
                  >
                    <div className="font-medium">{invoice.invoice_number}</div>
                    <div className="text-xs text-gray-500">{invoice.id.slice(0, 8)}...</div>
                  </button>
                ))}
              </div>
            </div>
          )}

          {availableMedicines.length > 0 && (
            <div className="bg-green-50 rounded-lg p-4 mb-4">
              <h3 className="font-medium text-gray-900 mb-3">الأدوية المتاحة (أول 10):</h3>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                {availableMedicines.map((medicine) => (
                  <div
                    key={medicine.id}
                    className="bg-white p-2 rounded border text-sm"
                  >
                    <div className="font-medium">{medicine.name}</div>
                    <div className="text-xs text-gray-500">{medicine.category}</div>
                    <div className="text-xs text-gray-400">{medicine.id.slice(0, 8)}...</div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {debugInfo && (
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="font-medium text-gray-900 mb-3">نتائج التشخيص:</h3>
              <div className="bg-black text-green-400 p-4 rounded font-mono text-sm max-h-96 overflow-y-auto">
                <pre>{JSON.stringify(debugInfo, null, 2)}</pre>
              </div>
            </div>
          )}
        </div>
      </div>
    </AppLayout>
  )
}
