import { PrintSettings } from '@/hooks/usePrintSettings'

export const generateLarenInvoiceHTML = (invoice: any, type: 'sales' | 'purchase' | 'return', settings: PrintSettings) => {
  let items, customerSupplier, documentTitle, documentNumber

  if (type === 'return') {
    items = invoice.return_invoice_items || invoice.sales_return_items || invoice.purchase_return_items || []
    const returnType = invoice.type || invoice.return_type || 'sales'
    customerSupplier = returnType === 'sales'
      ? (invoice.customers?.name || invoice.customer_name || invoice.customerName || 'عميل نقدي')
      : (invoice.suppliers?.name || invoice.supplier_name || invoice.supplierName || 'غير محدد')
    documentTitle = returnType === 'sales' ? 'مرتجع مبيعات' : 'مرتجع مشتريات'
    documentNumber = invoice.return_number || invoice.invoice_number || 'غير محدد'
  } else {
    items = type === 'sales' ? invoice.sales_invoice_items : invoice.purchase_invoice_items
    customerSupplier = type === 'sales'
      ? (invoice.customers?.name || invoice.customer_name || invoice.customerName || 'عميل نقدي')
      : (invoice.suppliers?.name || invoice.supplier_name || invoice.supplierName || 'غير محدد')
    documentTitle = type === 'sales' ? 'فاتورة مبيعات' : 'فاتورة مشتريات'
    documentNumber = invoice.invoice_number || 'غير محدد'
  }

  return `
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${documentTitle} - ${documentNumber}</title>
      <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
          font-family: 'Arial', sans-serif; 
          font-size: 14px;
          color: #000;
          background-color: #fff;
          line-height: 1.4;
          direction: rtl;
        }
        .container { 
          max-width: 210mm; 
          margin: 0 auto; 
          padding: 10mm;
          border: 2px solid #000;
          min-height: 297mm;
          position: relative;
        }
        
        /* Header Section */
        .header {
          border-bottom: 2px solid #000;
          padding-bottom: 15px;
          margin-bottom: 20px;
          display: flex;
          align-items: center;
          justify-content: space-between;
        }
        
        .company-info {
          flex: 1;
          text-align: right;
        }
        
        .company-name-ar {
          font-size: 24px;
          font-weight: bold;
          margin-bottom: 5px;
        }
        
        .company-name-en {
          font-size: 18px;
          font-weight: bold;
          margin-bottom: 10px;
          direction: ltr;
          text-align: left;
        }
        
        .company-address {
          font-size: 14px;
          margin-bottom: 5px;
        }
        
        .logo-section {
          width: 120px;
          height: 120px;
          border: 2px solid #000;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 20px;
          background-color: #f8f9fa;
        }
        
        .logo-text {
          font-size: 16px;
          font-weight: bold;
          text-align: center;
          line-height: 1.2;
        }
        
        /* Invoice Details Section */
        .invoice-details {
          display: flex;
          justify-content: space-between;
          margin-bottom: 20px;
          border: 1px solid #000;
        }
        
        .invoice-info, .customer-info {
          flex: 1;
          padding: 10px;
          border-right: 1px solid #000;
        }
        
        .customer-info {
          border-right: none;
        }
        
        .detail-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;
          padding: 2px 0;
        }
        
        .detail-label {
          font-weight: bold;
          min-width: 80px;
        }
        
        .detail-value {
          text-align: left;
        }
        
        /* Items Table */
        .items-table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 20px;
          border: 2px solid #000;
        }
        
        .items-table th,
        .items-table td {
          border: 1px solid #000;
          padding: 8px;
          text-align: center;
          font-size: 12px;
        }
        
        .items-table th {
          background-color: #f0f0f0;
          font-weight: bold;
        }
        
        .items-table .item-name {
          text-align: right;
          padding-right: 10px;
        }
        
        /* Totals Section */
        .totals-section {
          width: 300px;
          margin-left: auto;
          border: 2px solid #000;
          margin-bottom: 20px;
        }
        
        .totals-section table {
          width: 100%;
          border-collapse: collapse;
        }
        
        .totals-section td {
          border: 1px solid #000;
          padding: 8px;
          font-size: 14px;
        }
        
        .totals-section .total-label {
          background-color: #f0f0f0;
          font-weight: bold;
          text-align: right;
        }
        
        .totals-section .total-value {
          text-align: center;
          font-weight: bold;
        }
        
        /* Footer Section */
        .footer {
          position: absolute;
          bottom: 10mm;
          left: 10mm;
          right: 10mm;
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-top: 1px solid #000;
          padding-top: 15px;
        }
        
        .signature-section {
          text-align: center;
          flex: 1;
        }
        
        .signature-box {
          width: 150px;
          height: 80px;
          border: 2px solid #000;
          border-radius: 50%;
          margin: 0 auto 10px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          color: #666;
        }
        
        .notes-section {
          margin-bottom: 30px;
          border: 1px solid #000;
          padding: 10px;
          min-height: 60px;
        }
        
        .notes-label {
          font-weight: bold;
          margin-bottom: 5px;
        }
        
        @media print {
          body { margin: 0; }
          .container { 
            border: 2px solid #000;
            box-shadow: none;
            margin: 0;
            padding: 10mm;
          }
          @page {
            size: A4;
            margin: 0;
          }
        }
      </style>
    </head>
    <body>
      <div class="container">
        <!-- Header -->
        <div class="header">
          <div class="company-info">
            <div class="company-name-ar">${settings.companyName}</div>
            <div class="company-name-en">${settings.companyNameEn}</div>
            <div class="company-address">${settings.companyAddress}</div>
          </div>
          
          <div class="logo-section">
            <div class="logo-text">
              LAREN<br>
              لارين
            </div>
          </div>
        </div>

        <!-- Document Details -->
        <div class="invoice-details">
          <div class="invoice-info">
            <div class="detail-row">
              <span class="detail-label">${type === 'return' ? 'رقم المرتجع:' : 'رقم الفاتورة:'}</span>
              <span class="detail-value">${documentNumber}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">التاريخ:</span>
              <span class="detail-value">${new Date(invoice.created_at).toLocaleDateString('ar-EG')}</span>
            </div>
            ${type !== 'return' ? `
            <div class="detail-row">
              <span class="detail-label">طريقة الدفع:</span>
              <span class="detail-value">${invoice.payment_method === 'cash' ? 'نقداً' : 'آجل'}</span>
            </div>` : ''}
            ${type === 'return' ? `
            <div class="detail-row">
              <span class="detail-label">سبب المرتجع:</span>
              <span class="detail-value">${invoice.reason || 'غير محدد'}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">الحالة:</span>
              <span class="detail-value">${invoice.status === 'approved' ? 'مقبول' : invoice.status === 'rejected' ? 'مرفوض' : 'قيد المراجعة'}</span>
            </div>` : ''}
            </div>
          </div>
          
          <div class="customer-info">
            <div class="detail-row">
              <span class="detail-label">${
                type === 'return'
                  ? (invoice.type === 'purchase' || invoice.return_type === 'purchase' ? 'المورد:' : 'العميل:')
                  : (type === 'sales' ? 'العميل:' : 'المورد:')
              }</span>
              <span class="detail-value">${customerSupplier}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">الهاتف:</span>
              <span class="detail-value">${
                type === 'return'
                  ? (invoice.type === 'purchase' || invoice.return_type === 'purchase'
                      ? (invoice.suppliers?.phone || invoice.supplier_phone || invoice.supplierPhone || '')
                      : (invoice.customers?.phone || invoice.customer_phone || invoice.customerPhone || ''))
                  : (type === 'sales'
                      ? (invoice.customers?.phone || invoice.customer_phone || invoice.customerPhone || '')
                      : (invoice.suppliers?.phone || invoice.supplier_phone || invoice.supplierPhone || ''))
              }</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">العنوان:</span>
              <span class="detail-value">${
                type === 'return'
                  ? (invoice.type === 'purchase' || invoice.return_type === 'purchase'
                      ? (invoice.suppliers?.address || invoice.supplier_address || invoice.supplierAddress || '')
                      : (invoice.customers?.address || invoice.customer_address || invoice.customerAddress || ''))
                  : (type === 'sales'
                      ? (invoice.customers?.address || invoice.customer_address || invoice.customerAddress || '')
                      : (invoice.suppliers?.address || invoice.supplier_address || invoice.supplierAddress || ''))
              }</span>
            </div>
          </div>
        </div>

        <!-- Items Table -->
        <table class="items-table">
          <thead>
            <tr>
              <th style="width: 40px;">ت</th>
              <th style="width: ${type === 'return' ? '150px' : '200px'};">اسم المادة</th>
              <th style="width: 60px;">الكمية</th>
              <th style="width: 80px;">السعر</th>
              <th style="width: 100px;">المجموع</th>
              ${type === 'return' ? '<th style="width: 100px;">سبب المرتجع</th>' : ''}
              <th style="width: 60px;">EXP</th>
              <th style="width: 60px;">B.N</th>
            </tr>
          </thead>
          <tbody>
            ${items?.map((item: any, index: number) => `
              <tr>
                <td>${index + 1}</td>
                <td class="item-name">${
                  type === 'return'
                    ? (item.medicine_name || item.medicineName || item.medicine_batches?.medicines?.name || item.medicines?.name || 'غير محدد')
                    : type === 'sales'
                      ? (item.medicine_name || item.medicineName || item.medicine_batches?.medicines?.name || 'غير محدد')
                      : (item.medicine_name || item.medicineName || item.medicines?.name || item.name || 'غير محدد')
                }</td>
                <td>${item.quantity}</td>
                <td>${
                  type === 'return'
                    ? (invoice.type === 'purchase' || invoice.return_type === 'purchase'
                        ? (item.unit_cost || item.unitCost || 0).toLocaleString()
                        : (item.unit_price || item.unitPrice || 0).toLocaleString())
                    : type === 'sales'
                      ? (item.unit_price || 0).toLocaleString()
                      : (item.unit_cost || item.unitCost || 0).toLocaleString()
                }</td>
                <td>${
                  type === 'return'
                    ? (invoice.type === 'purchase' || invoice.return_type === 'purchase'
                        ? (item.total_cost || item.totalCost || 0).toLocaleString()
                        : (item.total_price || item.totalPrice || 0).toLocaleString())
                    : type === 'sales'
                      ? (item.total_price || 0).toLocaleString()
                      : (item.total_cost || item.totalCost || 0).toLocaleString()
                }</td>
                ${type === 'return' ? `<td style="font-size: 10px;">${item.return_reason || invoice.reason || 'غير محدد'}</td>` : ''}
                <td>${
                  type === 'return'
                    ? (item.expiry_date || item.expiryDate || item.medicine_batches?.expiry_date
                        ? new Date(item.expiry_date || item.expiryDate || item.medicine_batches.expiry_date).toLocaleDateString('en-GB').replace(/\//g, '/')
                        : '')
                    : type === 'sales'
                      ? (item.medicine_batches?.expiry_date ? new Date(item.medicine_batches.expiry_date).toLocaleDateString('en-GB').replace(/\//g, '/') : '')
                      : (item.expiry_date || item.expiryDate ? new Date(item.expiry_date || item.expiryDate).toLocaleDateString('en-GB').replace(/\//g, '/') : '')
                }</td>
                <td>${
                  type === 'return'
                    ? (item.batch_code || item.batchCode || item.medicine_batches?.batch_number || '')
                    : type === 'sales'
                      ? (item.medicine_batches?.batch_number || '')
                      : (item.batch_code || item.batchCode || '')
                }</td>
              </tr>
            `).join('') || ''}
            
            <!-- Empty rows to fill space -->
            ${Array.from({ length: Math.max(0, 10 - (items?.length || 0)) }, (_, i) => `
              <tr>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                ${type === 'return' ? '<td>&nbsp;</td>' : ''}
                <td>&nbsp;</td>
                <td>&nbsp;</td>
              </tr>
            `).join('')}
          </tbody>
        </table>

        <!-- Totals -->
        <div class="totals-section">
          <table>
            ${type !== 'return' ? `
            <tr>
              <td class="total-label">المجموع الفرعي:</td>
              <td class="total-value">${invoice.total_amount?.toLocaleString() || 0}</td>
            </tr>
            <tr>
              <td class="total-label">الخصم:</td>
              <td class="total-value">${invoice.discount_amount?.toLocaleString() || 0}</td>
            </tr>
            <tr style="background-color: #f0f0f0;">
              <td class="total-label">المجموع النهائي:</td>
              <td class="total-value">${invoice.final_amount?.toLocaleString() || 0}</td>
            </tr>` : `
            <tr style="background-color: #f0f0f0;">
              <td class="total-label">إجمالي المرتجع:</td>
              <td class="total-value">${invoice.total_amount?.toLocaleString() || 0}</td>
            </tr>`}
          </table>
        </div>

        <!-- Notes -->
        <div class="notes-section">
          <div class="notes-label">ملاحظات: ${invoice.notes || ''}</div>
          ${type === 'return' && invoice.rejection_reason ? `
          <div class="notes-label" style="margin-top: 10px; color: #dc2626;">سبب الرفض: ${invoice.rejection_reason}</div>` : ''}
        </div>

        <!-- Footer -->
        <div class="footer">
          <div style="font-size: 12px;">
            صفحة 1 من 1
          </div>
          
          <div class="signature-section">
            <div class="signature-box">
              ختم وتوقيع<br>
              المسؤول
            </div>
          </div>
          
          <div style="font-size: 12px; text-align: left;">
            ${settings.footerText || 'شكراً لتعاملكم معنا'}
          </div>
        </div>
      </div>
    </body>
    </html>
  `
}

// دالة ترجمة أسماء الأعمدة
const translateColumnName = (columnName: string): string => {
  const translations: { [key: string]: string } = {
    'id': 'الرقم',
    'name': 'الاسم',
    'invoice_number': 'رقم الفاتورة',
    'customer_name': 'اسم العميل',
    'supplier_name': 'اسم المورد',
    'total_amount': 'المبلغ الإجمالي',
    'final_amount': 'المبلغ النهائي',
    'payment_status': 'حالة الدفع',
    'created_at': 'التاريخ',
    'phone': 'الهاتف',
    'address': 'العنوان',
    'category': 'الفئة',
    'quantity': 'الكمية',
    'expiry_date': 'تاريخ الانتهاء',
    'medicine_name': 'اسم الدواء',
    'batch_code': 'رقم الدفعة',
    'unit_price': 'سعر الوحدة',
    'discount': 'الخصم',
    'notes': 'ملاحظات'
  }
  return translations[columnName] || columnName
}

// دالة تنسيق قيم الخلايا
const formatCellValue = (value: any, columnName: string): string => {
  if (value === null || value === undefined) return 'غير محدد'

  // تنسيق التواريخ
  if (columnName.includes('date') || columnName.includes('created_at')) {
    try {
      return new Date(value).toLocaleDateString('ar-EG')
    } catch {
      return value.toString()
    }
  }

  // تنسيق المبالغ
  if (columnName.includes('amount') || columnName.includes('price') || columnName.includes('cost')) {
    const num = parseFloat(value)
    return isNaN(num) ? value.toString() : `${num.toLocaleString()} د.ع`
  }

  // تنسيق حالة الدفع
  if (columnName === 'payment_status') {
    return value === 'paid' ? 'مدفوع' : value === 'pending' ? 'معلق' : value.toString()
  }

  return value.toString()
}

export const generateLarenReportHTML = (reportData: any, reportType: string, title: string, settings: PrintSettings) => {
  return `
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${title}</title>
      <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
          font-family: 'Arial', sans-serif; 
          font-size: 12px;
          color: #000;
          background-color: #fff;
          line-height: 1.4;
          direction: rtl;
        }
        .container { 
          max-width: 210mm; 
          margin: 0 auto; 
          padding: 10mm;
          border: 2px solid #000;
        }
        
        .header {
          border-bottom: 2px solid #000;
          padding-bottom: 15px;
          margin-bottom: 20px;
          display: flex;
          align-items: center;
          justify-content: space-between;
        }
        
        .company-info {
          flex: 1;
          text-align: right;
        }
        
        .company-name-ar {
          font-size: 20px;
          font-weight: bold;
          margin-bottom: 5px;
        }
        
        .company-name-en {
          font-size: 16px;
          font-weight: bold;
          margin-bottom: 10px;
          direction: ltr;
          text-align: left;
        }
        
        .logo-section {
          width: 100px;
          height: 100px;
          border: 2px solid #000;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 20px;
          background-color: #f8f9fa;
        }
        
        .report-title {
          text-align: center;
          font-size: 18px;
          font-weight: bold;
          margin: 20px 0;
          padding: 10px;
          border: 1px solid #000;
          background-color: #f0f0f0;
        }
        
        .report-date {
          text-align: center;
          margin-bottom: 20px;
          font-size: 14px;
        }
        
        table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 20px;
          border: 2px solid #000;
        }
        
        th, td {
          border: 1px solid #000;
          padding: 4px 6px;
          text-align: right;
          font-size: 10px;
          vertical-align: top;
          word-wrap: break-word;
          max-width: 120px;
        }

        th {
          background-color: #f0f0f0;
          font-weight: bold;
          text-align: center;
          font-size: 9px;
        }

        tr:nth-child(even) {
          background-color: #f9f9f9;
        }

        .number-cell {
          text-align: center;
          font-weight: bold;
          width: 30px;
        }
        
        .summary-section {
          display: flex;
          justify-content: space-around;
          margin-bottom: 20px;
          border: 1px solid #000;
          padding: 15px;
        }
        
        .summary-item {
          text-align: center;
        }
        
        .summary-label {
          font-size: 12px;
          margin-bottom: 5px;
        }
        
        .summary-value {
          font-size: 16px;
          font-weight: bold;
        }
        
        @media print {
          body { margin: 0; }
          .container { 
            border: 2px solid #000;
            box-shadow: none;
            margin: 0;
            padding: 10mm;
          }
          @page {
            size: A4;
            margin: 0;
          }
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <div class="company-info">
            <div class="company-name-ar">${settings.companyName}</div>
            <div class="company-name-en">${settings.companyNameEn}</div>
            <div>${settings.companyAddress}</div>
          </div>
          
          <div class="logo-section">
            <div style="font-size: 14px; font-weight: bold; text-align: center;">
              LAREN<br>
              لارين
            </div>
          </div>
        </div>

        <div class="report-title">${title}</div>
        <div class="report-date">تاريخ الطباعة: ${new Date().toLocaleDateString('ar-EG')}</div>

        ${Array.isArray(reportData) && reportData.length > 0 ? `
          <div class="summary-section">
            <div class="summary-item">
              <div class="summary-label">عدد السجلات</div>
              <div class="summary-value">${reportData.length}</div>
            </div>
            ${reportType.includes('sales') || reportType.includes('purchases') ? `
              <div class="summary-item">
                <div class="summary-label">إجمالي المبلغ</div>
                <div class="summary-value">${reportData.reduce((sum: number, item: any) => sum + (item.final_amount || item.total_amount || 0), 0).toLocaleString()} د.ع</div>
              </div>
              <div class="summary-item">
                <div class="summary-label">متوسط المبلغ</div>
                <div class="summary-value">${Math.round(reportData.reduce((sum: number, item: any) => sum + (item.final_amount || item.total_amount || 0), 0) / reportData.length).toLocaleString()} د.ع</div>
              </div>
              <div class="summary-item">
                <div class="summary-label">الفواتير المدفوعة</div>
                <div class="summary-value">${reportData.filter((item: any) => item.payment_status === 'paid').length}</div>
              </div>
              <div class="summary-item">
                <div class="summary-label">الفواتير المعلقة</div>
                <div class="summary-value">${reportData.filter((item: any) => item.payment_status === 'pending').length}</div>
              </div>
            ` : ''}
            ${reportType === 'inventory' ? `
              <div class="summary-item">
                <div class="summary-label">إجمالي الكمية</div>
                <div class="summary-value">${reportData.reduce((sum: number, item: any) => sum + (item.quantity || 0), 0).toLocaleString()}</div>
              </div>
              <div class="summary-item">
                <div class="summary-label">الأدوية منتهية الصلاحية</div>
                <div class="summary-value">${reportData.filter((item: any) => new Date(item.expiry_date) < new Date()).length}</div>
              </div>
              <div class="summary-item">
                <div class="summary-label">الأدوية قليلة الكمية</div>
                <div class="summary-value">${reportData.filter((item: any) => (item.quantity || 0) < 10).length}</div>
              </div>
            ` : ''}
          </div>

          <table>
            <thead>
              <tr>
                <th style="width: 30px;">ت</th>
                ${reportType.includes('sales') ? `
                  <th>رقم الفاتورة</th>
                  <th>العميل</th>
                  <th>المبلغ النهائي</th>
                  <th>حالة الدفع</th>
                  <th>التاريخ</th>
                ` : ''}
                ${reportType.includes('purchases') ? `
                  <th>رقم الفاتورة</th>
                  <th>المورد</th>
                  <th>المبلغ النهائي</th>
                  <th>حالة الدفع</th>
                  <th>التاريخ</th>
                ` : ''}
                ${reportType === 'inventory' ? `
                  <th>اسم الدواء</th>
                  <th>الفئة</th>
                  <th>الكمية</th>
                  <th>تاريخ الانتهاء</th>
                  <th>الحالة</th>
                ` : ''}
                ${reportType === 'financial' ? `
                  <th>نوع العملية</th>
                  <th>المبلغ</th>
                  <th>الوصف</th>
                  <th>التاريخ</th>
                ` : ''}
                ${reportType === 'customers' ? `
                  <th>اسم العميل</th>
                  <th>الهاتف</th>
                  <th>العنوان</th>
                  <th>إجمالي المشتريات</th>
                ` : ''}
                ${reportType === 'suppliers' ? `
                  <th>اسم المورد</th>
                  <th>الهاتف</th>
                  <th>العنوان</th>
                  <th>إجمالي المشتريات</th>
                ` : ''}
                ${!reportType.includes('sales') && !reportType.includes('purchases') && reportType !== 'inventory' && reportType !== 'financial' && reportType !== 'customers' && reportType !== 'suppliers' ? `
                  ${Object.keys(reportData[0] || {}).slice(0, 6).map(key => `<th>${translateColumnName(key)}</th>`).join('')}
                ` : ''}
              </tr>
            </thead>
            <tbody>
              ${reportData.map((item: any, index: number) => `
                <tr>
                  <td class="number-cell">${index + 1}</td>
                  ${reportType.includes('sales') ? `
                    <td>${item.invoice_number}</td>
                    <td>${item.customers?.name || item.customer_name || 'عميل نقدي'}</td>
                    <td>${item.final_amount?.toLocaleString()} د.ع</td>
                    <td>${item.payment_status === 'paid' ? 'مدفوع' : 'معلق'}</td>
                    <td>${new Date(item.created_at).toLocaleDateString('ar-EG')}</td>
                  ` : ''}
                  ${reportType.includes('purchases') ? `
                    <td>${item.invoice_number}</td>
                    <td>${item.suppliers?.name || 'غير محدد'}</td>
                    <td>${item.final_amount?.toLocaleString()} د.ع</td>
                    <td>${item.payment_status === 'paid' ? 'مدفوع' : 'معلق'}</td>
                    <td>${new Date(item.created_at).toLocaleDateString('ar-EG')}</td>
                  ` : ''}
                  ${reportType === 'inventory' ? `
                    <td>${item.medicines?.name || item.medicine_name || 'غير محدد'}</td>
                    <td>${item.medicines?.category || item.category || 'غير محدد'}</td>
                    <td>${item.quantity || 0}</td>
                    <td>${item.expiry_date ? new Date(item.expiry_date).toLocaleDateString('ar-EG') : 'غير محدد'}</td>
                    <td>${(item.quantity || 0) < 10 ? 'كمية قليلة' : 'طبيعي'}</td>
                  ` : ''}
                  ${reportType === 'financial' ? `
                    <td>${item.type === 'income' ? 'دخل' : 'مصروف'}</td>
                    <td>${item.amount?.toLocaleString() || 0} د.ع</td>
                    <td>${item.description || 'غير محدد'}</td>
                    <td>${item.created_at ? new Date(item.created_at).toLocaleDateString('ar-EG') : 'غير محدد'}</td>
                  ` : ''}
                  ${reportType === 'customers' ? `
                    <td>${item.name || 'غير محدد'}</td>
                    <td>${item.phone || 'غير محدد'}</td>
                    <td>${item.address || 'غير محدد'}</td>
                    <td>${item.total_purchases?.toLocaleString() || 0} د.ع</td>
                  ` : ''}
                  ${reportType === 'suppliers' ? `
                    <td>${item.name || 'غير محدد'}</td>
                    <td>${item.phone || 'غير محدد'}</td>
                    <td>${item.address || 'غير محدد'}</td>
                    <td>${item.total_purchases?.toLocaleString() || 0} د.ع</td>
                  ` : ''}
                  ${!reportType.includes('sales') && !reportType.includes('purchases') && reportType !== 'inventory' && reportType !== 'financial' && reportType !== 'customers' && reportType !== 'suppliers' ? `
                    ${Object.keys(reportData[0] || {}).slice(0, 6).map(key => `
                      <td>${formatCellValue(item[key], key)}</td>
                    `).join('')}
                  ` : ''}
                </tr>
              `).join('')}
            </tbody>
          </table>
        ` : `
          <div style="text-align: center; padding: 50px; border: 1px solid #000;">
            لا توجد بيانات للعرض
          </div>
        `}
      </div>
    </body>
    </html>
  `
}

export const generateLarenReturnHTML = (returnRecord: any, settings: PrintSettings) => {
  const items = returnRecord.return_items || []
  const isSupplierReturn = returnRecord.type === 'purchase_return'
  const customerSupplier = isSupplierReturn
    ? (returnRecord.suppliers?.name || 'غير محدد')
    : (returnRecord.customers?.name || returnRecord.customer_name || 'عميل نقدي')

  return `
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>سند إرجاع - ${returnRecord.return_number}</title>
      <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
          font-family: 'Arial', sans-serif;
          font-size: 14px;
          color: #000;
          background-color: #fff;
          line-height: 1.4;
          direction: rtl;
        }
        .container {
          max-width: 210mm;
          margin: 0 auto;
          padding: 10mm;
          border: 2px solid #000;
          min-height: 297mm;
          position: relative;
        }

        .header {
          border-bottom: 2px solid #000;
          padding-bottom: 15px;
          margin-bottom: 20px;
          display: flex;
          align-items: center;
          justify-content: space-between;
        }

        .company-info {
          flex: 1;
          text-align: right;
        }

        .company-name-ar {
          font-size: 24px;
          font-weight: bold;
          margin-bottom: 5px;
        }

        .company-name-en {
          font-size: 18px;
          font-weight: bold;
          margin-bottom: 10px;
          direction: ltr;
          text-align: left;
        }

        .logo-section {
          width: 120px;
          height: 120px;
          border: 2px solid #000;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 20px;
          background-color: #f8f9fa;
        }

        .return-title {
          text-align: center;
          font-size: 20px;
          font-weight: bold;
          margin: 20px 0;
          padding: 10px;
          border: 2px solid #000;
          background-color: #f0f0f0;
        }

        .return-details {
          display: flex;
          justify-content: space-between;
          margin-bottom: 20px;
          border: 1px solid #000;
        }

        .return-info, .customer-info {
          flex: 1;
          padding: 10px;
          border-right: 1px solid #000;
        }

        .customer-info {
          border-right: none;
        }

        .detail-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;
          padding: 2px 0;
        }

        .detail-label {
          font-weight: bold;
          min-width: 100px;
        }

        .detail-value {
          text-align: left;
        }

        .items-table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 20px;
          border: 2px solid #000;
        }

        .items-table th,
        .items-table td {
          border: 1px solid #000;
          padding: 8px;
          text-align: center;
          font-size: 12px;
        }

        .items-table th {
          background-color: #f0f0f0;
          font-weight: bold;
        }

        .items-table .item-name {
          text-align: right;
          padding-right: 10px;
        }

        .totals-section {
          width: 300px;
          margin-left: auto;
          border: 2px solid #000;
          margin-bottom: 20px;
        }

        .totals-section table {
          width: 100%;
          border-collapse: collapse;
        }

        .totals-section td {
          border: 1px solid #000;
          padding: 8px;
          font-size: 14px;
        }

        .totals-section .total-label {
          background-color: #f0f0f0;
          font-weight: bold;
          text-align: right;
        }

        .totals-section .total-value {
          text-align: center;
          font-weight: bold;
        }

        .footer {
          position: absolute;
          bottom: 10mm;
          left: 10mm;
          right: 10mm;
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-top: 1px solid #000;
          padding-top: 15px;
        }

        .signature-section {
          text-align: center;
          flex: 1;
        }

        .signature-box {
          width: 150px;
          height: 80px;
          border: 2px solid #000;
          border-radius: 50%;
          margin: 0 auto 10px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          color: #666;
        }

        .notes-section {
          margin-bottom: 30px;
          border: 1px solid #000;
          padding: 10px;
          min-height: 60px;
        }

        @media print {
          body { margin: 0; }
          .container {
            border: 2px solid #000;
            box-shadow: none;
            margin: 0;
            padding: 10mm;
          }
          @page {
            size: A4;
            margin: 0;
          }
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <div class="company-info">
            <div class="company-name-ar">${settings.companyName}</div>
            <div class="company-name-en">${settings.companyNameEn}</div>
            <div class="company-address">${settings.companyAddress}</div>
          </div>

          <div class="logo-section">
            <div style="font-size: 16px; font-weight: bold; text-align: center; line-height: 1.2;">
              LAREN<br>
              لارين
            </div>
          </div>
        </div>

        <div class="return-title">
          سند إرجاع ${isSupplierReturn ? 'مشتريات' : 'مبيعات'}
        </div>

        <div class="return-details">
          <div class="return-info">
            <div class="detail-row">
              <span class="detail-label">رقم سند الإرجاع:</span>
              <span class="detail-value">${returnRecord.return_number}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">التاريخ:</span>
              <span class="detail-value">${new Date(returnRecord.created_at).toLocaleDateString('ar-EG')}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">رقم الفاتورة الأصلية:</span>
              <span class="detail-value">${returnRecord.original_invoice_number || 'غير محدد'}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">نوع الإرجاع:</span>
              <span class="detail-value">${isSupplierReturn ? 'إرجاع للمورد' : 'إرجاع من العميل'}</span>
            </div>
          </div>

          <div class="customer-info">
            <div class="detail-row">
              <span class="detail-label">${isSupplierReturn ? 'المورد:' : 'العميل:'}</span>
              <span class="detail-value">${customerSupplier}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">الهاتف:</span>
              <span class="detail-value">${isSupplierReturn ? (returnRecord.suppliers?.phone || '') : (returnRecord.customers?.phone || '')}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">العنوان:</span>
              <span class="detail-value">${isSupplierReturn ? (returnRecord.suppliers?.address || '') : (returnRecord.customers?.address || '')}</span>
            </div>
          </div>
        </div>

        <table class="items-table">
          <thead>
            <tr>
              <th style="width: 40px;">ت</th>
              <th style="width: 200px;">اسم المادة</th>
              <th style="width: 60px;">الكمية</th>
              <th style="width: 80px;">السعر</th>
              <th style="width: 100px;">المجموع</th>
              <th style="width: 100px;">سبب الإرجاع</th>
              <th style="width: 60px;">EXP</th>
            </tr>
          </thead>
          <tbody>
            ${items?.map((item: any, index: number) => `
              <tr>
                <td>${index + 1}</td>
                <td class="item-name">${item.medicine_name || item.medicines?.name || item.medicine?.name || 'غير محدد'}</td>
                <td>${item.quantity}</td>
                <td>${(item.unit_price || 0).toLocaleString()}</td>
                <td>${(item.total_amount || 0).toLocaleString()}</td>
                <td style="font-size: 10px;">${item.return_reason || 'غير محدد'}</td>
                <td>${item.expiry_date ? new Date(item.expiry_date).toLocaleDateString('en-GB').replace(/\//g, '/') : ''}</td>
              </tr>
            `).join('') || ''}

            ${Array.from({ length: Math.max(0, 8 - (items?.length || 0)) }, (_, i) => `
              <tr>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
              </tr>
            `).join('')}
          </tbody>
        </table>

        <div class="totals-section">
          <table>
            <tr>
              <td class="total-label">إجمالي المبلغ المرتجع:</td>
              <td class="total-value">${returnRecord.total_amount?.toLocaleString() || 0} د.ع</td>
            </tr>
          </table>
        </div>

        <div class="notes-section">
          <div style="font-weight: bold; margin-bottom: 5px;">ملاحظات:</div>
          <div>${returnRecord.notes || ''}</div>
        </div>

        <div class="footer">
          <div style="font-size: 12px;">
            صفحة 1 من 1
          </div>

          <div class="signature-section">
            <div class="signature-box">
              ختم وتوقيع<br>
              المسؤول
            </div>
          </div>

          <div style="font-size: 12px; text-align: left;">
            ${settings.footerText || 'شكراً لتعاملكم معنا'}
          </div>
        </div>
      </div>
    </body>
    </html>
  `
}
