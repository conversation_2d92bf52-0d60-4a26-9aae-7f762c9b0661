<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مسح البيانات المحلية</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .button {
            background: #dc3545;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            width: 100%;
        }
        .button:hover {
            background: #c82333;
        }
        .success {
            background: #28a745;
            color: white;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            display: none;
        }
        .info {
            background: #17a2b8;
            color: white;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .back-button {
            background: #6c757d;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-top: 20px;
        }
        .back-button:hover {
            background: #5a6268;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗑️ مسح البيانات المحلية</h1>
        
        <div class="info">
            <strong>تنبيه:</strong> هذه الأداة ستقوم بمسح جميع البيانات المحفوظة محلياً في المتصفح. استخدمها إذا كنت تواجه مشاكل في عرض أسماء الأدوية أو حفظ الفواتير.
        </div>
        
        <button class="button" onclick="clearAllData()">
            🗑️ مسح جميع البيانات المحلية
        </button>
        
        <button class="button" onclick="clearInvoicesOnly()">
            📄 مسح الفواتير فقط
        </button>
        
        <button class="button" onclick="showStoredData()">
            👁️ عرض البيانات المحفوظة
        </button>
        
        <div id="success" class="success">
            ✅ تم مسح البيانات بنجاح! يرجى إعادة تحميل الصفحة.
        </div>
        
        <div id="dataDisplay" style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px; display: none;">
            <h3>البيانات المحفوظة:</h3>
            <div id="dataContent"></div>
        </div>
        
        <a href="/sales" class="back-button">← العودة إلى المبيعات</a>
    </div>

    <script>
        function clearAllData() {
            const keys = [
                'sales_invoices',
                'sales_invoice_items',
                'purchase_invoices',
                'purchase_invoice_items',
                'medicines',
                'medicine_batches',
                'customers',
                'suppliers',
                'cash_transactions',
                'inventory_movements'
            ];
            
            keys.forEach(key => {
                localStorage.removeItem(key);
            });
            
            showSuccess();
        }
        
        function clearInvoicesOnly() {
            const keys = [
                'sales_invoices',
                'sales_invoice_items',
                'purchase_invoices',
                'purchase_invoice_items'
            ];
            
            keys.forEach(key => {
                localStorage.removeItem(key);
            });
            
            showSuccess();
        }
        
        function showSuccess() {
            document.getElementById('success').style.display = 'block';
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        }
        
        function showStoredData() {
            const dataDisplay = document.getElementById('dataDisplay');
            const dataContent = document.getElementById('dataContent');
            
            let html = '';
            
            const keys = [
                'sales_invoices',
                'sales_invoice_items',
                'medicines',
                'medicine_batches',
                'customers'
            ];
            
            keys.forEach(key => {
                const data = localStorage.getItem(key);
                if (data) {
                    try {
                        const parsed = JSON.parse(data);
                        html += `<p><strong>${key}:</strong> ${parsed.length} عنصر</p>`;
                    } catch (e) {
                        html += `<p><strong>${key}:</strong> بيانات غير صالحة</p>`;
                    }
                } else {
                    html += `<p><strong>${key}:</strong> لا توجد بيانات</p>`;
                }
            });
            
            dataContent.innerHTML = html;
            dataDisplay.style.display = 'block';
        }
    </script>
</body>
</html>
