'use client'

import { useState, useEffect } from 'react'
import AppLayout from '@/components/AppLayout'
import {
  Search,
  Filter,
  Eye,
  Check,
  X,
  Calendar,
  DollarSign,
  User,
  FileText,
  MoreVertical,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  Printer,
  Download,
  FileSpreadsheet,
  Building2,
  Package,
  ShoppingCart,
  RotateCcw
} from 'lucide-react'
import { getReturns, updateReturn, getReturnById } from '@/lib/database'

interface ReturnRecord {
  id: string
  return_number: string
  customer_name?: string
  supplier_name?: string
  return_type: 'sales' | 'purchase'
  total_amount: number
  status: 'pending' | 'approved' | 'rejected'
  reason: string
  created_at: string
  customers?: {
    name: string
    phone: string
    address: string
  }
  suppliers?: {
    name: string
    phone: string
    address: string
  }
  return_items?: any[]
}

export default function ReturnsRecordsPage() {
  const [returnRecords, setReturnRecords] = useState<ReturnRecord[]>([])
  const [filteredRecords, setFilteredRecords] = useState<ReturnRecord[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState<'all' | 'sales' | 'purchase'>('all')
  const [filterStatus, setFilterStatus] = useState<'all' | 'pending' | 'approved' | 'rejected'>('all')
  const [selectedRecord, setSelectedRecord] = useState<ReturnRecord | null>(null)
  const [showDetailsModal, setShowDetailsModal] = useState(false)
  const [loading, setLoading] = useState(true)
  const [showActionsMenu, setShowActionsMenu] = useState<string | null>(null)
  const [processingAction, setProcessingAction] = useState<string | null>(null)

  useEffect(() => {
    loadReturnRecords()
  }, [])

  useEffect(() => {
    filterRecords()
  }, [returnRecords, searchTerm, filterType, filterStatus])

  // إنشاء بيانات تجريبية للمرتجعات
  const createSampleReturns = (force = false) => {
    // مسح البيانات القديمة إذا كان force = true
    if (force) {
      localStorage.removeItem('sales_returns')
      localStorage.removeItem('purchase_returns')
      localStorage.removeItem('customers')
      localStorage.removeItem('suppliers')
    }
    const sampleSalesReturns = [
      {
        id: 'sr-001',
        return_number: 'SR-2024-001',
        customer_id: 'cust-001',
        customer_name: 'أحمد محمد علي',
        total_amount: 150000,
        status: 'pending',
        reason: 'دواء منتهي الصلاحية',
        created_at: new Date().toISOString(),
        return_items: [
          {
            id: 'sri-001',
            medicine_name: 'باراسيتامول 500 مجم',
            quantity: 2,
            unit_price: 25000,
            total_price: 50000,
            reason: 'منتهي الصلاحية',
            expiry_date: '2024-01-15'
          },
          {
            id: 'sri-002',
            medicine_name: 'أموكسيسيلين 250 مجم',
            quantity: 4,
            unit_price: 25000,
            total_price: 100000,
            reason: 'عيب في التصنيع',
            expiry_date: '2024-06-20'
          }
        ]
      },
      {
        id: 'sr-002',
        return_number: 'SR-2024-002',
        customer_id: 'cust-002',
        customer_name: 'فاطمة أحمد حسن',
        total_amount: 75000,
        status: 'approved',
        reason: 'رد فعل تحسسي',
        created_at: new Date(Date.now() - 86400000).toISOString(),
        return_items: [
          {
            id: 'sri-003',
            medicine_name: 'إيبوبروفين 400 مجم',
            quantity: 3,
            unit_price: 25000,
            total_price: 75000,
            reason: 'رد فعل تحسسي',
            expiry_date: '2024-12-30'
          }
        ]
      }
    ]

    const samplePurchaseReturns = [
      {
        id: 'pr-001',
        return_number: 'PR-2024-001',
        supplier_id: 'supp-001',
        supplier_name: 'شركة الأدوية المتحدة',
        total_amount: 500000,
        status: 'pending',
        reason: 'شحنة تالفة',
        created_at: new Date(Date.now() - 172800000).toISOString(),
        return_items: [
          {
            id: 'pri-001',
            medicine_name: 'أسبرين 100 مجم',
            quantity: 10,
            unit_cost: 20000,
            total_cost: 200000,
            reason: 'عبوات مكسورة',
            expiry_date: '2025-03-15'
          },
          {
            id: 'pri-002',
            medicine_name: 'فيتامين د 1000 وحدة',
            quantity: 15,
            unit_cost: 20000,
            total_cost: 300000,
            reason: 'تاريخ انتهاء قريب',
            expiry_date: '2024-02-28'
          }
        ]
      }
    ]

    const sampleCustomers = [
      {
        id: 'cust-001',
        name: 'أحمد محمد علي',
        phone: '07901234567',
        address: 'بغداد - الكرادة'
      },
      {
        id: 'cust-002',
        name: 'فاطمة أحمد حسن',
        phone: '07907654321',
        address: 'بغداد - المنصور'
      }
    ]

    const sampleSuppliers = [
      {
        id: 'supp-001',
        name: 'شركة الأدوية المتحدة',
        phone: '07801234567',
        address: 'بغداد - المنطقة الصناعية'
      }
    ]

    // حفظ البيانات في localStorage
    localStorage.setItem('sales_returns', JSON.stringify(sampleSalesReturns))
    localStorage.setItem('purchase_returns', JSON.stringify(samplePurchaseReturns))
    localStorage.setItem('customers', JSON.stringify(sampleCustomers))
    localStorage.setItem('suppliers', JSON.stringify(sampleSuppliers))

    console.log('Sample returns data created:', {
      salesReturns: sampleSalesReturns.length,
      purchaseReturns: samplePurchaseReturns.length,
      salesItems: sampleSalesReturns.reduce((total, ret) => total + (ret.return_items?.length || 0), 0),
      purchaseItems: samplePurchaseReturns.reduce((total, ret) => total + (ret.return_items?.length || 0), 0)
    })
  }

  const loadReturnRecords = async () => {
    try {
      setLoading(true)

      // إنشاء بيانات تجريبية إذا لم تكن موجودة
      const existingReturns = localStorage.getItem('sales_returns')
      if (!existingReturns || JSON.parse(existingReturns).length === 0) {
        createSampleReturns()
      }

      const result = await getReturns()
      if (result.success && result.data) {
        console.log('Loaded return records:', result.data)
        // تحقق من وجود المواد في كل مرتجع
        result.data.forEach((record: any, index: number) => {
          console.log(`Return ${index + 1} (${record.return_number}):`, {
            id: record.id,
            return_items: record.return_items,
            items_count: record.return_items?.length || 0
          })
        })
        setReturnRecords(result.data)
      }
    } catch (error) {
      console.error('Error loading return records:', error)
    } finally {
      setLoading(false)
    }
  }

  const filterRecords = () => {
    let filtered = returnRecords

    // البحث
    if (searchTerm) {
      filtered = filtered.filter(record =>
        record.return_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
        record.customer_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        record.supplier_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        record.customers?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        record.suppliers?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        record.reason?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // فلترة النوع
    if (filterType !== 'all') {
      filtered = filtered.filter(record => record.return_type === filterType)
    }

    // فلترة الحالة
    if (filterStatus !== 'all') {
      filtered = filtered.filter(record => record.status === filterStatus)
    }

    setFilteredRecords(filtered)
  }

  const handleViewDetails = async (record: ReturnRecord) => {
    try {
      // جلب التفاصيل الكاملة للمرتجع
      let fullRecord = record
      const result = await getReturnById(record.id)
      if (result.success && result.data) {
        fullRecord = result.data
        console.log('Full record for details:', fullRecord)
      }

      setSelectedRecord(fullRecord)
      setShowDetailsModal(true)
      setShowActionsMenu(null)
    } catch (error) {
      console.error('Error loading return details:', error)
      setSelectedRecord(record)
      setShowDetailsModal(true)
      setShowActionsMenu(null)
    }
  }

  const handleApproveReturn = async (record: ReturnRecord) => {
    try {
      setProcessingAction(record.id)
      const result = await updateReturn(record.id, { status: 'approved' })
      if (result.success) {
        await loadReturnRecords()
        alert('تم قبول المرتجع بنجاح')
      } else {
        alert('حدث خطأ أثناء قبول المرتجع')
      }
    } catch (error) {
      console.error('Error approving return:', error)
      alert('حدث خطأ أثناء قبول المرتجع')
    } finally {
      setProcessingAction(null)
      setShowActionsMenu(null)
    }
  }

  const handleRejectReturn = async (record: ReturnRecord) => {
    const reason = prompt('يرجى إدخال سبب رفض المرتجع:')
    if (!reason) return

    try {
      setProcessingAction(record.id)
      const result = await updateReturn(record.id, { 
        status: 'rejected',
        rejection_reason: reason
      })
      if (result.success) {
        await loadReturnRecords()
        alert('تم رفض المرتجع')
      } else {
        alert('حدث خطأ أثناء رفض المرتجع')
      }
    } catch (error) {
      console.error('Error rejecting return:', error)
      alert('حدث خطأ أثناء رفض المرتجع')
    } finally {
      setProcessingAction(null)
      setShowActionsMenu(null)
    }
  }

  // طباعة مرتجع واحد
  const handlePrintSingleReturn = async (record: ReturnRecord) => {
    console.log('Printing return record:', record) // للتشخيص

    // جلب التفاصيل الكاملة للمرتجع
    let fullRecord = record
    try {
      const result = await getReturnById(record.id)
      if (result.success && result.data) {
        fullRecord = result.data
        console.log('Full return record with items:', fullRecord)
      }
    } catch (error) {
      console.error('Error fetching full return details:', error)
    }

    // استخدام قالب لارين الجديد
    const { generateLarenReturnHTML } = require('@/utils/larenPrintTemplate')
    const { usePrintSettings } = require('@/hooks/usePrintSettings')

    // إعدادات الطباعة الافتراضية
    const defaultSettings = {
      companyName: 'مكتب لارين العلمي',
      companyNameEn: 'LAREN SCIENTIFIC BUREAU',
      companyAddress: 'بغداد - شارع فلسطين',
      companyPhone: '+964 ************',
      companyEmail: '<EMAIL>',
      footerText: 'شكراً لتعاملكم معنا'
    }

    const printWindow = window.open('', '_blank')
    if (!printWindow) return

    const printContent = generateLarenReturnHTML(fullRecord, defaultSettings)

    printWindow.document.write(printContent)
    printWindow.document.close()
    printWindow.focus()

    setTimeout(() => {
      printWindow.print()
    }, 500)
  }

  // دوال الطباعة والتصدير
  const handlePrintSingleReturnWithLaren = async (record: ReturnRecord) => {
    console.log('Printing return record with Laren template:', record)

    // جلب التفاصيل الكاملة للمرتجع
    let fullRecord = record
    try {
      const result = await getReturnById(record.id)
      if (result.success && result.data) {
        fullRecord = result.data
        console.log('Full return record with items:', fullRecord)
      }
    } catch (error) {
      console.error('Error fetching full return details:', error)
    }

    // استخدام قالب لارين الجديد
    const { generateLarenReturnHTML } = require('@/utils/larenPrintTemplate')

    // إعدادات الطباعة الافتراضية
    const defaultSettings = {
      companyName: 'مكتب لارين العلمي',
      companyNameEn: 'LAREN SCIENTIFIC BUREAU',
      companyAddress: 'بغداد - شارع فلسطين',
      companyPhone: '+964 ************',
      companyEmail: '<EMAIL>',
      footerText: 'شكراً لتعاملكم معنا'
    }

    const printWindow = window.open('', '_blank')
    if (!printWindow) return

    const printContent = generateLarenReturnHTML(fullRecord, defaultSettings)

    printWindow.document.write(printContent)
    printWindow.document.close()
    printWindow.focus()

    setTimeout(() => {
      printWindow.print()
    }, 500)
  }

  const handlePrintReturns = () => {
    const printWindow = window.open('', '_blank')
    if (!printWindow) return

    const createReturnCard = (record: ReturnRecord) => {
      const statusColor = record.status === 'approved' ? '#10B981' :
                         record.status === 'rejected' ? '#EF4444' : '#F59E0B'
      const statusText = record.status === 'approved' ? 'مقبول' :
                        record.status === 'rejected' ? 'مرفوض' : 'في الانتظار'
      const typeText = record.return_type === 'sales' ? 'مرتجع مبيعات' : 'مرتجع مشتريات'
      const customerSupplier = record.return_type === 'sales'
        ? (record.customers?.name || record.customer_name || 'عميل غير محدد')
        : (record.suppliers?.name || record.supplier_name || 'مورد غير محدد')

      const items = record.return_items || []
      const itemsHtml = items.length > 0 ? `
        <div class="items-section">
          <h4>المواد المرتجعة (${items.length} مادة):</h4>
          <div class="items-grid">
            ${items.map((item: any, index: number) => `
              <div class="item-card">
                <div class="item-name">
                  <span class="item-number">${index + 1}.</span>
                  ${item.medicine_name || item.medicines?.name || item.medicine?.name || `دواء ${index + 1}`}
                </div>
                <div class="item-details">
                  <span>📦 الكمية: ${item.quantity || 0}</span>
                  <span>💰 السعر: ${(item.unit_price || item.unit_cost || item.price || 0).toLocaleString()} د.ع</span>
                  <span>💵 المجموع: ${(item.total_price || item.total_cost || item.total || (item.quantity || 0) * (item.unit_price || item.unit_cost || item.price || 0)).toLocaleString()} د.ع</span>
                  ${item.expiry_date ? `<span>📅 انتهاء: ${new Date(item.expiry_date).toLocaleDateString('ar-EG')}</span>` : ''}
                  ${item.reason || item.return_reason ? `<span>📝 السبب: ${item.reason || item.return_reason}</span>` : ''}
                </div>
              </div>
            `).join('')}
          </div>
          <div class="items-total">
            <strong>المجموع الكلي: ${record.total_amount?.toLocaleString() || 0} د.ع</strong>
          </div>
        </div>
      ` : `
        <div class="no-items">
          <div class="no-items-icon">📦</div>
          <div>لا توجد مواد مرتجعة</div>
        </div>
      `

      return `
        <div class="return-card">
          <div class="return-header">
            <div class="return-number">
              <h3>${record.return_number}</h3>
              <span class="return-type">${typeText}</span>
            </div>
            <div class="return-status" style="background: ${statusColor};">
              ${statusText}
            </div>
          </div>

          <div class="return-info">
            <div class="info-row">
              <div class="info-item">
                <label>التاريخ:</label>
                <span>${new Date(record.created_at).toLocaleDateString('ar-EG')}</span>
              </div>
              <div class="info-item">
                <label>${record.return_type === 'sales' ? 'العميل' : 'المورد'}:</label>
                <span>${customerSupplier}</span>
              </div>
              <div class="info-item">
                <label>المبلغ الإجمالي:</label>
                <span>${record.total_amount.toLocaleString()} د.ع</span>
              </div>
            </div>

            ${record.return_type === 'sales' && record.customers ? `
              <div class="contact-info">
                <div class="info-item">
                  <label>الهاتف:</label>
                  <span>${record.customers.phone || 'غير محدد'}</span>
                </div>
                <div class="info-item">
                  <label>العنوان:</label>
                  <span>${record.customers.address || 'غير محدد'}</span>
                </div>
              </div>
            ` : ''}

            ${record.return_type === 'purchase' && record.suppliers ? `
              <div class="contact-info">
                <div class="info-item">
                  <label>الهاتف:</label>
                  <span>${record.suppliers.phone || 'غير محدد'}</span>
                </div>
                <div class="info-item">
                  <label>العنوان:</label>
                  <span>${record.suppliers.address || 'غير محدد'}</span>
                </div>
              </div>
            ` : ''}

            <div class="reason-section">
              <label>سبب المرتجع:</label>
              <div class="reason-text">${record.reason || 'غير محدد'}</div>
            </div>

            ${itemsHtml}
          </div>
        </div>
      `
    }

    const printContent = `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>تقرير المرتجعات</title>
        <style>
          @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

          * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
          }

          body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #2D3748;
            background: #FFFFFF;
            direction: rtl;
            padding: 20px;
          }

          .print-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
          }

          .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
            border-radius: 12px 12px 0 0;
            margin-bottom: 30px;
          }

          .header h1 {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
          }

          .header .subtitle {
            font-size: 16px;
            opacity: 0.9;
          }

          .summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
          }

          .summary-card {
            background: #F7FAFC;
            border: 1px solid #E2E8F0;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            border-right: 4px solid #667eea;
          }

          .summary-card h3 {
            font-size: 14px;
            font-weight: 600;
            color: #4A5568;
            margin-bottom: 8px;
          }

          .summary-card p {
            font-size: 24px;
            font-weight: 700;
            color: #667eea;
          }

          .returns-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
          }

          .return-card {
            background: white;
            border: 1px solid #E2E8F0;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
            break-inside: avoid;
            margin-bottom: 20px;
          }

          .return-header {
            background: linear-gradient(135deg, #F7FAFC 0%, #EDF2F7 100%);
            padding: 15px 20px;
            border-bottom: 1px solid #E2E8F0;
            display: flex;
            justify-content: space-between;
            align-items: center;
          }

          .return-number h3 {
            font-size: 18px;
            font-weight: 700;
            color: #2D3748;
            margin-bottom: 5px;
          }

          .return-type {
            font-size: 12px;
            color: #718096;
            background: #EDF2F7;
            padding: 4px 8px;
            border-radius: 6px;
          }

          .return-status {
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
          }

          .return-info {
            padding: 20px;
          }

          .info-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
          }

          .contact-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
            padding: 15px;
            background: #F7FAFC;
            border-radius: 8px;
          }

          .info-item {
            display: flex;
            flex-direction: column;
          }

          .info-item label {
            font-size: 12px;
            font-weight: 600;
            color: #4A5568;
            margin-bottom: 4px;
          }

          .info-item span {
            font-size: 14px;
            color: #2D3748;
          }

          .reason-section {
            margin-bottom: 15px;
          }

          .reason-section label {
            font-size: 12px;
            font-weight: 600;
            color: #4A5568;
            margin-bottom: 8px;
            display: block;
          }

          .reason-text {
            background: #FFF5F5;
            border: 1px solid #FED7D7;
            border-radius: 8px;
            padding: 12px;
            font-size: 14px;
            color: #2D3748;
          }

          .items-section h4 {
            font-size: 14px;
            font-weight: 600;
            color: #4A5568;
            margin-bottom: 12px;
          }

          .items-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
          }

          .item-card {
            background: #F0FFF4;
            border: 1px solid #C6F6D5;
            border-radius: 8px;
            padding: 12px;
          }

          .item-name {
            font-weight: 600;
            color: #2D3748;
            margin-bottom: 8px;
            font-size: 13px;
          }

          .item-details {
            display: flex;
            flex-direction: column;
            gap: 4px;
          }

          .item-details span {
            font-size: 11px;
            color: #4A5568;
            display: block;
            margin-bottom: 2px;
          }

          .item-number {
            background: #667eea;
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
            margin-left: 5px;
          }

          .items-total {
            margin-top: 15px;
            padding: 10px;
            background: #EDF2F7;
            border-radius: 6px;
            text-align: center;
            font-size: 14px;
            color: #2D3748;
          }

          .no-items {
            text-align: center;
            color: #718096;
            padding: 20px;
            background: #F7FAFC;
            border-radius: 8px;
            border: 2px dashed #CBD5E0;
          }

          .no-items-icon {
            font-size: 24px;
            margin-bottom: 8px;
          }

          .footer {
            background: #F7FAFC;
            padding: 20px;
            text-align: center;
            border-top: 1px solid #E2E8F0;
            color: #718096;
            font-size: 12px;
            margin-top: 30px;
          }

          @media print {
            body {
              margin: 0;
              padding: 10px;
            }

            .returns-grid {
              grid-template-columns: 1fr;
            }

            .return-card {
              break-inside: avoid;
              margin-bottom: 15px;
            }

            .summary {
              grid-template-columns: repeat(4, 1fr);
            }
          }

          @page {
            margin: 1cm;
            size: A4;
          }
        </style>
      </head>
      <body>
        <div class="print-container">
          <div class="header">
            <h1>تقرير المرتجعات الشامل</h1>
            <div class="subtitle">نظام إدارة الصيدلية الاحترافي</div>
          </div>

          <div class="summary">
            <div class="summary-card">
              <h3>إجمالي المرتجعات</h3>
              <p>${filteredRecords.length}</p>
            </div>
            <div class="summary-card">
              <h3>في الانتظار</h3>
              <p>${filteredRecords.filter(r => r.status === 'pending').length}</p>
            </div>
            <div class="summary-card">
              <h3>مقبولة</h3>
              <p>${filteredRecords.filter(r => r.status === 'approved').length}</p>
            </div>
            <div class="summary-card">
              <h3>مرفوضة</h3>
              <p>${filteredRecords.filter(r => r.status === 'rejected').length}</p>
            </div>
          </div>

          <div class="returns-grid">
            ${filteredRecords.map(record => createReturnCard(record)).join('')}
          </div>

          <div class="footer">
            <div>تم إنشاء هذا التقرير في: ${new Date().toLocaleString('ar-EG')}</div>
            <div>نظام إدارة الصيدلية - تقرير المرتجعات</div>
          </div>
        </div>
      </body>
      </html>
    `

    printWindow.document.write(printContent)
    printWindow.document.close()
    printWindow.focus()

    setTimeout(() => {
      printWindow.print()
    }, 500)
  }

  // تصدير Excel
  const handleExportExcel = async () => {
    try {
      const XLSX = await import('xlsx')
      const workbook = XLSX.utils.book_new()

      // ورقة الملخص
      const summaryData = [
        ['تقرير المرتجعات الشامل'],
        ['نظام إدارة الصيدلية'],
        [''],
        ['تاريخ التقرير:', new Date().toLocaleDateString('ar-EG')],
        ['وقت التقرير:', new Date().toLocaleTimeString('ar-EG')],
        [''],
        ['الملخص الإحصائي'],
        ['المؤشر', 'العدد'],
        ['إجمالي المرتجعات', filteredRecords.length],
        ['في الانتظار', filteredRecords.filter(r => r.status === 'pending').length],
        ['مقبولة', filteredRecords.filter(r => r.status === 'approved').length],
        ['مرفوضة', filteredRecords.filter(r => r.status === 'rejected').length]
      ]

      const summaryWorksheet = XLSX.utils.aoa_to_sheet(summaryData)
      XLSX.utils.book_append_sheet(workbook, summaryWorksheet, 'الملخص')

      // ورقة البيانات التفصيلية
      const detailsData = filteredRecords.map(record => ({
        'رقم المرتجع': record.return_number,
        'النوع': record.return_type === 'sales' ? 'مرتجع مبيعات' : 'مرتجع مشتريات',
        'العميل/المورد': record.return_type === 'sales'
          ? (record.customers?.name || record.customer_name || 'غير محدد')
          : (record.suppliers?.name || record.supplier_name || 'غير محدد'),
        'المبلغ الإجمالي': record.total_amount,
        'الحالة': record.status === 'approved' ? 'مقبول' :
                  record.status === 'rejected' ? 'مرفوض' : 'في الانتظار',
        'سبب المرتجع': record.reason || 'غير محدد',
        'التاريخ': new Date(record.created_at).toLocaleDateString('ar-EG'),
        'الهاتف': record.return_type === 'sales'
          ? (record.customers?.phone || 'غير محدد')
          : (record.suppliers?.phone || 'غير محدد'),
        'العنوان': record.return_type === 'sales'
          ? (record.customers?.address || 'غير محدد')
          : (record.suppliers?.address || 'غير محدد')
      }))

      const detailsWorksheet = XLSX.utils.json_to_sheet(detailsData)
      XLSX.utils.book_append_sheet(workbook, detailsWorksheet, 'البيانات التفصيلية')

      // ورقة المواد المرتجعة
      const itemsData: any[] = []
      filteredRecords.forEach(record => {
        if (record.return_items && record.return_items.length > 0) {
          record.return_items.forEach((item: any) => {
            itemsData.push({
              'رقم المرتجع': record.return_number,
              'اسم الدواء': item.medicine_name || item.medicines?.name || 'غير محدد',
              'الكمية': item.quantity || 0,
              'سعر الوحدة': item.unit_price || item.unit_cost || 0,
              'المجموع': item.total_price || item.total_cost || 0,
              'تاريخ الانتهاء': item.expiry_date ? new Date(item.expiry_date).toLocaleDateString('ar-EG') : 'غير محدد'
            })
          })
        }
      })

      if (itemsData.length > 0) {
        const itemsWorksheet = XLSX.utils.json_to_sheet(itemsData)
        XLSX.utils.book_append_sheet(workbook, itemsWorksheet, 'المواد المرتجعة')
      }

      const fileName = `تقرير_المرتجعات_${new Date().toISOString().split('T')[0]}.xlsx`
      XLSX.writeFile(workbook, fileName)
    } catch (error) {
      console.error('Error exporting Excel:', error)
      alert('حدث خطأ أثناء تصدير Excel')
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'bg-green-100 text-green-800'
      case 'rejected': return 'bg-red-100 text-red-800'
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'approved': return 'مقبول'
      case 'rejected': return 'مرفوض'
      case 'pending': return 'في الانتظار'
      default: return 'غير محدد'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved': return <CheckCircle className="h-4 w-4" />
      case 'rejected': return <XCircle className="h-4 w-4" />
      case 'pending': return <Clock className="h-4 w-4" />
      default: return <AlertTriangle className="h-4 w-4" />
    }
  }

  const getTypeText = (type: string) => {
    switch (type) {
      case 'sales': return 'مرتجع مبيعات'
      case 'purchase': return 'مرتجع مشتريات'
      default: return 'غير محدد'
    }
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
              <RotateCcw className="h-8 w-8 text-blue-600" />
              سجل المرتجعات
            </h1>
            <p className="text-gray-600 mt-1">عرض وإدارة جميع المرتجعات مع إمكانيات طباعة وتصدير متقدمة</p>
          </div>
          <div className="flex items-center gap-3">
            <button
              onClick={handlePrintReturns}
              className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center gap-2 transition-colors"
            >
              <Printer className="h-4 w-4" />
              طباعة التقرير
            </button>
            <button
              onClick={handleExportExcel}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2 transition-colors"
            >
              <FileSpreadsheet className="h-4 w-4" />
              تصدير Excel
            </button>
            <button
              onClick={() => {
                createSampleReturns(true) // force = true لمسح البيانات القديمة
                setTimeout(() => loadReturnRecords(), 100) // انتظار قصير لحفظ البيانات
              }}
              className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 flex items-center gap-2 transition-colors"
            >
              <Package className="h-4 w-4" />
              إعادة إنشاء البيانات
            </button>
            <button
              onClick={loadReturnRecords}
              className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 flex items-center gap-2 transition-colors"
            >
              <RefreshCw className="h-4 w-4" />
              تحديث
            </button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي المرتجعات</p>
                <p className="text-2xl font-bold text-gray-900">{filteredRecords.length}</p>
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <RotateCcw className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">في الانتظار</p>
                <p className="text-2xl font-bold text-yellow-600">{filteredRecords.filter(r => r.status === 'pending').length}</p>
              </div>
              <div className="p-3 bg-yellow-100 rounded-full">
                <Clock className="h-6 w-6 text-yellow-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">مقبولة</p>
                <p className="text-2xl font-bold text-green-600">{filteredRecords.filter(r => r.status === 'approved').length}</p>
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <CheckCircle className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">مرفوضة</p>
                <p className="text-2xl font-bold text-red-600">{filteredRecords.filter(r => r.status === 'rejected').length}</p>
              </div>
              <div className="p-3 bg-red-100 rounded-full">
                <XCircle className="h-6 w-6 text-red-600" />
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="البحث برقم المرتجع أو اسم العميل..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* Type Filter */}
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value as any)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">جميع الأنواع</option>
              <option value="sales">مرتجع مبيعات</option>
              <option value="purchase">مرتجع مشتريات</option>
            </select>

            {/* Status Filter */}
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value as any)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">جميع الحالات</option>
              <option value="pending">في الانتظار</option>
              <option value="approved">مقبول</option>
              <option value="rejected">مرفوض</option>
            </select>

            {/* Stats */}
            <div className="flex items-center justify-center bg-yellow-50 rounded-lg px-4 py-2">
              <span className="text-sm text-yellow-800">
                في الانتظار: {returnRecords.filter(r => r.status === 'pending').length}
              </span>
            </div>

            {/* Results Count */}
            <div className="flex items-center justify-center bg-gray-50 rounded-lg px-4 py-2">
              <span className="text-sm text-gray-600">
                {filteredRecords.length} من {returnRecords.length} مرتجع
              </span>
            </div>
          </div>
        </div>

        {/* Records Table */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          {loading ? (
            <div className="p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-gray-600 mt-2">جاري تحميل السجلات...</p>
            </div>
          ) : filteredRecords.length === 0 ? (
            <div className="p-8 text-center">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">لا توجد مرتجعات</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      رقم المرتجع
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      النوع
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      العميل/المورد
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      المواد المرتجعة
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      المبلغ
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      السبب
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      الحالة
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      التاريخ
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      الإجراءات
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredRecords.map((record) => (
                    <tr key={record.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <FileText className="h-4 w-4 text-purple-500 mr-2" />
                          <span className="text-sm font-medium text-gray-900">
                            {record.return_number}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          record.return_type === 'sales' ? 'bg-blue-100 text-blue-800' : 'bg-orange-100 text-orange-800'
                        }`}>
                          {getTypeText(record.return_type)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {record.return_type === 'sales'
                              ? (record.customers?.name || record.customer_name || 'عميل نقدي')
                              : (record.suppliers?.name || record.supplier_name || 'غير محدد')
                            }
                          </div>
                          {((record.return_type === 'sales' && record.customers?.phone) || 
                            (record.return_type === 'purchase' && record.suppliers?.phone)) && (
                            <div className="text-sm text-gray-500">
                              {record.return_type === 'sales' ? record.customers?.phone : record.suppliers?.phone}
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="max-w-xs">
                          {(() => {
                            console.log(`Rendering items for ${record.return_number}:`, record.return_items)
                            const items = record.return_items || []

                            if (items.length > 0) {
                              return (
                                <div className="space-y-1">
                                  {items.slice(0, 2).map((item: any, index: number) => {
                                    const medicineName = item.medicine_name ||
                                                       item.medicines?.name ||
                                                       item.medicine?.name ||
                                                       `دواء ${index + 1}`
                                    const quantity = item.quantity || 0

                                    return (
                                      <div key={index} className="flex items-center text-xs text-gray-600 bg-gray-50 rounded px-2 py-1">
                                        <Package className="h-3 w-3 text-blue-500 mr-1" />
                                        <span className="truncate" title={`${medicineName} (${quantity})`}>
                                          {medicineName} ({quantity})
                                        </span>
                                      </div>
                                    )
                                  })}
                                  {items.length > 2 && (
                                    <div className="text-xs text-gray-500 text-center">
                                      +{items.length - 2} مواد أخرى
                                    </div>
                                  )}
                                </div>
                              )
                            } else {
                              return (
                                <div className="flex items-center text-xs text-gray-400 italic">
                                  <Package className="h-3 w-3 text-gray-400 mr-1" />
                                  <span>لا توجد مواد</span>
                                </div>
                              )
                            }
                          })()}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <DollarSign className="h-4 w-4 text-red-500 mr-1" />
                          <span className="text-sm font-medium text-gray-900">
                            {record.total_amount?.toLocaleString()} د.ع
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm text-gray-900 max-w-xs truncate" title={record.reason}>
                          {record.reason}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <span className={`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(record.status)}`}>
                            {getStatusIcon(record.status)}
                            <span className="mr-1">{getStatusText(record.status)}</span>
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 text-gray-400 mr-1" />
                          <span className="text-sm text-gray-900">
                            {new Date(record.created_at).toLocaleDateString('ar-EG')}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="relative">
                          <button
                            onClick={() => setShowActionsMenu(showActionsMenu === record.id ? null : record.id)}
                            className="text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-100"
                            disabled={processingAction === record.id}
                          >
                            {processingAction === record.id ? (
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600"></div>
                            ) : (
                              <MoreVertical className="h-4 w-4" />
                            )}
                          </button>
                          
                          {showActionsMenu === record.id && (
                            <div className="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200">
                              <div className="py-1">
                                <button
                                  onClick={() => handleViewDetails(record)}
                                  className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                >
                                  <Eye className="h-4 w-4 mr-2" />
                                  عرض التفاصيل
                                </button>
                                <button
                                  onClick={() => handlePrintSingleReturnWithLaren(record)}
                                  className="flex items-center w-full px-4 py-2 text-sm text-blue-700 hover:bg-blue-50"
                                >
                                  <Printer className="h-4 w-4 mr-2" />
                                  طباعة المرتجع (قالب لارين)
                                </button>

                                {record.status === 'pending' && (
                                  <>
                                    <button
                                      onClick={() => handleApproveReturn(record)}
                                      className="flex items-center w-full px-4 py-2 text-sm text-green-700 hover:bg-green-50"
                                    >
                                      <Check className="h-4 w-4 mr-2" />
                                      قبول المرتجع
                                    </button>
                                    <button
                                      onClick={() => handleRejectReturn(record)}
                                      className="flex items-center w-full px-4 py-2 text-sm text-red-700 hover:bg-red-50"
                                    >
                                      <X className="h-4 w-4 mr-2" />
                                      رفض المرتجع
                                    </button>
                                  </>
                                )}
                              </div>
                            </div>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Details Modal */}
        {showDetailsModal && selectedRecord && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
              <div className="flex items-center justify-between p-6 border-b">
                <h2 className="text-xl font-semibold text-gray-900">
                  تفاصيل المرتجع {selectedRecord.return_number}
                </h2>
                <button
                  onClick={() => setShowDetailsModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>
              
              <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">معلومات المرتجع</h3>
                    <div className="space-y-3">
                      <div>
                        <span className="text-sm font-medium text-gray-500">رقم المرتجع:</span>
                        <p className="text-sm text-gray-900">{selectedRecord.return_number}</p>
                      </div>
                      <div>
                        <span className="text-sm font-medium text-gray-500">النوع:</span>
                        <p className="text-sm text-gray-900">{getTypeText(selectedRecord.return_type)}</p>
                      </div>
                      <div>
                        <span className="text-sm font-medium text-gray-500">التاريخ:</span>
                        <p className="text-sm text-gray-900">
                          {new Date(selectedRecord.created_at).toLocaleDateString('ar-EG')}
                        </p>
                      </div>
                      <div>
                        <span className="text-sm font-medium text-gray-500">المبلغ الإجمالي:</span>
                        <p className="text-sm text-gray-900">{selectedRecord.total_amount?.toLocaleString()} د.ع</p>
                      </div>
                      <div>
                        <span className="text-sm font-medium text-gray-500">الحالة:</span>
                        <span className={`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(selectedRecord.status)}`}>
                          {getStatusIcon(selectedRecord.status)}
                          <span className="mr-1">{getStatusText(selectedRecord.status)}</span>
                        </span>
                      </div>
                      <div>
                        <span className="text-sm font-medium text-gray-500">سبب المرتجع:</span>
                        <p className="text-sm text-gray-900">{selectedRecord.reason}</p>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">
                      {selectedRecord.return_type === 'sales' ? 'معلومات العميل' : 'معلومات المورد'}
                    </h3>
                    <div className="space-y-3">
                      <div>
                        <span className="text-sm font-medium text-gray-500">الاسم:</span>
                        <p className="text-sm text-gray-900">
                          {selectedRecord.return_type === 'sales'
                            ? (selectedRecord.customers?.name || selectedRecord.customer_name || 'عميل نقدي')
                            : (selectedRecord.suppliers?.name || selectedRecord.supplier_name || 'غير محدد')
                          }
                        </p>
                      </div>
                      {((selectedRecord.return_type === 'sales' && selectedRecord.customers?.phone) || 
                        (selectedRecord.return_type === 'purchase' && selectedRecord.suppliers?.phone)) && (
                        <div>
                          <span className="text-sm font-medium text-gray-500">الهاتف:</span>
                          <p className="text-sm text-gray-900">
                            {selectedRecord.return_type === 'sales' ? selectedRecord.customers?.phone : selectedRecord.suppliers?.phone}
                          </p>
                        </div>
                      )}
                      {((selectedRecord.return_type === 'sales' && selectedRecord.customers?.address) || 
                        (selectedRecord.return_type === 'purchase' && selectedRecord.suppliers?.address)) && (
                        <div>
                          <span className="text-sm font-medium text-gray-500">العنوان:</span>
                          <p className="text-sm text-gray-900">
                            {selectedRecord.return_type === 'sales' ? selectedRecord.customers?.address : selectedRecord.suppliers?.address}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Items Table */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center gap-2">
                    <Package className="h-5 w-5 text-blue-600" />
                    المواد المرتجعة ({selectedRecord.return_items?.length || 0} مادة)
                  </h3>

                  {selectedRecord.return_items && selectedRecord.return_items.length > 0 ? (
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200 border border-gray-200 rounded-lg">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">#</th>
                            <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">اسم الدواء</th>
                            <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الكمية</th>
                            <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">سعر الوحدة</th>
                            <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">المجموع</th>
                            <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">تاريخ الانتهاء</th>
                            <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">سبب الإرجاع</th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {selectedRecord.return_items.map((item: any, index: number) => {
                            const medicineName = item.medicine_name ||
                                               item.medicines?.name ||
                                               item.medicine?.name ||
                                               `دواء ${index + 1}`
                            const quantity = item.quantity || 0
                            const unitPrice = item.unit_price || item.unit_cost || item.price || 0
                            const totalPrice = item.total_price || item.total_cost || item.total || (quantity * unitPrice)

                            return (
                              <tr key={index} className="hover:bg-gray-50">
                                <td className="px-4 py-3 text-sm font-medium text-gray-900">
                                  {index + 1}
                                </td>
                                <td className="px-4 py-3 text-sm text-gray-900">
                                  <div className="flex items-center gap-2">
                                    <Package className="h-4 w-4 text-blue-500" />
                                    <span className="font-medium">{medicineName}</span>
                                  </div>
                                </td>
                                <td className="px-4 py-3 text-sm text-gray-900">
                                  <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                                    {quantity}
                                  </span>
                                </td>
                                <td className="px-4 py-3 text-sm text-gray-900">
                                  {unitPrice.toLocaleString()} د.ع
                                </td>
                                <td className="px-4 py-3 text-sm font-medium text-gray-900">
                                  {totalPrice.toLocaleString()} د.ع
                                </td>
                                <td className="px-4 py-3 text-sm text-gray-900">
                                  {item.expiry_date ? new Date(item.expiry_date).toLocaleDateString('ar-EG') : 'غير محدد'}
                                </td>
                                <td className="px-4 py-3 text-sm text-gray-900">
                                  <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-xs">
                                    {item.reason || item.return_reason || 'غير محدد'}
                                  </span>
                                </td>
                              </tr>
                            )
                          })}
                        </tbody>
                        <tfoot className="bg-gray-50">
                          <tr>
                            <td colSpan={4} className="px-4 py-3 text-sm font-medium text-gray-900 text-right">
                              المجموع الكلي:
                            </td>
                            <td className="px-4 py-3 text-sm font-bold text-gray-900">
                              {selectedRecord.total_amount?.toLocaleString() || 0} د.ع
                            </td>
                            <td colSpan={2}></td>
                          </tr>
                        </tfoot>
                      </table>
                    </div>
                  ) : (
                    <div className="text-center py-12 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                      <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد مواد مرتجعة</h3>
                      <p className="text-gray-500">لم يتم تسجيل أي مواد لهذا المرتجع</p>
                    </div>
                  )}
                </div>
              </div>
              
              <div className="flex items-center justify-end gap-3 p-6 border-t bg-gray-50">
                <button
                  onClick={() => setShowDetailsModal(false)}
                  className="px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300"
                >
                  إغلاق
                </button>
                
                {selectedRecord.status === 'pending' && (
                  <>
                    <button
                      onClick={() => {
                        handleApproveReturn(selectedRecord)
                        setShowDetailsModal(false)
                      }}
                      className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center gap-2"
                    >
                      <Check className="h-4 w-4" />
                      قبول المرتجع
                    </button>
                    <button
                      onClick={() => {
                        handleRejectReturn(selectedRecord)
                        setShowDetailsModal(false)
                      }}
                      className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 flex items-center gap-2"
                    >
                      <X className="h-4 w-4" />
                      رفض المرتجع
                    </button>
                  </>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </AppLayout>
  )
}
