<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار سريع للطباعة</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        .button:hover {
            background: #2563eb;
        }
        .button.success {
            background: #10b981;
        }
        .button.danger {
            background: #ef4444;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            white-space: pre-wrap;
            font-size: 12px;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
        .warning { color: #ffc107; }
        .print-preview {
            border: 2px solid #ddd;
            margin-top: 20px;
            padding: 20px;
            background: white;
            font-size: 14px;
        }
        .invoice-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        .invoice-table th,
        .invoice-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        .invoice-table th {
            background-color: #f5f5f5;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار سريع لطباعة الفواتير</h1>
        
        <div>
            <button class="button success" onclick="setupTestData()">إعداد بيانات الاختبار</button>
            <button class="button" onclick="createTestInvoice()">إنشاء فاتورة اختبار</button>
            <button class="button" onclick="testPrintPreview()">معاينة الطباعة</button>
            <button class="button danger" onclick="clearAll()">مسح الكل</button>
        </div>
        
        <div id="log" class="log"></div>
        <div id="printPreview" class="print-preview" style="display: none;"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString('ar-EG');
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : type === 'warning' ? 'warning' : 'info';
            logDiv.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearAll() {
            localStorage.clear();
            document.getElementById('log').innerHTML = '';
            document.getElementById('printPreview').style.display = 'none';
            log('تم مسح جميع البيانات', 'success');
        }

        function setupTestData() {
            log('🔄 إعداد بيانات الاختبار...');
            
            const medicines = [
                {
                    id: 'med_1',
                    name: 'باراسيتامول 500 مجم',
                    category: 'مسكنات',
                    manufacturer: 'شركة الأدوية العراقية',
                    strength: '500mg',
                    form: 'أقراص'
                },
                {
                    id: 'med_2',
                    name: 'أموكسيسيلين 250 مجم',
                    category: 'مضادات حيوية',
                    manufacturer: 'شركة بغداد للأدوية',
                    strength: '250mg',
                    form: 'كبسولات'
                }
            ];

            const batches = [
                {
                    id: 'batch_1',
                    medicine_id: 'med_1',
                    batch_code: 'PAR001',
                    expiry_date: '2025-12-31',
                    quantity: 100,
                    selling_price: 750
                },
                {
                    id: 'batch_2',
                    medicine_id: 'med_2',
                    batch_code: 'AMX001',
                    expiry_date: '2025-06-30',
                    quantity: 50,
                    selling_price: 1500
                }
            ];

            localStorage.setItem('medicines', JSON.stringify(medicines));
            localStorage.setItem('medicine_batches', JSON.stringify(batches));
            localStorage.setItem('sales_invoices', JSON.stringify([]));
            localStorage.setItem('sales_invoice_items', JSON.stringify([]));

            log('✅ تم إعداد البيانات الأساسية', 'success');
        }

        function createTestInvoice() {
            log('🔄 إنشاء فاتورة اختبار...');
            
            const invoiceId = `invoice_${Date.now()}`;
            const invoice = {
                id: invoiceId,
                invoice_number: `TEST-${Date.now()}`,
                customer_name: 'عميل اختبار',
                total_amount: 2250,
                final_amount: 2250,
                payment_method: 'cash',
                payment_status: 'paid',
                created_at: new Date().toISOString()
            };

            const items = [
                {
                    id: `item_${Date.now()}_1`,
                    invoice_id: invoiceId,
                    medicine_batch_id: 'batch_1',
                    quantity: 2,
                    unit_price: 750,
                    total_price: 1500,
                    is_gift: false,
                    medicine_name: 'باراسيتامول 500 مجم',
                    medicineName: 'باراسيتامول 500 مجم'
                },
                {
                    id: `item_${Date.now()}_2`,
                    invoice_id: invoiceId,
                    medicine_batch_id: 'batch_2',
                    quantity: 1,
                    unit_price: 1500,
                    total_price: 1500,
                    is_gift: false,
                    medicine_name: 'أموكسيسيلين 250 مجم',
                    medicineName: 'أموكسيسيلين 250 مجم'
                }
            ];

            // Save to localStorage
            const existingInvoices = JSON.parse(localStorage.getItem('sales_invoices') || '[]');
            existingInvoices.push(invoice);
            localStorage.setItem('sales_invoices', JSON.stringify(existingInvoices));

            const existingItems = JSON.parse(localStorage.getItem('sales_invoice_items') || '[]');
            existingItems.push(...items);
            localStorage.setItem('sales_invoice_items', JSON.stringify(existingItems));

            log(`✅ تم إنشاء الفاتورة: ${invoice.invoice_number}`, 'success');
            log(`📦 تم إضافة ${items.length} عنصر`, 'success');
        }

        function testPrintPreview() {
            log('🖨️ اختبار معاينة الطباعة...');
            
            const invoices = JSON.parse(localStorage.getItem('sales_invoices') || '[]');
            const items = JSON.parse(localStorage.getItem('sales_invoice_items') || '[]');
            const medicines = JSON.parse(localStorage.getItem('medicines') || '[]');
            const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]');

            if (invoices.length === 0) {
                log('⚠️ لا توجد فواتير للطباعة', 'warning');
                return;
            }

            const lastInvoice = invoices[invoices.length - 1];
            const invoiceItems = items.filter(item => item.invoice_id === lastInvoice.id);

            log(`🖨️ معاينة الفاتورة: ${lastInvoice.invoice_number}`, 'info');

            // Enhance items with medicine names (simulate the fix)
            const enhancedItems = invoiceItems.map(item => {
                const batch = batches.find(b => b.id === item.medicine_batch_id);
                const medicine = medicines.find(m => m.id === batch?.medicine_id);
                const medicineName = medicine?.name || item.medicine_name || item.medicineName || 'غير محدد';

                return {
                    ...item,
                    medicine_name: medicineName,
                    medicineName: medicineName,
                    medicine_batches: {
                        batch_code: batch?.batch_code || '',
                        expiry_date: batch?.expiry_date || '',
                        medicines: {
                            name: medicineName,
                            category: medicine?.category || '',
                            manufacturer: medicine?.manufacturer || '',
                            strength: medicine?.strength || '',
                            form: medicine?.form || ''
                        }
                    }
                };
            });

            // Create print preview
            const printPreview = document.getElementById('printPreview');
            printPreview.style.display = 'block';
            
            let tableRows = '';
            enhancedItems.forEach((item, index) => {
                const medicineName = item.medicine_batches?.medicines?.name || item.medicine_name || item.medicineName || 'غير محدد';
                const status = medicineName === 'غير محدد' ? '❌' : '✅';
                
                tableRows += `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${medicineName} ${status}</td>
                        <td>${item.quantity}</td>
                        <td>${item.unit_price}</td>
                        <td>${item.total_price}</td>
                        <td>${item.medicine_batches?.batch_code || ''}</td>
                        <td>${item.medicine_batches?.expiry_date || ''}</td>
                    </tr>
                `;
                
                log(`📦 العنصر ${index + 1}: ${medicineName}`, medicineName === 'غير محدد' ? 'error' : 'success');
            });

            printPreview.innerHTML = `
                <h3>معاينة فاتورة المبيعات</h3>
                <p><strong>رقم الفاتورة:</strong> ${lastInvoice.invoice_number}</p>
                <p><strong>اسم العميل:</strong> ${lastInvoice.customer_name}</p>
                <p><strong>التاريخ:</strong> ${new Date(lastInvoice.created_at).toLocaleDateString('ar-EG')}</p>
                
                <table class="invoice-table">
                    <thead>
                        <tr>
                            <th>ت</th>
                            <th>اسم الدواء</th>
                            <th>الكمية</th>
                            <th>سعر الوحدة</th>
                            <th>المجموع</th>
                            <th>رقم الدفعة</th>
                            <th>تاريخ الانتهاء</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${tableRows}
                    </tbody>
                </table>
                
                <p><strong>المجموع النهائي:</strong> ${lastInvoice.final_amount} د.ع</p>
            `;

            log('✅ تم إنشاء معاينة الطباعة', 'success');
        }

        // Initialize
        log('تم تحميل صفحة اختبار الطباعة السريع', 'success');
    </script>
</body>
</html>
