'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { usePermissions } from '@/contexts/AuthContext'
import {
  Home,
  ShoppingCart,
  Package,
  Users,
  UserCheck,
  RotateCcw,
  BarChart3,
  Settings,
  Pill,
  FileText,
  Wallet,
  Shield,
  Activity,
  Bell,
  Wrench,
  Printer,
  Bug
} from 'lucide-react'

interface MenuItem {
  title: string
  href: string
  icon: any
  permission?: string
  requireAny?: string[]
}

const getMenuItems = (permissions: any): MenuItem[] => [
  {
    title: 'الرئيسية',
    href: '/',
    icon: Home
  },
  {
    title: 'إدارة المخزون',
    href: '/inventory',
    icon: Package,
    permission: 'inventory_view'
  },
  {
    title: 'المبيعات',
    href: '/sales',
    icon: ShoppingCart,
    permission: 'sales_view'
  },

  {
    title: 'المشتريات',
    href: '/purchases',
    icon: Pill,
    permission: 'purchases_view'
  },

  {
    title: 'العملاء',
    href: '/customers',
    icon: Users,
    permission: 'customers_view'
  },
  {
    title: 'الموردين',
    href: '/suppliers',
    icon: UserCheck,
    permission: 'suppliers_view'
  },
  {
    title: 'المرتجعات',
    href: '/returns',
    icon: RotateCcw,
    permission: 'returns_view'
  },

  {
    title: 'الصندوق',
    href: '/cashbox',
    icon: Wallet,
    permission: 'cashbox_view'
  },
  {
    title: 'التقارير',
    href: '/reports',
    icon: BarChart3,
    permission: 'reports_view'
  },
  {
    title: 'إدارة المستخدمين',
    href: '/users',
    icon: Shield,
    permission: 'users_view'
  },
  {
    title: 'سجل النشاطات',
    href: '/activity-log',
    icon: Activity,
    permission: 'users_view'
  },
  {
    title: 'التنبيهات',
    href: '/notifications',
    icon: Bell
  },
  {
    title: 'الإعدادات',
    href: '/settings',
    icon: Settings,
    permission: 'settings_view'
  },
  {
    title: 'إصلاح البيانات',
    href: '/fix-data',
    icon: Wrench,
    permission: 'settings_view'
  },
  {
    title: 'اختبار الطباعة',
    href: '/test-print',
    icon: Printer,
    permission: 'sales_view'
  },
  {
    title: 'تشخيص البيانات',
    href: '/debug-data',
    icon: Bug,
    permission: 'settings_view'
  },
  {
    title: 'اختبار المبيعات',
    href: '/debug-sales',
    icon: ShoppingCart,
    permission: 'sales_view'
  }
]

interface SidebarProps {
  isOpen?: boolean
  onClose?: () => void
}

export default function Sidebar({ isOpen = false, onClose }: SidebarProps = {}) {
  const pathname = usePathname()
  const { hasPermission, permissions } = usePermissions()

  const menuItems = getMenuItems(permissions)

  // إغلاق القائمة عند تغيير الصفحة
  useEffect(() => {
    if (onClose) {
      onClose()
    }
  }, [pathname, onClose])

  // إغلاق القائمة عند النقر خارجها
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const sidebar = document.getElementById('mobile-sidebar')
      const menuButton = document.getElementById('mobile-menu-button')

      if (sidebar && !sidebar.contains(event.target as Node) &&
          menuButton && !menuButton.contains(event.target as Node)) {
        if (onClose) {
          onClose()
        }
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
      document.body.style.overflow = 'unset'
    }
  }, [isOpen, onClose])

  // تصفية العناصر بناءً على الصلاحيات
  const visibleMenuItems = menuItems.filter(item => {
    if (!item.permission) return true // العناصر بدون صلاحيات مطلوبة (مثل الرئيسية)
    return hasPermission(item.permission as any)
  })

  return (
    <>
      {/* Mobile Overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-30 md:hidden"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <div
        id="mobile-sidebar"
        className={`bg-gradient-to-b from-white to-gray-50 shadow-2xl h-screen w-64 fixed right-0 top-0 z-40 border-l border-gray-200 transform transition-transform duration-300 ease-in-out ${
          isOpen ? 'translate-x-0' : 'translate-x-full md:translate-x-0'
        }`}
      >
        <div className="p-6">
        <div className="flex items-center gap-3 mb-8 p-4 bg-gradient-to-r from-blue-600 to-indigo-700 rounded-xl text-white shadow-lg">
          <div className="bg-white bg-opacity-20 p-2 rounded-lg">
            <Pill className="h-6 w-6 text-white" />
          </div>
          <div>
            <h1 className="text-lg font-bold">نظام الصيدلية</h1>
            <p className="text-sm text-blue-100">مكتب لارين العلمي</p>
          </div>
        </div>

        <nav className="space-y-2">
          {visibleMenuItems.map((item) => {
            const Icon = item.icon
            const isActive = pathname === item.href

            return (
              <Link
                key={item.href}
                href={item.href}
                className={`flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300 group hover-lift ${
                  isActive
                    ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg transform scale-105'
                    : 'text-gray-600 hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-100 hover:text-gray-800 hover:shadow-md'
                }`}
              >
                <Icon className={`h-5 w-5 transition-transform duration-300 ${isActive ? 'text-white' : 'group-hover:scale-110'}`} />
                <span className="font-medium">{item.title}</span>
                {isActive && (
                  <div className="mr-auto w-2 h-2 bg-white rounded-full animate-pulse"></div>
                )}
              </Link>
            )
          })}
        </nav>

        {/* Footer */}
        <div className="absolute bottom-4 left-4 right-4">
          <div className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl p-4 text-center border border-gray-200 shadow-sm">
            <div className="flex items-center justify-center gap-2 mb-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <p className="text-xs text-gray-600 font-medium">
                متصل
              </p>
            </div>
            <p className="text-xs text-gray-500">
              © 2024 مكتب لارين العلمي
            </p>
            <p className="text-xs text-gray-400 mt-1">
              الإصدار 1.0.0
            </p>
          </div>
        </div>
      </div>
      </div>
    </>
  )
}

// تصدير دالة للتحكم في القائمة من مكونات أخرى
export const useSidebar = () => {
  const [isOpen, setIsOpen] = useState(false)
  return { isOpen, setIsOpen, toggleSidebar: () => setIsOpen(!isOpen) }
}
