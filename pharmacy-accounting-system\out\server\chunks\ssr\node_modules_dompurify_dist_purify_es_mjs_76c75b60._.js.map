{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/dompurify/dist/purify.es.mjs", "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%287%29/New%20folder%20%2819%29/pharmacy-accounting-system/node_modules/dompurify/src/utils.ts", "file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%287%29/New%20folder%20%2819%29/pharmacy-accounting-system/node_modules/dompurify/src/tags.ts", "file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%287%29/New%20folder%20%2819%29/pharmacy-accounting-system/node_modules/dompurify/src/attrs.ts", "file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%287%29/New%20folder%20%2819%29/pharmacy-accounting-system/node_modules/dompurify/src/regexp.ts", "file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%287%29/New%20folder%20%2819%29/pharmacy-accounting-system/node_modules/dompurify/src/purify.ts"], "sourcesContent": ["const {\n  entries,\n  setPrototypeOf,\n  isFrozen,\n  getPrototypeOf,\n  getOwnPropertyDescriptor,\n} = Object;\n\nlet { freeze, seal, create } = Object; // eslint-disable-line import/no-mutable-exports\nlet { apply, construct } = typeof Reflect !== 'undefined' && Reflect;\n\nif (!freeze) {\n  freeze = function (x) {\n    return x;\n  };\n}\n\nif (!seal) {\n  seal = function (x) {\n    return x;\n  };\n}\n\nif (!apply) {\n  apply = function (fun, thisValue, args) {\n    return fun.apply(thisValue, args);\n  };\n}\n\nif (!construct) {\n  construct = function (Func, args) {\n    return new Func(...args);\n  };\n}\n\nconst arrayForEach = unapply(Array.prototype.forEach);\nconst arrayIndexOf = unapply(Array.prototype.indexOf);\nconst arrayLastIndexOf = unapply(Array.prototype.lastIndexOf);\nconst arrayPop = unapply(Array.prototype.pop);\nconst arrayPush = unapply(Array.prototype.push);\nconst arraySlice = unapply(Array.prototype.slice);\nconst arraySplice = unapply(Array.prototype.splice);\n\nconst stringToLowerCase = unapply(String.prototype.toLowerCase);\nconst stringToString = unapply(String.prototype.toString);\nconst stringMatch = unapply(String.prototype.match);\nconst stringReplace = unapply(String.prototype.replace);\nconst stringIndexOf = unapply(String.prototype.indexOf);\nconst stringTrim = unapply(String.prototype.trim);\n\nconst objectHasOwnProperty = unapply(Object.prototype.hasOwnProperty);\n\nconst regExpTest = unapply(RegExp.prototype.test);\n\nconst typeErrorCreate = unconstruct(TypeError);\n\n/**\n * Creates a new function that calls the given function with a specified thisArg and arguments.\n *\n * @param func - The function to be wrapped and called.\n * @returns A new function that calls the given function with a specified thisArg and arguments.\n */\nfunction unapply<T>(\n  func: (thisArg: any, ...args: any[]) => T\n): (thisArg: any, ...args: any[]) => T {\n  return (thisArg: any, ...args: any[]): T => {\n    if (thisArg instanceof RegExp) {\n      thisArg.lastIndex = 0;\n    }\n\n    return apply(func, thisArg, args);\n  };\n}\n\n/**\n * Creates a new function that constructs an instance of the given constructor function with the provided arguments.\n *\n * @param func - The constructor function to be wrapped and called.\n * @returns A new function that constructs an instance of the given constructor function with the provided arguments.\n */\nfunction unconstruct<T>(func: (...args: any[]) => T): (...args: any[]) => T {\n  return (...args: any[]): T => construct(func, args);\n}\n\n/**\n * Add properties to a lookup table\n *\n * @param set - The set to which elements will be added.\n * @param array - The array containing elements to be added to the set.\n * @param transformCaseFunc - An optional function to transform the case of each element before adding to the set.\n * @returns The modified set with added elements.\n */\nfunction addToSet(\n  set: Record<string, any>,\n  array: readonly any[],\n  transformCaseFunc: ReturnType<typeof unapply<string>> = stringToLowerCase\n): Record<string, any> {\n  if (setPrototypeOf) {\n    // Make 'in' and truthy checks like Boolean(set.constructor)\n    // independent of any properties defined on Object.prototype.\n    // Prevent prototype setters from intercepting set as a this value.\n    setPrototypeOf(set, null);\n  }\n\n  let l = array.length;\n  while (l--) {\n    let element = array[l];\n    if (typeof element === 'string') {\n      const lcElement = transformCaseFunc(element);\n      if (lcElement !== element) {\n        // Config presets (e.g. tags.js, attrs.js) are immutable.\n        if (!isFrozen(array)) {\n          (array as any[])[l] = lcElement;\n        }\n\n        element = lcElement;\n      }\n    }\n\n    set[element] = true;\n  }\n\n  return set;\n}\n\n/**\n * Clean up an array to harden against CSPP\n *\n * @param array - The array to be cleaned.\n * @returns The cleaned version of the array\n */\nfunction cleanArray<T>(array: T[]): Array<T | null> {\n  for (let index = 0; index < array.length; index++) {\n    const isPropertyExist = objectHasOwnProperty(array, index);\n\n    if (!isPropertyExist) {\n      array[index] = null;\n    }\n  }\n\n  return array;\n}\n\n/**\n * Shallow clone an object\n *\n * @param object - The object to be cloned.\n * @returns A new object that copies the original.\n */\nfunction clone<T extends Record<string, any>>(object: T): T {\n  const newObject = create(null);\n\n  for (const [property, value] of entries(object)) {\n    const isPropertyExist = objectHasOwnProperty(object, property);\n\n    if (isPropertyExist) {\n      if (Array.isArray(value)) {\n        newObject[property] = cleanArray(value);\n      } else if (\n        value &&\n        typeof value === 'object' &&\n        value.constructor === Object\n      ) {\n        newObject[property] = clone(value);\n      } else {\n        newObject[property] = value;\n      }\n    }\n  }\n\n  return newObject;\n}\n\n/**\n * This method automatically checks if the prop is function or getter and behaves accordingly.\n *\n * @param object - The object to look up the getter function in its prototype chain.\n * @param prop - The property name for which to find the getter function.\n * @returns The getter function found in the prototype chain or a fallback function.\n */\nfunction lookupGetter<T extends Record<string, any>>(\n  object: T,\n  prop: string\n): ReturnType<typeof unapply<any>> | (() => null) {\n  while (object !== null) {\n    const desc = getOwnPropertyDescriptor(object, prop);\n\n    if (desc) {\n      if (desc.get) {\n        return unapply(desc.get);\n      }\n\n      if (typeof desc.value === 'function') {\n        return unapply(desc.value);\n      }\n    }\n\n    object = getPrototypeOf(object);\n  }\n\n  function fallbackValue(): null {\n    return null;\n  }\n\n  return fallbackValue;\n}\n\nexport {\n  // Array\n  arrayForEach,\n  arrayIndexOf,\n  arrayLastIndexOf,\n  arrayPop,\n  arrayPush,\n  arraySlice,\n  arraySplice,\n  // Object\n  entries,\n  freeze,\n  getPrototypeOf,\n  getOwnPropertyDescriptor,\n  isFrozen,\n  setPrototypeOf,\n  seal,\n  clone,\n  create,\n  objectHasOwnProperty,\n  // RegExp\n  regExpTest,\n  // String\n  stringIndexOf,\n  stringMatch,\n  stringReplace,\n  stringToLowerCase,\n  stringToString,\n  stringTrim,\n  // Errors\n  typeErrorCreate,\n  // Other\n  lookupGetter,\n  addToSet,\n  // Reflect\n  unapply,\n  unconstruct,\n};\n", "import { freeze } from './utils.js';\n\nexport const html = freeze([\n  'a',\n  'abbr',\n  'acronym',\n  'address',\n  'area',\n  'article',\n  'aside',\n  'audio',\n  'b',\n  'bdi',\n  'bdo',\n  'big',\n  'blink',\n  'blockquote',\n  'body',\n  'br',\n  'button',\n  'canvas',\n  'caption',\n  'center',\n  'cite',\n  'code',\n  'col',\n  'colgroup',\n  'content',\n  'data',\n  'datalist',\n  'dd',\n  'decorator',\n  'del',\n  'details',\n  'dfn',\n  'dialog',\n  'dir',\n  'div',\n  'dl',\n  'dt',\n  'element',\n  'em',\n  'fieldset',\n  'figcaption',\n  'figure',\n  'font',\n  'footer',\n  'form',\n  'h1',\n  'h2',\n  'h3',\n  'h4',\n  'h5',\n  'h6',\n  'head',\n  'header',\n  'hgroup',\n  'hr',\n  'html',\n  'i',\n  'img',\n  'input',\n  'ins',\n  'kbd',\n  'label',\n  'legend',\n  'li',\n  'main',\n  'map',\n  'mark',\n  'marquee',\n  'menu',\n  'menuitem',\n  'meter',\n  'nav',\n  'nobr',\n  'ol',\n  'optgroup',\n  'option',\n  'output',\n  'p',\n  'picture',\n  'pre',\n  'progress',\n  'q',\n  'rp',\n  'rt',\n  'ruby',\n  's',\n  'samp',\n  'section',\n  'select',\n  'shadow',\n  'small',\n  'source',\n  'spacer',\n  'span',\n  'strike',\n  'strong',\n  'style',\n  'sub',\n  'summary',\n  'sup',\n  'table',\n  'tbody',\n  'td',\n  'template',\n  'textarea',\n  'tfoot',\n  'th',\n  'thead',\n  'time',\n  'tr',\n  'track',\n  'tt',\n  'u',\n  'ul',\n  'var',\n  'video',\n  'wbr',\n] as const);\n\nexport const svg = freeze([\n  'svg',\n  'a',\n  'altglyph',\n  'altglyphdef',\n  'altglyphitem',\n  'animatecolor',\n  'animatemotion',\n  'animatetransform',\n  'circle',\n  'clippath',\n  'defs',\n  'desc',\n  'ellipse',\n  'filter',\n  'font',\n  'g',\n  'glyph',\n  'glyphref',\n  'hkern',\n  'image',\n  'line',\n  'lineargradient',\n  'marker',\n  'mask',\n  'metadata',\n  'mpath',\n  'path',\n  'pattern',\n  'polygon',\n  'polyline',\n  'radialgradient',\n  'rect',\n  'stop',\n  'style',\n  'switch',\n  'symbol',\n  'text',\n  'textpath',\n  'title',\n  'tref',\n  'tspan',\n  'view',\n  'vkern',\n] as const);\n\nexport const svgFilters = freeze([\n  'feBlend',\n  'feColorMatrix',\n  'feComponentTransfer',\n  'feComposite',\n  'feConvolveMatrix',\n  'feDiffuseLighting',\n  'feDisplacementMap',\n  'feDistantLight',\n  'feDropShadow',\n  'feFlood',\n  'feFuncA',\n  'feFuncB',\n  'feFuncG',\n  'feFuncR',\n  'feGaussianBlur',\n  'feImage',\n  'feMerge',\n  'feMergeNode',\n  'feMorphology',\n  'feOffset',\n  'fePointLight',\n  'feSpecularLighting',\n  'feSpotLight',\n  'feTile',\n  'feTurbulence',\n] as const);\n\n// List of SVG elements that are disallowed by default.\n// We still need to know them so that we can do namespace\n// checks properly in case one wants to add them to\n// allow-list.\nexport const svgDisallowed = freeze([\n  'animate',\n  'color-profile',\n  'cursor',\n  'discard',\n  'font-face',\n  'font-face-format',\n  'font-face-name',\n  'font-face-src',\n  'font-face-uri',\n  'foreignobject',\n  'hatch',\n  'hatchpath',\n  'mesh',\n  'meshgradient',\n  'meshpatch',\n  'meshrow',\n  'missing-glyph',\n  'script',\n  'set',\n  'solidcolor',\n  'unknown',\n  'use',\n] as const);\n\nexport const mathMl = freeze([\n  'math',\n  'menclose',\n  'merror',\n  'mfenced',\n  'mfrac',\n  'mglyph',\n  'mi',\n  'mlabeledtr',\n  'mmultiscripts',\n  'mn',\n  'mo',\n  'mover',\n  'mpadded',\n  'mphantom',\n  'mroot',\n  'mrow',\n  'ms',\n  'mspace',\n  'msqrt',\n  'mstyle',\n  'msub',\n  'msup',\n  'msubsup',\n  'mtable',\n  'mtd',\n  'mtext',\n  'mtr',\n  'munder',\n  'munderover',\n  'mprescripts',\n] as const);\n\n// Similarly to SVG, we want to know all MathML elements,\n// even those that we disallow by default.\nexport const mathMlDisallowed = freeze([\n  'maction',\n  'maligngroup',\n  'malignmark',\n  'mlongdiv',\n  'mscarries',\n  'mscarry',\n  'msgroup',\n  'mstack',\n  'msline',\n  'msrow',\n  'semantics',\n  'annotation',\n  'annotation-xml',\n  'mprescripts',\n  'none',\n] as const);\n\nexport const text = freeze(['#text'] as const);\n", "import { freeze } from './utils.js';\n\nexport const html = freeze([\n  'accept',\n  'action',\n  'align',\n  'alt',\n  'autocapitalize',\n  'autocomplete',\n  'autopictureinpicture',\n  'autoplay',\n  'background',\n  'bgcolor',\n  'border',\n  'capture',\n  'cellpadding',\n  'cellspacing',\n  'checked',\n  'cite',\n  'class',\n  'clear',\n  'color',\n  'cols',\n  'colspan',\n  'controls',\n  'controlslist',\n  'coords',\n  'crossorigin',\n  'datetime',\n  'decoding',\n  'default',\n  'dir',\n  'disabled',\n  'disablepictureinpicture',\n  'disableremoteplayback',\n  'download',\n  'draggable',\n  'enctype',\n  'enterkeyhint',\n  'face',\n  'for',\n  'headers',\n  'height',\n  'hidden',\n  'high',\n  'href',\n  'hreflang',\n  'id',\n  'inputmode',\n  'integrity',\n  'ismap',\n  'kind',\n  'label',\n  'lang',\n  'list',\n  'loading',\n  'loop',\n  'low',\n  'max',\n  'maxlength',\n  'media',\n  'method',\n  'min',\n  'minlength',\n  'multiple',\n  'muted',\n  'name',\n  'nonce',\n  'noshade',\n  'novalidate',\n  'nowrap',\n  'open',\n  'optimum',\n  'pattern',\n  'placeholder',\n  'playsinline',\n  'popover',\n  'popovertarget',\n  'popovertargetaction',\n  'poster',\n  'preload',\n  'pubdate',\n  'radiogroup',\n  'readonly',\n  'rel',\n  'required',\n  'rev',\n  'reversed',\n  'role',\n  'rows',\n  'rowspan',\n  'spellcheck',\n  'scope',\n  'selected',\n  'shape',\n  'size',\n  'sizes',\n  'span',\n  'srclang',\n  'start',\n  'src',\n  'srcset',\n  'step',\n  'style',\n  'summary',\n  'tabindex',\n  'title',\n  'translate',\n  'type',\n  'usemap',\n  'valign',\n  'value',\n  'width',\n  'wrap',\n  'xmlns',\n  'slot',\n] as const);\n\nexport const svg = freeze([\n  'accent-height',\n  'accumulate',\n  'additive',\n  'alignment-baseline',\n  'amplitude',\n  'ascent',\n  'attributename',\n  'attributetype',\n  'azimuth',\n  'basefrequency',\n  'baseline-shift',\n  'begin',\n  'bias',\n  'by',\n  'class',\n  'clip',\n  'clippathunits',\n  'clip-path',\n  'clip-rule',\n  'color',\n  'color-interpolation',\n  'color-interpolation-filters',\n  'color-profile',\n  'color-rendering',\n  'cx',\n  'cy',\n  'd',\n  'dx',\n  'dy',\n  'diffuseconstant',\n  'direction',\n  'display',\n  'divisor',\n  'dur',\n  'edgemode',\n  'elevation',\n  'end',\n  'exponent',\n  'fill',\n  'fill-opacity',\n  'fill-rule',\n  'filter',\n  'filterunits',\n  'flood-color',\n  'flood-opacity',\n  'font-family',\n  'font-size',\n  'font-size-adjust',\n  'font-stretch',\n  'font-style',\n  'font-variant',\n  'font-weight',\n  'fx',\n  'fy',\n  'g1',\n  'g2',\n  'glyph-name',\n  'glyphref',\n  'gradientunits',\n  'gradienttransform',\n  'height',\n  'href',\n  'id',\n  'image-rendering',\n  'in',\n  'in2',\n  'intercept',\n  'k',\n  'k1',\n  'k2',\n  'k3',\n  'k4',\n  'kerning',\n  'keypoints',\n  'keysplines',\n  'keytimes',\n  'lang',\n  'lengthadjust',\n  'letter-spacing',\n  'kernelmatrix',\n  'kernelunitlength',\n  'lighting-color',\n  'local',\n  'marker-end',\n  'marker-mid',\n  'marker-start',\n  'markerheight',\n  'markerunits',\n  'markerwidth',\n  'maskcontentunits',\n  'maskunits',\n  'max',\n  'mask',\n  'media',\n  'method',\n  'mode',\n  'min',\n  'name',\n  'numoctaves',\n  'offset',\n  'operator',\n  'opacity',\n  'order',\n  'orient',\n  'orientation',\n  'origin',\n  'overflow',\n  'paint-order',\n  'path',\n  'pathlength',\n  'patterncontentunits',\n  'patterntransform',\n  'patternunits',\n  'points',\n  'preservealpha',\n  'preserveaspectratio',\n  'primitiveunits',\n  'r',\n  'rx',\n  'ry',\n  'radius',\n  'refx',\n  'refy',\n  'repeatcount',\n  'repeatdur',\n  'restart',\n  'result',\n  'rotate',\n  'scale',\n  'seed',\n  'shape-rendering',\n  'slope',\n  'specularconstant',\n  'specularexponent',\n  'spreadmethod',\n  'startoffset',\n  'stddeviation',\n  'stitchtiles',\n  'stop-color',\n  'stop-opacity',\n  'stroke-dasharray',\n  'stroke-dashoffset',\n  'stroke-linecap',\n  'stroke-linejoin',\n  'stroke-miterlimit',\n  'stroke-opacity',\n  'stroke',\n  'stroke-width',\n  'style',\n  'surfacescale',\n  'systemlanguage',\n  'tabindex',\n  'tablevalues',\n  'targetx',\n  'targety',\n  'transform',\n  'transform-origin',\n  'text-anchor',\n  'text-decoration',\n  'text-rendering',\n  'textlength',\n  'type',\n  'u1',\n  'u2',\n  'unicode',\n  'values',\n  'viewbox',\n  'visibility',\n  'version',\n  'vert-adv-y',\n  'vert-origin-x',\n  'vert-origin-y',\n  'width',\n  'word-spacing',\n  'wrap',\n  'writing-mode',\n  'xchannelselector',\n  'ychannelselector',\n  'x',\n  'x1',\n  'x2',\n  'xmlns',\n  'y',\n  'y1',\n  'y2',\n  'z',\n  'zoomandpan',\n] as const);\n\nexport const mathMl = freeze([\n  'accent',\n  'accentunder',\n  'align',\n  'bevelled',\n  'close',\n  'columnsalign',\n  'columnlines',\n  'columnspan',\n  'denomalign',\n  'depth',\n  'dir',\n  'display',\n  'displaystyle',\n  'encoding',\n  'fence',\n  'frame',\n  'height',\n  'href',\n  'id',\n  'largeop',\n  'length',\n  'linethickness',\n  'lspace',\n  'lquote',\n  'mathbackground',\n  'mathcolor',\n  'mathsize',\n  'mathvariant',\n  'maxsize',\n  'minsize',\n  'movablelimits',\n  'notation',\n  'numalign',\n  'open',\n  'rowalign',\n  'rowlines',\n  'rowspacing',\n  'rowspan',\n  'rspace',\n  'rquote',\n  'scriptlevel',\n  'scriptminsize',\n  'scriptsizemultiplier',\n  'selection',\n  'separator',\n  'separators',\n  'stretchy',\n  'subscriptshift',\n  'supscriptshift',\n  'symmetric',\n  'voffset',\n  'width',\n  'xmlns',\n]);\n\nexport const xml = freeze([\n  'xlink:href',\n  'xml:id',\n  'xlink:title',\n  'xml:space',\n  'xmlns:xlink',\n] as const);\n", "import { seal } from './utils.js';\n\n// eslint-disable-next-line unicorn/better-regex\nexport const MUSTACHE_EXPR = seal(/\\{\\{[\\w\\W]*|[\\w\\W]*\\}\\}/gm); // Specify template detection regex for SAFE_FOR_TEMPLATES mode\nexport const ERB_EXPR = seal(/<%[\\w\\W]*|[\\w\\W]*%>/gm);\nexport const TMPLIT_EXPR = seal(/\\$\\{[\\w\\W]*/gm); // eslint-disable-line unicorn/better-regex\nexport const DATA_ATTR = seal(/^data-[\\-\\w.\\u00B7-\\uFFFF]+$/); // eslint-disable-line no-useless-escape\nexport const ARIA_ATTR = seal(/^aria-[\\-\\w]+$/); // eslint-disable-line no-useless-escape\nexport const IS_ALLOWED_URI = seal(\n  /^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp|matrix):|[^a-z]|[a-z+.\\-]+(?:[^a-z+.\\-:]|$))/i // eslint-disable-line no-useless-escape\n);\nexport const IS_SCRIPT_OR_DATA = seal(/^(?:\\w+script|data):/i);\nexport const ATTR_WHITESPACE = seal(\n  /[\\u0000-\\u0020\\u00A0\\u1680\\u180E\\u2000-\\u2029\\u205F\\u3000]/g // eslint-disable-line no-control-regex\n);\nexport const DOCTYPE_NAME = seal(/^html$/i);\nexport const CUSTOM_ELEMENT = seal(/^[a-z][.\\w]*(-[.\\w]+)+$/i);\n", "/* eslint-disable @typescript-eslint/indent */\n\nimport type { TrustedHTML, TrustedTypesWindow } from 'trusted-types/lib';\nimport type { Config, UseProfilesConfig } from './config';\nimport * as TAGS from './tags.js';\nimport * as ATTRS from './attrs.js';\nimport * as EXPRESSIONS from './regexp.js';\nimport {\n  addToSet,\n  clone,\n  entries,\n  freeze,\n  arrayForEach,\n  arrayLastIndexOf,\n  arrayPop,\n  arrayPush,\n  arraySplice,\n  stringMatch,\n  stringReplace,\n  stringToLowerCase,\n  stringToString,\n  stringIndexOf,\n  stringTrim,\n  regExpTest,\n  typeErrorCreate,\n  lookupGetter,\n  create,\n  objectHasOwnProperty,\n} from './utils.js';\n\nexport type { Config } from './config';\n\ndeclare const VERSION: string;\n\n// https://developer.mozilla.org/en-US/docs/Web/API/Node/nodeType\nconst NODE_TYPE = {\n  element: 1,\n  attribute: 2,\n  text: 3,\n  cdataSection: 4,\n  entityReference: 5, // Deprecated\n  entityNode: 6, // Deprecated\n  progressingInstruction: 7,\n  comment: 8,\n  document: 9,\n  documentType: 10,\n  documentFragment: 11,\n  notation: 12, // Deprecated\n};\n\nconst getGlobal = function (): WindowLike {\n  return typeof window === 'undefined' ? null : window;\n};\n\n/**\n * Creates a no-op policy for internal use only.\n * Don't export this function outside this module!\n * @param trustedTypes The policy factory.\n * @param purifyHostElement The Script element used to load DOMPurify (to determine policy name suffix).\n * @return The policy created (or null, if Trusted Types\n * are not supported or creating the policy failed).\n */\nconst _createTrustedTypesPolicy = function (\n  trustedTypes: TrustedTypePolicyFactory,\n  purifyHostElement: HTMLScriptElement\n) {\n  if (\n    typeof trustedTypes !== 'object' ||\n    typeof trustedTypes.createPolicy !== 'function'\n  ) {\n    return null;\n  }\n\n  // Allow the callers to control the unique policy name\n  // by adding a data-tt-policy-suffix to the script element with the DOMPurify.\n  // Policy creation with duplicate names throws in Trusted Types.\n  let suffix = null;\n  const ATTR_NAME = 'data-tt-policy-suffix';\n  if (purifyHostElement && purifyHostElement.hasAttribute(ATTR_NAME)) {\n    suffix = purifyHostElement.getAttribute(ATTR_NAME);\n  }\n\n  const policyName = 'dompurify' + (suffix ? '#' + suffix : '');\n\n  try {\n    return trustedTypes.createPolicy(policyName, {\n      createHTML(html) {\n        return html;\n      },\n      createScriptURL(scriptUrl) {\n        return scriptUrl;\n      },\n    });\n  } catch (_) {\n    // Policy creation failed (most likely another DOMPurify script has\n    // already run). Skip creating the policy, as this will only cause errors\n    // if TT are enforced.\n    console.warn(\n      'TrustedTypes policy ' + policyName + ' could not be created.'\n    );\n    return null;\n  }\n};\n\nconst _createHooksMap = function (): HooksMap {\n  return {\n    afterSanitizeAttributes: [],\n    afterSanitizeElements: [],\n    afterSanitizeShadowDOM: [],\n    beforeSanitizeAttributes: [],\n    beforeSanitizeElements: [],\n    beforeSanitizeShadowDOM: [],\n    uponSanitizeAttribute: [],\n    uponSanitizeElement: [],\n    uponSanitizeShadowNode: [],\n  };\n};\n\nfunction createDOMPurify(window: WindowLike = getGlobal()): DOMPurify {\n  const DOMPurify: DOMPurify = (root: WindowLike) => createDOMPurify(root);\n\n  DOMPurify.version = VERSION;\n\n  DOMPurify.removed = [];\n\n  if (\n    !window ||\n    !window.document ||\n    window.document.nodeType !== NODE_TYPE.document ||\n    !window.Element\n  ) {\n    // Not running in a browser, provide a factory function\n    // so that you can pass your own Window\n    DOMPurify.isSupported = false;\n\n    return DOMPurify;\n  }\n\n  let { document } = window;\n\n  const originalDocument = document;\n  const currentScript: HTMLScriptElement =\n    originalDocument.currentScript as HTMLScriptElement;\n  const {\n    DocumentFragment,\n    HTMLTemplateElement,\n    Node,\n    Element,\n    NodeFilter,\n    NamedNodeMap = window.NamedNodeMap || (window as any).MozNamedAttrMap,\n    HTMLFormElement,\n    DOMParser,\n    trustedTypes,\n  } = window;\n\n  const ElementPrototype = Element.prototype;\n\n  const cloneNode = lookupGetter(ElementPrototype, 'cloneNode');\n  const remove = lookupGetter(ElementPrototype, 'remove');\n  const getNextSibling = lookupGetter(ElementPrototype, 'nextSibling');\n  const getChildNodes = lookupGetter(ElementPrototype, 'childNodes');\n  const getParentNode = lookupGetter(ElementPrototype, 'parentNode');\n\n  // As per issue #47, the web-components registry is inherited by a\n  // new document created via createHTMLDocument. As per the spec\n  // (http://w3c.github.io/webcomponents/spec/custom/#creating-and-passing-registries)\n  // a new empty registry is used when creating a template contents owner\n  // document, so we use that as our parent document to ensure nothing\n  // is inherited.\n  if (typeof HTMLTemplateElement === 'function') {\n    const template = document.createElement('template');\n    if (template.content && template.content.ownerDocument) {\n      document = template.content.ownerDocument;\n    }\n  }\n\n  let trustedTypesPolicy;\n  let emptyHTML = '';\n\n  const {\n    implementation,\n    createNodeIterator,\n    createDocumentFragment,\n    getElementsByTagName,\n  } = document;\n  const { importNode } = originalDocument;\n\n  let hooks = _createHooksMap();\n\n  /**\n   * Expose whether this browser supports running the full DOMPurify.\n   */\n  DOMPurify.isSupported =\n    typeof entries === 'function' &&\n    typeof getParentNode === 'function' &&\n    implementation &&\n    implementation.createHTMLDocument !== undefined;\n\n  const {\n    MUSTACHE_EXPR,\n    ERB_EXPR,\n    TMPLIT_EXPR,\n    DATA_ATTR,\n    ARIA_ATTR,\n    IS_SCRIPT_OR_DATA,\n    ATTR_WHITESPACE,\n    CUSTOM_ELEMENT,\n  } = EXPRESSIONS;\n\n  let { IS_ALLOWED_URI } = EXPRESSIONS;\n\n  /**\n   * We consider the elements and attributes below to be safe. Ideally\n   * don't add any new ones but feel free to remove unwanted ones.\n   */\n\n  /* allowed element names */\n  let ALLOWED_TAGS = null;\n  const DEFAULT_ALLOWED_TAGS = addToSet({}, [\n    ...TAGS.html,\n    ...TAGS.svg,\n    ...TAGS.svgFilters,\n    ...TAGS.mathMl,\n    ...TAGS.text,\n  ]);\n\n  /* Allowed attribute names */\n  let ALLOWED_ATTR = null;\n  const DEFAULT_ALLOWED_ATTR = addToSet({}, [\n    ...ATTRS.html,\n    ...ATTRS.svg,\n    ...ATTRS.mathMl,\n    ...ATTRS.xml,\n  ]);\n\n  /*\n   * Configure how DOMPurify should handle custom elements and their attributes as well as customized built-in elements.\n   * @property {RegExp|Function|null} tagNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any custom elements)\n   * @property {RegExp|Function|null} attributeNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any attributes not on the allow list)\n   * @property {boolean} allowCustomizedBuiltInElements allow custom elements derived from built-ins if they pass CUSTOM_ELEMENT_HANDLING.tagNameCheck. Default: `false`.\n   */\n  let CUSTOM_ELEMENT_HANDLING = Object.seal(\n    create(null, {\n      tagNameCheck: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: null,\n      },\n      attributeNameCheck: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: null,\n      },\n      allowCustomizedBuiltInElements: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: false,\n      },\n    })\n  );\n\n  /* Explicitly forbidden tags (overrides ALLOWED_TAGS/ADD_TAGS) */\n  let FORBID_TAGS = null;\n\n  /* Explicitly forbidden attributes (overrides ALLOWED_ATTR/ADD_ATTR) */\n  let FORBID_ATTR = null;\n\n  /* Decide if ARIA attributes are okay */\n  let ALLOW_ARIA_ATTR = true;\n\n  /* Decide if custom data attributes are okay */\n  let ALLOW_DATA_ATTR = true;\n\n  /* Decide if unknown protocols are okay */\n  let ALLOW_UNKNOWN_PROTOCOLS = false;\n\n  /* Decide if self-closing tags in attributes are allowed.\n   * Usually removed due to a mXSS issue in jQuery 3.0 */\n  let ALLOW_SELF_CLOSE_IN_ATTR = true;\n\n  /* Output should be safe for common template engines.\n   * This means, DOMPurify removes data attributes, mustaches and ERB\n   */\n  let SAFE_FOR_TEMPLATES = false;\n\n  /* Output should be safe even for XML used within HTML and alike.\n   * This means, DOMPurify removes comments when containing risky content.\n   */\n  let SAFE_FOR_XML = true;\n\n  /* Decide if document with <html>... should be returned */\n  let WHOLE_DOCUMENT = false;\n\n  /* Track whether config is already set on this instance of DOMPurify. */\n  let SET_CONFIG = false;\n\n  /* Decide if all elements (e.g. style, script) must be children of\n   * document.body. By default, browsers might move them to document.head */\n  let FORCE_BODY = false;\n\n  /* Decide if a DOM `HTMLBodyElement` should be returned, instead of a html\n   * string (or a TrustedHTML object if Trusted Types are supported).\n   * If `WHOLE_DOCUMENT` is enabled a `HTMLHtmlElement` will be returned instead\n   */\n  let RETURN_DOM = false;\n\n  /* Decide if a DOM `DocumentFragment` should be returned, instead of a html\n   * string  (or a TrustedHTML object if Trusted Types are supported) */\n  let RETURN_DOM_FRAGMENT = false;\n\n  /* Try to return a Trusted Type object instead of a string, return a string in\n   * case Trusted Types are not supported  */\n  let RETURN_TRUSTED_TYPE = false;\n\n  /* Output should be free from DOM clobbering attacks?\n   * This sanitizes markups named with colliding, clobberable built-in DOM APIs.\n   */\n  let SANITIZE_DOM = true;\n\n  /* Achieve full DOM Clobbering protection by isolating the namespace of named\n   * properties and JS variables, mitigating attacks that abuse the HTML/DOM spec rules.\n   *\n   * HTML/DOM spec rules that enable DOM Clobbering:\n   *   - Named Access on Window (§7.3.3)\n   *   - DOM Tree Accessors (§3.1.5)\n   *   - Form Element Parent-Child Relations (§4.10.3)\n   *   - Iframe srcdoc / Nested WindowProxies (§4.8.5)\n   *   - HTMLCollection (§4.2.10.2)\n   *\n   * Namespace isolation is implemented by prefixing `id` and `name` attributes\n   * with a constant string, i.e., `user-content-`\n   */\n  let SANITIZE_NAMED_PROPS = false;\n  const SANITIZE_NAMED_PROPS_PREFIX = 'user-content-';\n\n  /* Keep element content when removing element? */\n  let KEEP_CONTENT = true;\n\n  /* If a `Node` is passed to sanitize(), then performs sanitization in-place instead\n   * of importing it into a new Document and returning a sanitized copy */\n  let IN_PLACE = false;\n\n  /* Allow usage of profiles like html, svg and mathMl */\n  let USE_PROFILES: UseProfilesConfig | false = {};\n\n  /* Tags to ignore content of when KEEP_CONTENT is true */\n  let FORBID_CONTENTS = null;\n  const DEFAULT_FORBID_CONTENTS = addToSet({}, [\n    'annotation-xml',\n    'audio',\n    'colgroup',\n    'desc',\n    'foreignobject',\n    'head',\n    'iframe',\n    'math',\n    'mi',\n    'mn',\n    'mo',\n    'ms',\n    'mtext',\n    'noembed',\n    'noframes',\n    'noscript',\n    'plaintext',\n    'script',\n    'style',\n    'svg',\n    'template',\n    'thead',\n    'title',\n    'video',\n    'xmp',\n  ]);\n\n  /* Tags that are safe for data: URIs */\n  let DATA_URI_TAGS = null;\n  const DEFAULT_DATA_URI_TAGS = addToSet({}, [\n    'audio',\n    'video',\n    'img',\n    'source',\n    'image',\n    'track',\n  ]);\n\n  /* Attributes safe for values like \"javascript:\" */\n  let URI_SAFE_ATTRIBUTES = null;\n  const DEFAULT_URI_SAFE_ATTRIBUTES = addToSet({}, [\n    'alt',\n    'class',\n    'for',\n    'id',\n    'label',\n    'name',\n    'pattern',\n    'placeholder',\n    'role',\n    'summary',\n    'title',\n    'value',\n    'style',\n    'xmlns',\n  ]);\n\n  const MATHML_NAMESPACE = 'http://www.w3.org/1998/Math/MathML';\n  const SVG_NAMESPACE = 'http://www.w3.org/2000/svg';\n  const HTML_NAMESPACE = 'http://www.w3.org/1999/xhtml';\n  /* Document namespace */\n  let NAMESPACE = HTML_NAMESPACE;\n  let IS_EMPTY_INPUT = false;\n\n  /* Allowed XHTML+XML namespaces */\n  let ALLOWED_NAMESPACES = null;\n  const DEFAULT_ALLOWED_NAMESPACES = addToSet(\n    {},\n    [MATHML_NAMESPACE, SVG_NAMESPACE, HTML_NAMESPACE],\n    stringToString\n  );\n\n  let MATHML_TEXT_INTEGRATION_POINTS = addToSet({}, [\n    'mi',\n    'mo',\n    'mn',\n    'ms',\n    'mtext',\n  ]);\n\n  let HTML_INTEGRATION_POINTS = addToSet({}, ['annotation-xml']);\n\n  // Certain elements are allowed in both SVG and HTML\n  // namespace. We need to specify them explicitly\n  // so that they don't get erroneously deleted from\n  // HTML namespace.\n  const COMMON_SVG_AND_HTML_ELEMENTS = addToSet({}, [\n    'title',\n    'style',\n    'font',\n    'a',\n    'script',\n  ]);\n\n  /* Parsing of strict XHTML documents */\n  let PARSER_MEDIA_TYPE: null | DOMParserSupportedType = null;\n  const SUPPORTED_PARSER_MEDIA_TYPES = ['application/xhtml+xml', 'text/html'];\n  const DEFAULT_PARSER_MEDIA_TYPE = 'text/html';\n  let transformCaseFunc: null | Parameters<typeof addToSet>[2] = null;\n\n  /* Keep a reference to config to pass to hooks */\n  let CONFIG: Config | null = null;\n\n  /* Ideally, do not touch anything below this line */\n  /* ______________________________________________ */\n\n  const formElement = document.createElement('form');\n\n  const isRegexOrFunction = function (\n    testValue: unknown\n  ): testValue is Function | RegExp {\n    return testValue instanceof RegExp || testValue instanceof Function;\n  };\n\n  /**\n   * _parseConfig\n   *\n   * @param cfg optional config literal\n   */\n  // eslint-disable-next-line complexity\n  const _parseConfig = function (cfg: Config = {}): void {\n    if (CONFIG && CONFIG === cfg) {\n      return;\n    }\n\n    /* Shield configuration object from tampering */\n    if (!cfg || typeof cfg !== 'object') {\n      cfg = {};\n    }\n\n    /* Shield configuration object from prototype pollution */\n    cfg = clone(cfg);\n\n    PARSER_MEDIA_TYPE =\n      // eslint-disable-next-line unicorn/prefer-includes\n      SUPPORTED_PARSER_MEDIA_TYPES.indexOf(cfg.PARSER_MEDIA_TYPE) === -1\n        ? DEFAULT_PARSER_MEDIA_TYPE\n        : cfg.PARSER_MEDIA_TYPE;\n\n    // HTML tags and attributes are not case-sensitive, converting to lowercase. Keeping XHTML as is.\n    transformCaseFunc =\n      PARSER_MEDIA_TYPE === 'application/xhtml+xml'\n        ? stringToString\n        : stringToLowerCase;\n\n    /* Set configuration parameters */\n    ALLOWED_TAGS = objectHasOwnProperty(cfg, 'ALLOWED_TAGS')\n      ? addToSet({}, cfg.ALLOWED_TAGS, transformCaseFunc)\n      : DEFAULT_ALLOWED_TAGS;\n    ALLOWED_ATTR = objectHasOwnProperty(cfg, 'ALLOWED_ATTR')\n      ? addToSet({}, cfg.ALLOWED_ATTR, transformCaseFunc)\n      : DEFAULT_ALLOWED_ATTR;\n    ALLOWED_NAMESPACES = objectHasOwnProperty(cfg, 'ALLOWED_NAMESPACES')\n      ? addToSet({}, cfg.ALLOWED_NAMESPACES, stringToString)\n      : DEFAULT_ALLOWED_NAMESPACES;\n    URI_SAFE_ATTRIBUTES = objectHasOwnProperty(cfg, 'ADD_URI_SAFE_ATTR')\n      ? addToSet(\n          clone(DEFAULT_URI_SAFE_ATTRIBUTES),\n          cfg.ADD_URI_SAFE_ATTR,\n          transformCaseFunc\n        )\n      : DEFAULT_URI_SAFE_ATTRIBUTES;\n    DATA_URI_TAGS = objectHasOwnProperty(cfg, 'ADD_DATA_URI_TAGS')\n      ? addToSet(\n          clone(DEFAULT_DATA_URI_TAGS),\n          cfg.ADD_DATA_URI_TAGS,\n          transformCaseFunc\n        )\n      : DEFAULT_DATA_URI_TAGS;\n    FORBID_CONTENTS = objectHasOwnProperty(cfg, 'FORBID_CONTENTS')\n      ? addToSet({}, cfg.FORBID_CONTENTS, transformCaseFunc)\n      : DEFAULT_FORBID_CONTENTS;\n    FORBID_TAGS = objectHasOwnProperty(cfg, 'FORBID_TAGS')\n      ? addToSet({}, cfg.FORBID_TAGS, transformCaseFunc)\n      : clone({});\n    FORBID_ATTR = objectHasOwnProperty(cfg, 'FORBID_ATTR')\n      ? addToSet({}, cfg.FORBID_ATTR, transformCaseFunc)\n      : clone({});\n    USE_PROFILES = objectHasOwnProperty(cfg, 'USE_PROFILES')\n      ? cfg.USE_PROFILES\n      : false;\n    ALLOW_ARIA_ATTR = cfg.ALLOW_ARIA_ATTR !== false; // Default true\n    ALLOW_DATA_ATTR = cfg.ALLOW_DATA_ATTR !== false; // Default true\n    ALLOW_UNKNOWN_PROTOCOLS = cfg.ALLOW_UNKNOWN_PROTOCOLS || false; // Default false\n    ALLOW_SELF_CLOSE_IN_ATTR = cfg.ALLOW_SELF_CLOSE_IN_ATTR !== false; // Default true\n    SAFE_FOR_TEMPLATES = cfg.SAFE_FOR_TEMPLATES || false; // Default false\n    SAFE_FOR_XML = cfg.SAFE_FOR_XML !== false; // Default true\n    WHOLE_DOCUMENT = cfg.WHOLE_DOCUMENT || false; // Default false\n    RETURN_DOM = cfg.RETURN_DOM || false; // Default false\n    RETURN_DOM_FRAGMENT = cfg.RETURN_DOM_FRAGMENT || false; // Default false\n    RETURN_TRUSTED_TYPE = cfg.RETURN_TRUSTED_TYPE || false; // Default false\n    FORCE_BODY = cfg.FORCE_BODY || false; // Default false\n    SANITIZE_DOM = cfg.SANITIZE_DOM !== false; // Default true\n    SANITIZE_NAMED_PROPS = cfg.SANITIZE_NAMED_PROPS || false; // Default false\n    KEEP_CONTENT = cfg.KEEP_CONTENT !== false; // Default true\n    IN_PLACE = cfg.IN_PLACE || false; // Default false\n    IS_ALLOWED_URI = cfg.ALLOWED_URI_REGEXP || EXPRESSIONS.IS_ALLOWED_URI;\n    NAMESPACE = cfg.NAMESPACE || HTML_NAMESPACE;\n    MATHML_TEXT_INTEGRATION_POINTS =\n      cfg.MATHML_TEXT_INTEGRATION_POINTS || MATHML_TEXT_INTEGRATION_POINTS;\n    HTML_INTEGRATION_POINTS =\n      cfg.HTML_INTEGRATION_POINTS || HTML_INTEGRATION_POINTS;\n\n    CUSTOM_ELEMENT_HANDLING = cfg.CUSTOM_ELEMENT_HANDLING || {};\n    if (\n      cfg.CUSTOM_ELEMENT_HANDLING &&\n      isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck)\n    ) {\n      CUSTOM_ELEMENT_HANDLING.tagNameCheck =\n        cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck;\n    }\n\n    if (\n      cfg.CUSTOM_ELEMENT_HANDLING &&\n      isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)\n    ) {\n      CUSTOM_ELEMENT_HANDLING.attributeNameCheck =\n        cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck;\n    }\n\n    if (\n      cfg.CUSTOM_ELEMENT_HANDLING &&\n      typeof cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements ===\n        'boolean'\n    ) {\n      CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements =\n        cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements;\n    }\n\n    if (SAFE_FOR_TEMPLATES) {\n      ALLOW_DATA_ATTR = false;\n    }\n\n    if (RETURN_DOM_FRAGMENT) {\n      RETURN_DOM = true;\n    }\n\n    /* Parse profile info */\n    if (USE_PROFILES) {\n      ALLOWED_TAGS = addToSet({}, TAGS.text);\n      ALLOWED_ATTR = [];\n      if (USE_PROFILES.html === true) {\n        addToSet(ALLOWED_TAGS, TAGS.html);\n        addToSet(ALLOWED_ATTR, ATTRS.html);\n      }\n\n      if (USE_PROFILES.svg === true) {\n        addToSet(ALLOWED_TAGS, TAGS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n\n      if (USE_PROFILES.svgFilters === true) {\n        addToSet(ALLOWED_TAGS, TAGS.svgFilters);\n        addToSet(ALLOWED_ATTR, ATTRS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n\n      if (USE_PROFILES.mathMl === true) {\n        addToSet(ALLOWED_TAGS, TAGS.mathMl);\n        addToSet(ALLOWED_ATTR, ATTRS.mathMl);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n    }\n\n    /* Merge configuration parameters */\n    if (cfg.ADD_TAGS) {\n      if (ALLOWED_TAGS === DEFAULT_ALLOWED_TAGS) {\n        ALLOWED_TAGS = clone(ALLOWED_TAGS);\n      }\n\n      addToSet(ALLOWED_TAGS, cfg.ADD_TAGS, transformCaseFunc);\n    }\n\n    if (cfg.ADD_ATTR) {\n      if (ALLOWED_ATTR === DEFAULT_ALLOWED_ATTR) {\n        ALLOWED_ATTR = clone(ALLOWED_ATTR);\n      }\n\n      addToSet(ALLOWED_ATTR, cfg.ADD_ATTR, transformCaseFunc);\n    }\n\n    if (cfg.ADD_URI_SAFE_ATTR) {\n      addToSet(URI_SAFE_ATTRIBUTES, cfg.ADD_URI_SAFE_ATTR, transformCaseFunc);\n    }\n\n    if (cfg.FORBID_CONTENTS) {\n      if (FORBID_CONTENTS === DEFAULT_FORBID_CONTENTS) {\n        FORBID_CONTENTS = clone(FORBID_CONTENTS);\n      }\n\n      addToSet(FORBID_CONTENTS, cfg.FORBID_CONTENTS, transformCaseFunc);\n    }\n\n    /* Add #text in case KEEP_CONTENT is set to true */\n    if (KEEP_CONTENT) {\n      ALLOWED_TAGS['#text'] = true;\n    }\n\n    /* Add html, head and body to ALLOWED_TAGS in case WHOLE_DOCUMENT is true */\n    if (WHOLE_DOCUMENT) {\n      addToSet(ALLOWED_TAGS, ['html', 'head', 'body']);\n    }\n\n    /* Add tbody to ALLOWED_TAGS in case tables are permitted, see #286, #365 */\n    if (ALLOWED_TAGS.table) {\n      addToSet(ALLOWED_TAGS, ['tbody']);\n      delete FORBID_TAGS.tbody;\n    }\n\n    if (cfg.TRUSTED_TYPES_POLICY) {\n      if (typeof cfg.TRUSTED_TYPES_POLICY.createHTML !== 'function') {\n        throw typeErrorCreate(\n          'TRUSTED_TYPES_POLICY configuration option must provide a \"createHTML\" hook.'\n        );\n      }\n\n      if (typeof cfg.TRUSTED_TYPES_POLICY.createScriptURL !== 'function') {\n        throw typeErrorCreate(\n          'TRUSTED_TYPES_POLICY configuration option must provide a \"createScriptURL\" hook.'\n        );\n      }\n\n      // Overwrite existing TrustedTypes policy.\n      trustedTypesPolicy = cfg.TRUSTED_TYPES_POLICY;\n\n      // Sign local variables required by `sanitize`.\n      emptyHTML = trustedTypesPolicy.createHTML('');\n    } else {\n      // Uninitialized policy, attempt to initialize the internal dompurify policy.\n      if (trustedTypesPolicy === undefined) {\n        trustedTypesPolicy = _createTrustedTypesPolicy(\n          trustedTypes,\n          currentScript\n        );\n      }\n\n      // If creating the internal policy succeeded sign internal variables.\n      if (trustedTypesPolicy !== null && typeof emptyHTML === 'string') {\n        emptyHTML = trustedTypesPolicy.createHTML('');\n      }\n    }\n\n    // Prevent further manipulation of configuration.\n    // Not available in IE8, Safari 5, etc.\n    if (freeze) {\n      freeze(cfg);\n    }\n\n    CONFIG = cfg;\n  };\n\n  /* Keep track of all possible SVG and MathML tags\n   * so that we can perform the namespace checks\n   * correctly. */\n  const ALL_SVG_TAGS = addToSet({}, [\n    ...TAGS.svg,\n    ...TAGS.svgFilters,\n    ...TAGS.svgDisallowed,\n  ]);\n  const ALL_MATHML_TAGS = addToSet({}, [\n    ...TAGS.mathMl,\n    ...TAGS.mathMlDisallowed,\n  ]);\n\n  /**\n   * @param element a DOM element whose namespace is being checked\n   * @returns Return false if the element has a\n   *  namespace that a spec-compliant parser would never\n   *  return. Return true otherwise.\n   */\n  const _checkValidNamespace = function (element: Element): boolean {\n    let parent = getParentNode(element);\n\n    // In JSDOM, if we're inside shadow DOM, then parentNode\n    // can be null. We just simulate parent in this case.\n    if (!parent || !parent.tagName) {\n      parent = {\n        namespaceURI: NAMESPACE,\n        tagName: 'template',\n      };\n    }\n\n    const tagName = stringToLowerCase(element.tagName);\n    const parentTagName = stringToLowerCase(parent.tagName);\n\n    if (!ALLOWED_NAMESPACES[element.namespaceURI]) {\n      return false;\n    }\n\n    if (element.namespaceURI === SVG_NAMESPACE) {\n      // The only way to switch from HTML namespace to SVG\n      // is via <svg>. If it happens via any other tag, then\n      // it should be killed.\n      if (parent.namespaceURI === HTML_NAMESPACE) {\n        return tagName === 'svg';\n      }\n\n      // The only way to switch from MathML to SVG is via`\n      // svg if parent is either <annotation-xml> or MathML\n      // text integration points.\n      if (parent.namespaceURI === MATHML_NAMESPACE) {\n        return (\n          tagName === 'svg' &&\n          (parentTagName === 'annotation-xml' ||\n            MATHML_TEXT_INTEGRATION_POINTS[parentTagName])\n        );\n      }\n\n      // We only allow elements that are defined in SVG\n      // spec. All others are disallowed in SVG namespace.\n      return Boolean(ALL_SVG_TAGS[tagName]);\n    }\n\n    if (element.namespaceURI === MATHML_NAMESPACE) {\n      // The only way to switch from HTML namespace to MathML\n      // is via <math>. If it happens via any other tag, then\n      // it should be killed.\n      if (parent.namespaceURI === HTML_NAMESPACE) {\n        return tagName === 'math';\n      }\n\n      // The only way to switch from SVG to MathML is via\n      // <math> and HTML integration points\n      if (parent.namespaceURI === SVG_NAMESPACE) {\n        return tagName === 'math' && HTML_INTEGRATION_POINTS[parentTagName];\n      }\n\n      // We only allow elements that are defined in MathML\n      // spec. All others are disallowed in MathML namespace.\n      return Boolean(ALL_MATHML_TAGS[tagName]);\n    }\n\n    if (element.namespaceURI === HTML_NAMESPACE) {\n      // The only way to switch from SVG to HTML is via\n      // HTML integration points, and from MathML to HTML\n      // is via MathML text integration points\n      if (\n        parent.namespaceURI === SVG_NAMESPACE &&\n        !HTML_INTEGRATION_POINTS[parentTagName]\n      ) {\n        return false;\n      }\n\n      if (\n        parent.namespaceURI === MATHML_NAMESPACE &&\n        !MATHML_TEXT_INTEGRATION_POINTS[parentTagName]\n      ) {\n        return false;\n      }\n\n      // We disallow tags that are specific for MathML\n      // or SVG and should never appear in HTML namespace\n      return (\n        !ALL_MATHML_TAGS[tagName] &&\n        (COMMON_SVG_AND_HTML_ELEMENTS[tagName] || !ALL_SVG_TAGS[tagName])\n      );\n    }\n\n    // For XHTML and XML documents that support custom namespaces\n    if (\n      PARSER_MEDIA_TYPE === 'application/xhtml+xml' &&\n      ALLOWED_NAMESPACES[element.namespaceURI]\n    ) {\n      return true;\n    }\n\n    // The code should never reach this place (this means\n    // that the element somehow got namespace that is not\n    // HTML, SVG, MathML or allowed via ALLOWED_NAMESPACES).\n    // Return false just in case.\n    return false;\n  };\n\n  /**\n   * _forceRemove\n   *\n   * @param node a DOM node\n   */\n  const _forceRemove = function (node: Node): void {\n    arrayPush(DOMPurify.removed, { element: node });\n\n    try {\n      // eslint-disable-next-line unicorn/prefer-dom-node-remove\n      getParentNode(node).removeChild(node);\n    } catch (_) {\n      remove(node);\n    }\n  };\n\n  /**\n   * _removeAttribute\n   *\n   * @param name an Attribute name\n   * @param element a DOM node\n   */\n  const _removeAttribute = function (name: string, element: Element): void {\n    try {\n      arrayPush(DOMPurify.removed, {\n        attribute: element.getAttributeNode(name),\n        from: element,\n      });\n    } catch (_) {\n      arrayPush(DOMPurify.removed, {\n        attribute: null,\n        from: element,\n      });\n    }\n\n    element.removeAttribute(name);\n\n    // We void attribute values for unremovable \"is\" attributes\n    if (name === 'is') {\n      if (RETURN_DOM || RETURN_DOM_FRAGMENT) {\n        try {\n          _forceRemove(element);\n        } catch (_) {}\n      } else {\n        try {\n          element.setAttribute(name, '');\n        } catch (_) {}\n      }\n    }\n  };\n\n  /**\n   * _initDocument\n   *\n   * @param dirty - a string of dirty markup\n   * @return a DOM, filled with the dirty markup\n   */\n  const _initDocument = function (dirty: string): Document {\n    /* Create a HTML document */\n    let doc = null;\n    let leadingWhitespace = null;\n\n    if (FORCE_BODY) {\n      dirty = '<remove></remove>' + dirty;\n    } else {\n      /* If FORCE_BODY isn't used, leading whitespace needs to be preserved manually */\n      const matches = stringMatch(dirty, /^[\\r\\n\\t ]+/);\n      leadingWhitespace = matches && matches[0];\n    }\n\n    if (\n      PARSER_MEDIA_TYPE === 'application/xhtml+xml' &&\n      NAMESPACE === HTML_NAMESPACE\n    ) {\n      // Root of XHTML doc must contain xmlns declaration (see https://www.w3.org/TR/xhtml1/normative.html#strict)\n      dirty =\n        '<html xmlns=\"http://www.w3.org/1999/xhtml\"><head></head><body>' +\n        dirty +\n        '</body></html>';\n    }\n\n    const dirtyPayload = trustedTypesPolicy\n      ? trustedTypesPolicy.createHTML(dirty)\n      : dirty;\n    /*\n     * Use the DOMParser API by default, fallback later if needs be\n     * DOMParser not work for svg when has multiple root element.\n     */\n    if (NAMESPACE === HTML_NAMESPACE) {\n      try {\n        doc = new DOMParser().parseFromString(dirtyPayload, PARSER_MEDIA_TYPE);\n      } catch (_) {}\n    }\n\n    /* Use createHTMLDocument in case DOMParser is not available */\n    if (!doc || !doc.documentElement) {\n      doc = implementation.createDocument(NAMESPACE, 'template', null);\n      try {\n        doc.documentElement.innerHTML = IS_EMPTY_INPUT\n          ? emptyHTML\n          : dirtyPayload;\n      } catch (_) {\n        // Syntax error if dirtyPayload is invalid xml\n      }\n    }\n\n    const body = doc.body || doc.documentElement;\n\n    if (dirty && leadingWhitespace) {\n      body.insertBefore(\n        document.createTextNode(leadingWhitespace),\n        body.childNodes[0] || null\n      );\n    }\n\n    /* Work on whole document or just its body */\n    if (NAMESPACE === HTML_NAMESPACE) {\n      return getElementsByTagName.call(\n        doc,\n        WHOLE_DOCUMENT ? 'html' : 'body'\n      )[0];\n    }\n\n    return WHOLE_DOCUMENT ? doc.documentElement : body;\n  };\n\n  /**\n   * Creates a NodeIterator object that you can use to traverse filtered lists of nodes or elements in a document.\n   *\n   * @param root The root element or node to start traversing on.\n   * @return The created NodeIterator\n   */\n  const _createNodeIterator = function (root: Node): NodeIterator {\n    return createNodeIterator.call(\n      root.ownerDocument || root,\n      root,\n      // eslint-disable-next-line no-bitwise\n      NodeFilter.SHOW_ELEMENT |\n        NodeFilter.SHOW_COMMENT |\n        NodeFilter.SHOW_TEXT |\n        NodeFilter.SHOW_PROCESSING_INSTRUCTION |\n        NodeFilter.SHOW_CDATA_SECTION,\n      null\n    );\n  };\n\n  /**\n   * _isClobbered\n   *\n   * @param element element to check for clobbering attacks\n   * @return true if clobbered, false if safe\n   */\n  const _isClobbered = function (element: Element): boolean {\n    return (\n      element instanceof HTMLFormElement &&\n      (typeof element.nodeName !== 'string' ||\n        typeof element.textContent !== 'string' ||\n        typeof element.removeChild !== 'function' ||\n        !(element.attributes instanceof NamedNodeMap) ||\n        typeof element.removeAttribute !== 'function' ||\n        typeof element.setAttribute !== 'function' ||\n        typeof element.namespaceURI !== 'string' ||\n        typeof element.insertBefore !== 'function' ||\n        typeof element.hasChildNodes !== 'function')\n    );\n  };\n\n  /**\n   * Checks whether the given object is a DOM node.\n   *\n   * @param value object to check whether it's a DOM node\n   * @return true is object is a DOM node\n   */\n  const _isNode = function (value: unknown): value is Node {\n    return typeof Node === 'function' && value instanceof Node;\n  };\n\n  function _executeHooks<\n    T extends\n      | NodeHook\n      | ElementHook\n      | DocumentFragmentHook\n      | UponSanitizeElementHook\n      | UponSanitizeAttributeHook\n  >(hooks: T[], currentNode: Parameters<T>[0], data: Parameters<T>[1]): void {\n    arrayForEach(hooks, (hook) => {\n      hook.call(DOMPurify, currentNode, data, CONFIG);\n    });\n  }\n\n  /**\n   * _sanitizeElements\n   *\n   * @protect nodeName\n   * @protect textContent\n   * @protect removeChild\n   * @param currentNode to check for permission to exist\n   * @return true if node was killed, false if left alive\n   */\n  const _sanitizeElements = function (currentNode: any): boolean {\n    let content = null;\n\n    /* Execute a hook if present */\n    _executeHooks(hooks.beforeSanitizeElements, currentNode, null);\n\n    /* Check if element is clobbered or can clobber */\n    if (_isClobbered(currentNode)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Now let's check the element's type and name */\n    const tagName = transformCaseFunc(currentNode.nodeName);\n\n    /* Execute a hook if present */\n    _executeHooks(hooks.uponSanitizeElement, currentNode, {\n      tagName,\n      allowedTags: ALLOWED_TAGS,\n    });\n\n    /* Detect mXSS attempts abusing namespace confusion */\n    if (\n      SAFE_FOR_XML &&\n      currentNode.hasChildNodes() &&\n      !_isNode(currentNode.firstElementChild) &&\n      regExpTest(/<[/\\w!]/g, currentNode.innerHTML) &&\n      regExpTest(/<[/\\w!]/g, currentNode.textContent)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Remove any occurrence of processing instructions */\n    if (currentNode.nodeType === NODE_TYPE.progressingInstruction) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Remove any kind of possibly harmful comments */\n    if (\n      SAFE_FOR_XML &&\n      currentNode.nodeType === NODE_TYPE.comment &&\n      regExpTest(/<[/\\w]/g, currentNode.data)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Remove element if anything forbids its presence */\n    if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n      /* Check if we have a custom element to handle */\n      if (!FORBID_TAGS[tagName] && _isBasicCustomElement(tagName)) {\n        if (\n          CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp &&\n          regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, tagName)\n        ) {\n          return false;\n        }\n\n        if (\n          CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function &&\n          CUSTOM_ELEMENT_HANDLING.tagNameCheck(tagName)\n        ) {\n          return false;\n        }\n      }\n\n      /* Keep content except for bad-listed elements */\n      if (KEEP_CONTENT && !FORBID_CONTENTS[tagName]) {\n        const parentNode = getParentNode(currentNode) || currentNode.parentNode;\n        const childNodes = getChildNodes(currentNode) || currentNode.childNodes;\n\n        if (childNodes && parentNode) {\n          const childCount = childNodes.length;\n\n          for (let i = childCount - 1; i >= 0; --i) {\n            const childClone = cloneNode(childNodes[i], true);\n            childClone.__removalCount = (currentNode.__removalCount || 0) + 1;\n            parentNode.insertBefore(childClone, getNextSibling(currentNode));\n          }\n        }\n      }\n\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Check whether element has a valid namespace */\n    if (currentNode instanceof Element && !_checkValidNamespace(currentNode)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Make sure that older browsers don't get fallback-tag mXSS */\n    if (\n      (tagName === 'noscript' ||\n        tagName === 'noembed' ||\n        tagName === 'noframes') &&\n      regExpTest(/<\\/no(script|embed|frames)/i, currentNode.innerHTML)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Sanitize element content to be template-safe */\n    if (SAFE_FOR_TEMPLATES && currentNode.nodeType === NODE_TYPE.text) {\n      /* Get the element's text content */\n      content = currentNode.textContent;\n\n      arrayForEach([MUSTACHE_EXPR, ERB_EXPR, TMPLIT_EXPR], (expr) => {\n        content = stringReplace(content, expr, ' ');\n      });\n\n      if (currentNode.textContent !== content) {\n        arrayPush(DOMPurify.removed, { element: currentNode.cloneNode() });\n        currentNode.textContent = content;\n      }\n    }\n\n    /* Execute a hook if present */\n    _executeHooks(hooks.afterSanitizeElements, currentNode, null);\n\n    return false;\n  };\n\n  /**\n   * _isValidAttribute\n   *\n   * @param lcTag Lowercase tag name of containing element.\n   * @param lcName Lowercase attribute name.\n   * @param value Attribute value.\n   * @return Returns true if `value` is valid, otherwise false.\n   */\n  // eslint-disable-next-line complexity\n  const _isValidAttribute = function (\n    lcTag: string,\n    lcName: string,\n    value: string\n  ): boolean {\n    /* Make sure attribute cannot clobber */\n    if (\n      SANITIZE_DOM &&\n      (lcName === 'id' || lcName === 'name') &&\n      (value in document || value in formElement)\n    ) {\n      return false;\n    }\n\n    /* Allow valid data-* attributes: At least one character after \"-\"\n        (https://html.spec.whatwg.org/multipage/dom.html#embedding-custom-non-visible-data-with-the-data-*-attributes)\n        XML-compatible (https://html.spec.whatwg.org/multipage/infrastructure.html#xml-compatible and http://www.w3.org/TR/xml/#d0e804)\n        We don't need to check the value; it's always URI safe. */\n    if (\n      ALLOW_DATA_ATTR &&\n      !FORBID_ATTR[lcName] &&\n      regExpTest(DATA_ATTR, lcName)\n    ) {\n      // This attribute is safe\n    } else if (ALLOW_ARIA_ATTR && regExpTest(ARIA_ATTR, lcName)) {\n      // This attribute is safe\n      /* Otherwise, check the name is permitted */\n    } else if (!ALLOWED_ATTR[lcName] || FORBID_ATTR[lcName]) {\n      if (\n        // First condition does a very basic check if a) it's basically a valid custom element tagname AND\n        // b) if the tagName passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n        // and c) if the attribute name passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.attributeNameCheck\n        (_isBasicCustomElement(lcTag) &&\n          ((CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp &&\n            regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, lcTag)) ||\n            (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function &&\n              CUSTOM_ELEMENT_HANDLING.tagNameCheck(lcTag))) &&\n          ((CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof RegExp &&\n            regExpTest(CUSTOM_ELEMENT_HANDLING.attributeNameCheck, lcName)) ||\n            (CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof Function &&\n              CUSTOM_ELEMENT_HANDLING.attributeNameCheck(lcName)))) ||\n        // Alternative, second condition checks if it's an `is`-attribute, AND\n        // the value passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n        (lcName === 'is' &&\n          CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements &&\n          ((CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp &&\n            regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, value)) ||\n            (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function &&\n              CUSTOM_ELEMENT_HANDLING.tagNameCheck(value))))\n      ) {\n        // If user has supplied a regexp or function in CUSTOM_ELEMENT_HANDLING.tagNameCheck, we need to also allow derived custom elements using the same tagName test.\n        // Additionally, we need to allow attributes passing the CUSTOM_ELEMENT_HANDLING.attributeNameCheck user has configured, as custom elements can define these at their own discretion.\n      } else {\n        return false;\n      }\n      /* Check value is safe. First, is attr inert? If so, is safe */\n    } else if (URI_SAFE_ATTRIBUTES[lcName]) {\n      // This attribute is safe\n      /* Check no script, data or unknown possibly unsafe URI\n        unless we know URI values are safe for that attribute */\n    } else if (\n      regExpTest(IS_ALLOWED_URI, stringReplace(value, ATTR_WHITESPACE, ''))\n    ) {\n      // This attribute is safe\n      /* Keep image data URIs alive if src/xlink:href is allowed */\n      /* Further prevent gadget XSS for dynamically built script tags */\n    } else if (\n      (lcName === 'src' || lcName === 'xlink:href' || lcName === 'href') &&\n      lcTag !== 'script' &&\n      stringIndexOf(value, 'data:') === 0 &&\n      DATA_URI_TAGS[lcTag]\n    ) {\n      // This attribute is safe\n      /* Allow unknown protocols: This provides support for links that\n        are handled by protocol handlers which may be unknown ahead of\n        time, e.g. fb:, spotify: */\n    } else if (\n      ALLOW_UNKNOWN_PROTOCOLS &&\n      !regExpTest(IS_SCRIPT_OR_DATA, stringReplace(value, ATTR_WHITESPACE, ''))\n    ) {\n      // This attribute is safe\n      /* Check for binary attributes */\n    } else if (value) {\n      return false;\n    } else {\n      // Binary attributes are safe at this point\n      /* Anything else, presume unsafe, do not add it back */\n    }\n\n    return true;\n  };\n\n  /**\n   * _isBasicCustomElement\n   * checks if at least one dash is included in tagName, and it's not the first char\n   * for more sophisticated checking see https://github.com/sindresorhus/validate-element-name\n   *\n   * @param tagName name of the tag of the node to sanitize\n   * @returns Returns true if the tag name meets the basic criteria for a custom element, otherwise false.\n   */\n  const _isBasicCustomElement = function (tagName: string): RegExpMatchArray {\n    return tagName !== 'annotation-xml' && stringMatch(tagName, CUSTOM_ELEMENT);\n  };\n\n  /**\n   * _sanitizeAttributes\n   *\n   * @protect attributes\n   * @protect nodeName\n   * @protect removeAttribute\n   * @protect setAttribute\n   *\n   * @param currentNode to sanitize\n   */\n  const _sanitizeAttributes = function (currentNode: Element): void {\n    /* Execute a hook if present */\n    _executeHooks(hooks.beforeSanitizeAttributes, currentNode, null);\n\n    const { attributes } = currentNode;\n\n    /* Check if we have attributes; if not we might have a text node */\n    if (!attributes || _isClobbered(currentNode)) {\n      return;\n    }\n\n    const hookEvent = {\n      attrName: '',\n      attrValue: '',\n      keepAttr: true,\n      allowedAttributes: ALLOWED_ATTR,\n      forceKeepAttr: undefined,\n    };\n    let l = attributes.length;\n\n    /* Go backwards over all attributes; safely remove bad ones */\n    while (l--) {\n      const attr = attributes[l];\n      const { name, namespaceURI, value: attrValue } = attr;\n      const lcName = transformCaseFunc(name);\n\n      const initValue = attrValue;\n      let value = name === 'value' ? initValue : stringTrim(initValue);\n\n      /* Execute a hook if present */\n      hookEvent.attrName = lcName;\n      hookEvent.attrValue = value;\n      hookEvent.keepAttr = true;\n      hookEvent.forceKeepAttr = undefined; // Allows developers to see this is a property they can set\n      _executeHooks(hooks.uponSanitizeAttribute, currentNode, hookEvent);\n      value = hookEvent.attrValue;\n\n      /* Full DOM Clobbering protection via namespace isolation,\n       * Prefix id and name attributes with `user-content-`\n       */\n      if (SANITIZE_NAMED_PROPS && (lcName === 'id' || lcName === 'name')) {\n        // Remove the attribute with this value\n        _removeAttribute(name, currentNode);\n\n        // Prefix the value and later re-create the attribute with the sanitized value\n        value = SANITIZE_NAMED_PROPS_PREFIX + value;\n      }\n\n      /* Work around a security issue with comments inside attributes */\n      if (SAFE_FOR_XML && regExpTest(/((--!?|])>)|<\\/(style|title)/i, value)) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n\n      /* Did the hooks approve of the attribute? */\n      if (hookEvent.forceKeepAttr) {\n        continue;\n      }\n\n      /* Did the hooks approve of the attribute? */\n      if (!hookEvent.keepAttr) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n\n      /* Work around a security issue in jQuery 3.0 */\n      if (!ALLOW_SELF_CLOSE_IN_ATTR && regExpTest(/\\/>/i, value)) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n\n      /* Sanitize attribute content to be template-safe */\n      if (SAFE_FOR_TEMPLATES) {\n        arrayForEach([MUSTACHE_EXPR, ERB_EXPR, TMPLIT_EXPR], (expr) => {\n          value = stringReplace(value, expr, ' ');\n        });\n      }\n\n      /* Is `value` valid for this attribute? */\n      const lcTag = transformCaseFunc(currentNode.nodeName);\n      if (!_isValidAttribute(lcTag, lcName, value)) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n\n      /* Handle attributes that require Trusted Types */\n      if (\n        trustedTypesPolicy &&\n        typeof trustedTypes === 'object' &&\n        typeof trustedTypes.getAttributeType === 'function'\n      ) {\n        if (namespaceURI) {\n          /* Namespaces are not yet supported, see https://bugs.chromium.org/p/chromium/issues/detail?id=1305293 */\n        } else {\n          switch (trustedTypes.getAttributeType(lcTag, lcName)) {\n            case 'TrustedHTML': {\n              value = trustedTypesPolicy.createHTML(value);\n              break;\n            }\n\n            case 'TrustedScriptURL': {\n              value = trustedTypesPolicy.createScriptURL(value);\n              break;\n            }\n\n            default: {\n              break;\n            }\n          }\n        }\n      }\n\n      /* Handle invalid data-* attribute set by try-catching it */\n      if (value !== initValue) {\n        try {\n          if (namespaceURI) {\n            currentNode.setAttributeNS(namespaceURI, name, value);\n          } else {\n            /* Fallback to setAttribute() for browser-unrecognized namespaces e.g. \"x-schema\". */\n            currentNode.setAttribute(name, value);\n          }\n\n          if (_isClobbered(currentNode)) {\n            _forceRemove(currentNode);\n          } else {\n            arrayPop(DOMPurify.removed);\n          }\n        } catch (_) {\n          _removeAttribute(name, currentNode);\n        }\n      }\n    }\n\n    /* Execute a hook if present */\n    _executeHooks(hooks.afterSanitizeAttributes, currentNode, null);\n  };\n\n  /**\n   * _sanitizeShadowDOM\n   *\n   * @param fragment to iterate over recursively\n   */\n  const _sanitizeShadowDOM = function (fragment: DocumentFragment): void {\n    let shadowNode = null;\n    const shadowIterator = _createNodeIterator(fragment);\n\n    /* Execute a hook if present */\n    _executeHooks(hooks.beforeSanitizeShadowDOM, fragment, null);\n\n    while ((shadowNode = shadowIterator.nextNode())) {\n      /* Execute a hook if present */\n      _executeHooks(hooks.uponSanitizeShadowNode, shadowNode, null);\n\n      /* Sanitize tags and elements */\n      _sanitizeElements(shadowNode);\n\n      /* Check attributes next */\n      _sanitizeAttributes(shadowNode);\n\n      /* Deep shadow DOM detected */\n      if (shadowNode.content instanceof DocumentFragment) {\n        _sanitizeShadowDOM(shadowNode.content);\n      }\n    }\n\n    /* Execute a hook if present */\n    _executeHooks(hooks.afterSanitizeShadowDOM, fragment, null);\n  };\n\n  // eslint-disable-next-line complexity\n  DOMPurify.sanitize = function (dirty, cfg = {}) {\n    let body = null;\n    let importedNode = null;\n    let currentNode = null;\n    let returnNode = null;\n    /* Make sure we have a string to sanitize.\n      DO NOT return early, as this will return the wrong type if\n      the user has requested a DOM object rather than a string */\n    IS_EMPTY_INPUT = !dirty;\n    if (IS_EMPTY_INPUT) {\n      dirty = '<!-->';\n    }\n\n    /* Stringify, in case dirty is an object */\n    if (typeof dirty !== 'string' && !_isNode(dirty)) {\n      if (typeof dirty.toString === 'function') {\n        dirty = dirty.toString();\n        if (typeof dirty !== 'string') {\n          throw typeErrorCreate('dirty is not a string, aborting');\n        }\n      } else {\n        throw typeErrorCreate('toString is not a function');\n      }\n    }\n\n    /* Return dirty HTML if DOMPurify cannot run */\n    if (!DOMPurify.isSupported) {\n      return dirty;\n    }\n\n    /* Assign config vars */\n    if (!SET_CONFIG) {\n      _parseConfig(cfg);\n    }\n\n    /* Clean up removed elements */\n    DOMPurify.removed = [];\n\n    /* Check if dirty is correctly typed for IN_PLACE */\n    if (typeof dirty === 'string') {\n      IN_PLACE = false;\n    }\n\n    if (IN_PLACE) {\n      /* Do some early pre-sanitization to avoid unsafe root nodes */\n      if ((dirty as Node).nodeName) {\n        const tagName = transformCaseFunc((dirty as Node).nodeName);\n        if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n          throw typeErrorCreate(\n            'root node is forbidden and cannot be sanitized in-place'\n          );\n        }\n      }\n    } else if (dirty instanceof Node) {\n      /* If dirty is a DOM element, append to an empty document to avoid\n         elements being stripped by the parser */\n      body = _initDocument('<!---->');\n      importedNode = body.ownerDocument.importNode(dirty, true);\n      if (\n        importedNode.nodeType === NODE_TYPE.element &&\n        importedNode.nodeName === 'BODY'\n      ) {\n        /* Node is already a body, use as is */\n        body = importedNode;\n      } else if (importedNode.nodeName === 'HTML') {\n        body = importedNode;\n      } else {\n        // eslint-disable-next-line unicorn/prefer-dom-node-append\n        body.appendChild(importedNode);\n      }\n    } else {\n      /* Exit directly if we have nothing to do */\n      if (\n        !RETURN_DOM &&\n        !SAFE_FOR_TEMPLATES &&\n        !WHOLE_DOCUMENT &&\n        // eslint-disable-next-line unicorn/prefer-includes\n        dirty.indexOf('<') === -1\n      ) {\n        return trustedTypesPolicy && RETURN_TRUSTED_TYPE\n          ? trustedTypesPolicy.createHTML(dirty)\n          : dirty;\n      }\n\n      /* Initialize the document to work on */\n      body = _initDocument(dirty);\n\n      /* Check we have a DOM node from the data */\n      if (!body) {\n        return RETURN_DOM ? null : RETURN_TRUSTED_TYPE ? emptyHTML : '';\n      }\n    }\n\n    /* Remove first element node (ours) if FORCE_BODY is set */\n    if (body && FORCE_BODY) {\n      _forceRemove(body.firstChild);\n    }\n\n    /* Get node iterator */\n    const nodeIterator = _createNodeIterator(IN_PLACE ? dirty : body);\n\n    /* Now start iterating over the created document */\n    while ((currentNode = nodeIterator.nextNode())) {\n      /* Sanitize tags and elements */\n      _sanitizeElements(currentNode);\n\n      /* Check attributes next */\n      _sanitizeAttributes(currentNode);\n\n      /* Shadow DOM detected, sanitize it */\n      if (currentNode.content instanceof DocumentFragment) {\n        _sanitizeShadowDOM(currentNode.content);\n      }\n    }\n\n    /* If we sanitized `dirty` in-place, return it. */\n    if (IN_PLACE) {\n      return dirty;\n    }\n\n    /* Return sanitized string or DOM */\n    if (RETURN_DOM) {\n      if (RETURN_DOM_FRAGMENT) {\n        returnNode = createDocumentFragment.call(body.ownerDocument);\n\n        while (body.firstChild) {\n          // eslint-disable-next-line unicorn/prefer-dom-node-append\n          returnNode.appendChild(body.firstChild);\n        }\n      } else {\n        returnNode = body;\n      }\n\n      if (ALLOWED_ATTR.shadowroot || ALLOWED_ATTR.shadowrootmode) {\n        /*\n          AdoptNode() is not used because internal state is not reset\n          (e.g. the past names map of a HTMLFormElement), this is safe\n          in theory but we would rather not risk another attack vector.\n          The state that is cloned by importNode() is explicitly defined\n          by the specs.\n        */\n        returnNode = importNode.call(originalDocument, returnNode, true);\n      }\n\n      return returnNode;\n    }\n\n    let serializedHTML = WHOLE_DOCUMENT ? body.outerHTML : body.innerHTML;\n\n    /* Serialize doctype if allowed */\n    if (\n      WHOLE_DOCUMENT &&\n      ALLOWED_TAGS['!doctype'] &&\n      body.ownerDocument &&\n      body.ownerDocument.doctype &&\n      body.ownerDocument.doctype.name &&\n      regExpTest(EXPRESSIONS.DOCTYPE_NAME, body.ownerDocument.doctype.name)\n    ) {\n      serializedHTML =\n        '<!DOCTYPE ' + body.ownerDocument.doctype.name + '>\\n' + serializedHTML;\n    }\n\n    /* Sanitize final string template-safe */\n    if (SAFE_FOR_TEMPLATES) {\n      arrayForEach([MUSTACHE_EXPR, ERB_EXPR, TMPLIT_EXPR], (expr) => {\n        serializedHTML = stringReplace(serializedHTML, expr, ' ');\n      });\n    }\n\n    return trustedTypesPolicy && RETURN_TRUSTED_TYPE\n      ? trustedTypesPolicy.createHTML(serializedHTML)\n      : serializedHTML;\n  };\n\n  DOMPurify.setConfig = function (cfg = {}) {\n    _parseConfig(cfg);\n    SET_CONFIG = true;\n  };\n\n  DOMPurify.clearConfig = function () {\n    CONFIG = null;\n    SET_CONFIG = false;\n  };\n\n  DOMPurify.isValidAttribute = function (tag, attr, value) {\n    /* Initialize shared config vars if necessary. */\n    if (!CONFIG) {\n      _parseConfig({});\n    }\n\n    const lcTag = transformCaseFunc(tag);\n    const lcName = transformCaseFunc(attr);\n    return _isValidAttribute(lcTag, lcName, value);\n  };\n\n  DOMPurify.addHook = function (entryPoint, hookFunction) {\n    if (typeof hookFunction !== 'function') {\n      return;\n    }\n\n    arrayPush(hooks[entryPoint], hookFunction);\n  };\n\n  DOMPurify.removeHook = function (entryPoint, hookFunction) {\n    if (hookFunction !== undefined) {\n      const index = arrayLastIndexOf(hooks[entryPoint], hookFunction);\n\n      return index === -1\n        ? undefined\n        : arraySplice(hooks[entryPoint], index, 1)[0];\n    }\n\n    return arrayPop(hooks[entryPoint]);\n  };\n\n  DOMPurify.removeHooks = function (entryPoint) {\n    hooks[entryPoint] = [];\n  };\n\n  DOMPurify.removeAllHooks = function () {\n    hooks = _createHooksMap();\n  };\n\n  return DOMPurify;\n}\n\nexport default createDOMPurify();\n\nexport interface DOMPurify {\n  /**\n   * Creates a DOMPurify instance using the given window-like object. Defaults to `window`.\n   */\n  (root?: WindowLike): DOMPurify;\n\n  /**\n   * Version label, exposed for easier checks\n   * if DOMPurify is up to date or not\n   */\n  version: string;\n\n  /**\n   * Array of elements that DOMPurify removed during sanitation.\n   * Empty if nothing was removed.\n   */\n  removed: Array<RemovedElement | RemovedAttribute>;\n\n  /**\n   * Expose whether this browser supports running the full DOMPurify.\n   */\n  isSupported: boolean;\n\n  /**\n   * Set the configuration once.\n   *\n   * @param cfg configuration object\n   */\n  setConfig(cfg?: Config): void;\n\n  /**\n   * Removes the configuration.\n   */\n  clearConfig(): void;\n\n  /**\n   * Provides core sanitation functionality.\n   *\n   * @param dirty string or DOM node\n   * @param cfg object\n   * @returns Sanitized TrustedHTML.\n   */\n  sanitize(\n    dirty: string | Node,\n    cfg: Config & { RETURN_TRUSTED_TYPE: true }\n  ): TrustedHTML;\n\n  /**\n   * Provides core sanitation functionality.\n   *\n   * @param dirty DOM node\n   * @param cfg object\n   * @returns Sanitized DOM node.\n   */\n  sanitize(dirty: Node, cfg: Config & { IN_PLACE: true }): Node;\n\n  /**\n   * Provides core sanitation functionality.\n   *\n   * @param dirty string or DOM node\n   * @param cfg object\n   * @returns Sanitized DOM node.\n   */\n  sanitize(dirty: string | Node, cfg: Config & { RETURN_DOM: true }): Node;\n\n  /**\n   * Provides core sanitation functionality.\n   *\n   * @param dirty string or DOM node\n   * @param cfg object\n   * @returns Sanitized document fragment.\n   */\n  sanitize(\n    dirty: string | Node,\n    cfg: Config & { RETURN_DOM_FRAGMENT: true }\n  ): DocumentFragment;\n\n  /**\n   * Provides core sanitation functionality.\n   *\n   * @param dirty string or DOM node\n   * @param cfg object\n   * @returns Sanitized string.\n   */\n  sanitize(dirty: string | Node, cfg?: Config): string;\n\n  /**\n   * Checks if an attribute value is valid.\n   * Uses last set config, if any. Otherwise, uses config defaults.\n   *\n   * @param tag Tag name of containing element.\n   * @param attr Attribute name.\n   * @param value Attribute value.\n   * @returns Returns true if `value` is valid. Otherwise, returns false.\n   */\n  isValidAttribute(tag: string, attr: string, value: string): boolean;\n\n  /**\n   * Adds a DOMPurify hook.\n   *\n   * @param entryPoint entry point for the hook to add\n   * @param hookFunction function to execute\n   */\n  addHook(entryPoint: BasicHookName, hookFunction: NodeHook): void;\n\n  /**\n   * Adds a DOMPurify hook.\n   *\n   * @param entryPoint entry point for the hook to add\n   * @param hookFunction function to execute\n   */\n  addHook(entryPoint: ElementHookName, hookFunction: ElementHook): void;\n\n  /**\n   * Adds a DOMPurify hook.\n   *\n   * @param entryPoint entry point for the hook to add\n   * @param hookFunction function to execute\n   */\n  addHook(\n    entryPoint: DocumentFragmentHookName,\n    hookFunction: DocumentFragmentHook\n  ): void;\n\n  /**\n   * Adds a DOMPurify hook.\n   *\n   * @param entryPoint entry point for the hook to add\n   * @param hookFunction function to execute\n   */\n  addHook(\n    entryPoint: 'uponSanitizeElement',\n    hookFunction: UponSanitizeElementHook\n  ): void;\n\n  /**\n   * Adds a DOMPurify hook.\n   *\n   * @param entryPoint entry point for the hook to add\n   * @param hookFunction function to execute\n   */\n  addHook(\n    entryPoint: 'uponSanitizeAttribute',\n    hookFunction: UponSanitizeAttributeHook\n  ): void;\n\n  /**\n   * Remove a DOMPurify hook at a given entryPoint\n   * (pops it from the stack of hooks if hook not specified)\n   *\n   * @param entryPoint entry point for the hook to remove\n   * @param hookFunction optional specific hook to remove\n   * @returns removed hook\n   */\n  removeHook(\n    entryPoint: BasicHookName,\n    hookFunction?: NodeHook\n  ): NodeHook | undefined;\n\n  /**\n   * Remove a DOMPurify hook at a given entryPoint\n   * (pops it from the stack of hooks if hook not specified)\n   *\n   * @param entryPoint entry point for the hook to remove\n   * @param hookFunction optional specific hook to remove\n   * @returns removed hook\n   */\n  removeHook(\n    entryPoint: ElementHookName,\n    hookFunction?: ElementHook\n  ): ElementHook | undefined;\n\n  /**\n   * Remove a DOMPurify hook at a given entryPoint\n   * (pops it from the stack of hooks if hook not specified)\n   *\n   * @param entryPoint entry point for the hook to remove\n   * @param hookFunction optional specific hook to remove\n   * @returns removed hook\n   */\n  removeHook(\n    entryPoint: DocumentFragmentHookName,\n    hookFunction?: DocumentFragmentHook\n  ): DocumentFragmentHook | undefined;\n\n  /**\n   * Remove a DOMPurify hook at a given entryPoint\n   * (pops it from the stack of hooks if hook not specified)\n   *\n   * @param entryPoint entry point for the hook to remove\n   * @param hookFunction optional specific hook to remove\n   * @returns removed hook\n   */\n  removeHook(\n    entryPoint: 'uponSanitizeElement',\n    hookFunction?: UponSanitizeElementHook\n  ): UponSanitizeElementHook | undefined;\n\n  /**\n   * Remove a DOMPurify hook at a given entryPoint\n   * (pops it from the stack of hooks if hook not specified)\n   *\n   * @param entryPoint entry point for the hook to remove\n   * @param hookFunction optional specific hook to remove\n   * @returns removed hook\n   */\n  removeHook(\n    entryPoint: 'uponSanitizeAttribute',\n    hookFunction?: UponSanitizeAttributeHook\n  ): UponSanitizeAttributeHook | undefined;\n\n  /**\n   * Removes all DOMPurify hooks at a given entryPoint\n   *\n   * @param entryPoint entry point for the hooks to remove\n   */\n  removeHooks(entryPoint: HookName): void;\n\n  /**\n   * Removes all DOMPurify hooks.\n   */\n  removeAllHooks(): void;\n}\n\n/**\n * An element removed by DOMPurify.\n */\nexport interface RemovedElement {\n  /**\n   * The element that was removed.\n   */\n  element: Node;\n}\n\n/**\n * An element removed by DOMPurify.\n */\nexport interface RemovedAttribute {\n  /**\n   * The attribute that was removed.\n   */\n  attribute: Attr | null;\n\n  /**\n   * The element that the attribute was removed.\n   */\n  from: Node;\n}\n\ntype BasicHookName =\n  | 'beforeSanitizeElements'\n  | 'afterSanitizeElements'\n  | 'uponSanitizeShadowNode';\ntype ElementHookName = 'beforeSanitizeAttributes' | 'afterSanitizeAttributes';\ntype DocumentFragmentHookName =\n  | 'beforeSanitizeShadowDOM'\n  | 'afterSanitizeShadowDOM';\ntype UponSanitizeElementHookName = 'uponSanitizeElement';\ntype UponSanitizeAttributeHookName = 'uponSanitizeAttribute';\n\ninterface HooksMap {\n  beforeSanitizeElements: NodeHook[];\n  afterSanitizeElements: NodeHook[];\n  beforeSanitizeShadowDOM: DocumentFragmentHook[];\n  uponSanitizeShadowNode: NodeHook[];\n  afterSanitizeShadowDOM: DocumentFragmentHook[];\n  beforeSanitizeAttributes: ElementHook[];\n  afterSanitizeAttributes: ElementHook[];\n  uponSanitizeElement: UponSanitizeElementHook[];\n  uponSanitizeAttribute: UponSanitizeAttributeHook[];\n}\n\nexport type HookName =\n  | BasicHookName\n  | ElementHookName\n  | DocumentFragmentHookName\n  | UponSanitizeElementHookName\n  | UponSanitizeAttributeHookName;\n\nexport type NodeHook = (\n  this: DOMPurify,\n  currentNode: Node,\n  hookEvent: null,\n  config: Config\n) => void;\n\nexport type ElementHook = (\n  this: DOMPurify,\n  currentNode: Element,\n  hookEvent: null,\n  config: Config\n) => void;\n\nexport type DocumentFragmentHook = (\n  this: DOMPurify,\n  currentNode: DocumentFragment,\n  hookEvent: null,\n  config: Config\n) => void;\n\nexport type UponSanitizeElementHook = (\n  this: DOMPurify,\n  currentNode: Node,\n  hookEvent: UponSanitizeElementHookEvent,\n  config: Config\n) => void;\n\nexport type UponSanitizeAttributeHook = (\n  this: DOMPurify,\n  currentNode: Element,\n  hookEvent: UponSanitizeAttributeHookEvent,\n  config: Config\n) => void;\n\nexport interface UponSanitizeElementHookEvent {\n  tagName: string;\n  allowedTags: Record<string, boolean>;\n}\n\nexport interface UponSanitizeAttributeHookEvent {\n  attrName: string;\n  attrValue: string;\n  keepAttr: boolean;\n  allowedAttributes: Record<string, boolean>;\n  forceKeepAttr: boolean | undefined;\n}\n\n/**\n * A `Window`-like object containing the properties and types that DOMPurify requires.\n */\nexport type WindowLike = Pick<\n  typeof globalThis,\n  | 'DocumentFragment'\n  | 'HTMLTemplateElement'\n  | 'Node'\n  | 'Element'\n  | 'NodeFilter'\n  | 'NamedNodeMap'\n  | 'HTMLFormElement'\n  | 'DOMParser'\n> & {\n  document?: Document;\n  MozNamedAttrMap?: typeof window.NamedNodeMap;\n} & Pick<TrustedTypesWindow, 'trustedTypes'>;\n"], "names": ["entries", "setPrototypeOf", "isFrozen", "getPrototypeOf", "getOwnPropertyDescriptor", "Object", "freeze", "seal", "create", "apply", "construct", "Reflect", "x", "fun", "thisValue", "args", "Func", "arrayForEach", "unapply", "Array", "prototype", "for<PERSON>ach", "arrayLastIndexOf", "lastIndexOf", "arrayPop", "pop", "arrayPush", "push", "arraySplice", "splice", "stringToLowerCase", "String", "toLowerCase", "stringToString", "toString", "stringMatch", "match", "stringReplace", "replace", "stringIndexOf", "indexOf", "stringTrim", "trim", "objectHasOwnProperty", "hasOwnProperty", "regExpTest", "RegExp", "test", "typeErrorCreate", "unconstruct", "TypeError", "func", "thisArg", "lastIndex", "_len", "arguments", "length", "_key", "_len2", "_key2", "addToSet", "set", "array", "transformCaseFunc", "l", "element", "lcElement", "cleanArray", "index", "isPropertyExist", "clone", "object", "newObject", "property", "value", "isArray", "constructor", "lookupGetter", "prop", "desc", "get", "fallback<PERSON><PERSON><PERSON>", "html", "svg", "svgFilters", "svgDisallowed", "mathMl", "mathMlDisallowed", "text", "xml", "MUSTACHE_EXPR", "ERB_EXPR", "TMPLIT_EXPR", "DATA_ATTR", "ARIA_ATTR", "IS_ALLOWED_URI", "IS_SCRIPT_OR_DATA", "ATTR_WHITESPACE", "DOCTYPE_NAME", "CUSTOM_ELEMENT", "NODE_TYPE", "attribute", "cdataSection", "entityReference", "entityNode", "progressingInstruction", "comment", "document", "documentType", "documentFragment", "notation", "getGlobal", "window", "_createTrustedTypesPolicy", "trustedTypes", "purifyHostElement", "createPolicy", "suffix", "ATTR_NAME", "hasAttribute", "getAttribute", "policyName", "createHTML", "createScriptURL", "scriptUrl", "_", "console", "warn", "_createHooksMap", "afterSanitizeAttributes", "afterSanitizeElements", "afterSanitizeShadowDOM", "beforeSanitizeAttributes", "beforeSanitizeElements", "beforeSanitizeShadowDOM", "uponSanitizeAttribute", "uponSanitizeElement", "uponSanitizeShadowNode", "createDOMPurify", "undefined", "DOMPurify", "root", "version", "VERSION", "removed", "nodeType", "Element", "isSupported", "originalDocument", "currentScript", "DocumentFragment", "HTMLTemplateElement", "Node", "Node<PERSON><PERSON><PERSON>", "NamedNodeMap", "MozNamedAttrMap", "HTMLFormElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ElementPrototype", "cloneNode", "remove", "getNextSibling", "getChildNodes", "getParentNode", "template", "createElement", "content", "ownerDocument", "trustedTypesPolicy", "emptyHTML", "implementation", "createNodeIterator", "createDocumentFragment", "getElementsByTagName", "importNode", "hooks", "createHTMLDocument", "EXPRESSIONS", "ALLOWED_TAGS", "DEFAULT_ALLOWED_TAGS", "TAGS", "ALLOWED_ATTR", "DEFAULT_ALLOWED_ATTR", "ATTRS", "CUSTOM_ELEMENT_HANDLING", "tagNameCheck", "writable", "configurable", "enumerable", "attributeNameCheck", "allowCustomizedBuiltInElements", "FORBID_TAGS", "FORBID_ATTR", "ALLOW_ARIA_ATTR", "ALLOW_DATA_ATTR", "ALLOW_UNKNOWN_PROTOCOLS", "ALLOW_SELF_CLOSE_IN_ATTR", "SAFE_FOR_TEMPLATES", "SAFE_FOR_XML", "WHOLE_DOCUMENT", "SET_CONFIG", "FORCE_BODY", "RETURN_DOM", "RETURN_DOM_FRAGMENT", "RETURN_TRUSTED_TYPE", "SANITIZE_DOM", "SANITIZE_NAMED_PROPS", "SANITIZE_NAMED_PROPS_PREFIX", "KEEP_CONTENT", "IN_PLACE", "USE_PROFILES", "FORBID_CONTENTS", "DEFAULT_FORBID_CONTENTS", "DATA_URI_TAGS", "DEFAULT_DATA_URI_TAGS", "URI_SAFE_ATTRIBUTES", "DEFAULT_URI_SAFE_ATTRIBUTES", "MATHML_NAMESPACE", "SVG_NAMESPACE", "HTML_NAMESPACE", "NAMESPACE", "IS_EMPTY_INPUT", "ALLOWED_NAMESPACES", "DEFAULT_ALLOWED_NAMESPACES", "MATHML_TEXT_INTEGRATION_POINTS", "HTML_INTEGRATION_POINTS", "COMMON_SVG_AND_HTML_ELEMENTS", "PARSER_MEDIA_TYPE", "SUPPORTED_PARSER_MEDIA_TYPES", "DEFAULT_PARSER_MEDIA_TYPE", "CONFIG", "formElement", "isRegexOrFunction", "testValue", "Function", "_parseConfig", "cfg", "ADD_URI_SAFE_ATTR", "ADD_DATA_URI_TAGS", "ALLOWED_URI_REGEXP", "ADD_TAGS", "ADD_ATTR", "table", "tbody", "TRUSTED_TYPES_POLICY", "ALL_SVG_TAGS", "ALL_MATHML_TAGS", "_checkValidNamespace", "parent", "tagName", "namespaceURI", "parentTagName", "Boolean", "_forceRemove", "node", "<PERSON><PERSON><PERSON><PERSON>", "_removeAttribute", "name", "getAttributeNode", "from", "removeAttribute", "setAttribute", "_initDocument", "dirty", "doc", "leadingWhitespace", "matches", "dirtyPayload", "parseFromString", "documentElement", "createDocument", "innerHTML", "body", "insertBefore", "createTextNode", "childNodes", "call", "_createNodeIterator", "SHOW_ELEMENT", "SHOW_COMMENT", "SHOW_TEXT", "SHOW_PROCESSING_INSTRUCTION", "SHOW_CDATA_SECTION", "_isClobbered", "nodeName", "textContent", "attributes", "hasChildNodes", "_isNode", "_executeHooks", "currentNode", "data", "hook", "_sanitizeElements", "allowedTags", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "_isBasicCustomElement", "parentNode", "childCount", "i", "child<PERSON>lone", "__removalCount", "expr", "_isValidAttribute", "lcTag", "lcName", "_sanitizeAttributes", "hookEvent", "attrName", "attrValue", "keepAttr", "allowedAttributes", "forceKeepAttr", "attr", "initValue", "getAttributeType", "setAttributeNS", "_sanitizeShadowDOM", "fragment", "shadowNode", "shadowIterator", "nextNode", "sanitize", "importedNode", "returnNode", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "nodeIterator", "shadowroot", "shadowrootmode", "serializedHTML", "outerHTML", "doctype", "setConfig", "clearConfig", "isValidAttribute", "tag", "addHook", "entryPoint", "hookFunction", "removeH<PERSON>", "removeHooks", "removeAllHooks"], "mappings": ";;;AAAA,MAAM,EACJA,OAAO,EACPC,cAAc,EACdC,QAAQ,EACRC,cAAc,EACdC,wBAAAA,EACD,GAAGC,MAAM,CAAA;AAEV,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAEC,MAAAA,EAAQ,GAAGH,MAAM,CAAC,CAAA,gDAAA;AACtC,IAAI,EAAEI,KAAK,EAAEC,SAAAA,EAAW,GAAG,OAAOC,OAAO,KAAK,WAAW,IAAIA,OAAO,CAAA;AAEpE,IAAI,CAACL,MAAM,EAAE;IACXA,MAAM,GAAG,SAAAA,MAAUM,CAAAA,CAAC,EAAA;QAClB,OAAOA,CAAC,CAAA;KACT,CAAA;AACH,CAAA;AAEA,IAAI,CAACL,IAAI,EAAE;IACTA,IAAI,GAAG,SAAAA,IAAUK,CAAAA,CAAC,EAAA;QAChB,OAAOA,CAAC,CAAA;KACT,CAAA;AACH,CAAA;AAEA,IAAI,CAACH,KAAK,EAAE;IACVA,KAAK,GAAG,SAAAA,KAAUI,CAAAA,GAAG,EAAEC,SAAS,EAAEC,IAAI,EAAA;QACpC,OAAOF,GAAG,CAACJ,KAAK,CAACK,SAAS,EAAEC,IAAI,CAAC,CAAA;KAClC,CAAA;AACH,CAAA;AAEA,IAAI,CAACL,SAAS,EAAE;IACdA,SAAS,GAAG,SAAAA,SAAAA,CAAUM,IAAI,EAAED,IAAI,EAAA;QAC9B,OAAO,IAAIC,IAAI,CAAC,GAAGD,IAAI,CAAC,CAAA;KACzB,CAAA;AACH,CAAA;AAEA,MAAME,YAAY,GAAGC,OAAO,CAACC,KAAK,CAACC,SAAS,CAACC,OAAO,CAAC,CAAA;AAErD,MAAMC,gBAAgB,GAAGJ,OAAO,CAACC,KAAK,CAACC,SAAS,CAACG,WAAW,CAAC,CAAA;AAC7D,MAAMC,QAAQ,GAAGN,OAAO,CAACC,KAAK,CAACC,SAAS,CAACK,GAAG,CAAC,CAAA;AAC7C,MAAMC,SAAS,GAAGR,OAAO,CAACC,KAAK,CAACC,SAAS,CAACO,IAAI,CAAC,CAAA;AAE/C,MAAMC,WAAW,GAAGV,OAAO,CAACC,KAAK,CAACC,SAAS,CAACS,MAAM,CAAC,CAAA;AAEnD,MAAMC,iBAAiB,GAAGZ,OAAO,CAACa,MAAM,CAACX,SAAS,CAACY,WAAW,CAAC,CAAA;AAC/D,MAAMC,cAAc,GAAGf,OAAO,CAACa,MAAM,CAACX,SAAS,CAACc,QAAQ,CAAC,CAAA;AACzD,MAAMC,WAAW,GAAGjB,OAAO,CAACa,MAAM,CAACX,SAAS,CAACgB,KAAK,CAAC,CAAA;AACnD,MAAMC,aAAa,GAAGnB,OAAO,CAACa,MAAM,CAACX,SAAS,CAACkB,OAAO,CAAC,CAAA;AACvD,MAAMC,aAAa,GAAGrB,OAAO,CAACa,MAAM,CAACX,SAAS,CAACoB,OAAO,CAAC,CAAA;AACvD,MAAMC,UAAU,GAAGvB,OAAO,CAACa,MAAM,CAACX,SAAS,CAACsB,IAAI,CAAC,CAAA;AAEjD,MAAMC,oBAAoB,GAAGzB,OAAO,CAACb,MAAM,CAACe,SAAS,CAACwB,cAAc,CAAC,CAAA;AAErE,MAAMC,UAAU,GAAG3B,OAAO,CAAC4B,MAAM,CAAC1B,SAAS,CAAC2B,IAAI,CAAC,CAAA;AAEjD,MAAMC,eAAe,GAAGC,WAAW,CAACC,SAAS,CAAC,CAAA;AAE9C;;;;;CAKG,GACH,SAAShC,OAAOA,CACdiC,IAAyC,EAAA;IAEzC,OAAO,SAACC,OAAY,EAAuB;QACzC,IAAIA,OAAO,YAAYN,MAAM,EAAE;YAC7BM,OAAO,CAACC,SAAS,GAAG,CAAC,CAAA;QACvB,CAAA;QAAC,IAAAC,IAAAA,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAHsBzC,IAAW,GAAA,IAAAI,KAAA,CAAAmC,IAAA,GAAAA,CAAAA,GAAAA,IAAA,GAAA,IAAA,IAAAG,IAAA,GAAA,CAAA,EAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA,EAAA,CAAA;YAAX1C,IAAW,CAAA0C,IAAA,GAAAF,CAAAA,CAAAA,GAAAA,SAAA,CAAAE,IAAA,CAAA,CAAA;QAAA,CAAA;QAKlC,OAAOhD,KAAK,CAAC0C,IAAI,EAAEC,OAAO,EAAErC,IAAI,CAAC,CAAA;KAClC,CAAA;AACH,CAAA;AAEA;;;;;CAKG,GACH,SAASkC,WAAWA,CAAIE,IAA2B,EAAA;IACjD,OAAO,YAAA;QAAA,IAAA,IAAAO,KAAA,GAAAH,SAAA,CAAAC,MAAA,EAAIzC,IAAW,GAAAI,IAAAA,KAAA,CAAAuC,KAAA,GAAAC,KAAA,GAAA,CAAA,EAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA,EAAA,CAAA;YAAX5C,IAAW,CAAA4C,KAAA,CAAAJ,GAAAA,SAAA,CAAAI,KAAA,CAAA,CAAA;QAAA,CAAA;QAAA,OAAQjD,SAAS,CAACyC,IAAI,EAAEpC,IAAI,CAAC,CAAA;IAAA,CAAA,CAAA;AACrD,CAAA;AAEA;;;;;;;CAOG,GACH,SAAS6C,QAAQA,CACfC,GAAwB,EACxBC,KAAqB,EACoD;IAAA,IAAzEC,oBAAAA,UAAAA,MAAAA,GAAAA,KAAAA,SAAAA,CAAAA,EAAAA,KAAAA,YAAAA,SAAAA,CAAAA,EAAAA,GAAwDjC,iBAAiB,CAAA;IAEzE,IAAI7B,cAAc,EAAE;QAClB,4DAAA;QACA,6DAAA;QACA,mEAAA;QACAA,cAAc,CAAC4D,GAAG,EAAE,IAAI,CAAC,CAAA;IAC3B,CAAA;IAEA,IAAIG,CAAC,GAAGF,KAAK,CAACN,MAAM,CAAA;IACpB,MAAOQ,CAAC,EAAE,CAAE;QACV,IAAIC,OAAO,GAAGH,KAAK,CAACE,CAAC,CAAC,CAAA;QACtB,IAAI,OAAOC,OAAO,KAAK,QAAQ,EAAE;YAC/B,MAAMC,SAAS,GAAGH,iBAAiB,CAACE,OAAO,CAAC,CAAA;YAC5C,IAAIC,SAAS,KAAKD,OAAO,EAAE;gBACzB,yDAAA;gBACA,IAAI,CAAC/D,QAAQ,CAAC4D,KAAK,CAAC,EAAE;oBACnBA,KAAe,CAACE,CAAC,CAAC,GAAGE,SAAS,CAAA;gBACjC,CAAA;gBAEAD,OAAO,GAAGC,SAAS,CAAA;YACrB,CAAA;QACF,CAAA;QAEAL,GAAG,CAACI,OAAO,CAAC,GAAG,IAAI,CAAA;IACrB,CAAA;IAEA,OAAOJ,GAAG,CAAA;AACZ,CAAA;AAEA;;;;;CAKG,GACH,SAASM,UAAUA,CAAIL,KAAU,EAAA;IAC/B,IAAK,IAAIM,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGN,KAAK,CAACN,MAAM,EAAEY,KAAK,EAAE,CAAE;QACjD,MAAMC,eAAe,GAAG1B,oBAAoB,CAACmB,KAAK,EAAEM,KAAK,CAAC,CAAA;QAE1D,IAAI,CAACC,eAAe,EAAE;YACpBP,KAAK,CAACM,KAAK,CAAC,GAAG,IAAI,CAAA;QACrB,CAAA;IACF,CAAA;IAEA,OAAON,KAAK,CAAA;AACd,CAAA;AAEA;;;;;CAKG,GACH,SAASQ,KAAKA,CAAgCC,MAAS,EAAA;IACrD,MAAMC,SAAS,GAAGhE,MAAM,CAAC,IAAI,CAAC,CAAA;IAE9B,KAAK,MAAM,CAACiE,QAAQ,EAAEC,KAAK,CAAC,IAAI1E,OAAO,CAACuE,MAAM,CAAC,CAAE;QAC/C,MAAMF,eAAe,GAAG1B,oBAAoB,CAAC4B,MAAM,EAAEE,QAAQ,CAAC,CAAA;QAE9D,IAAIJ,eAAe,EAAE;YACnB,IAAIlD,KAAK,CAACwD,OAAO,CAACD,KAAK,CAAC,EAAE;gBACxBF,SAAS,CAACC,QAAQ,CAAC,GAAGN,UAAU,CAACO,KAAK,CAAC,CAAA;YACzC,CAAC,MAAM,IACLA,KAAK,IACL,OAAOA,KAAK,KAAK,QAAQ,IACzBA,KAAK,CAACE,WAAW,KAAKvE,MAAM,EAC5B;gBACAmE,SAAS,CAACC,QAAQ,CAAC,GAAGH,KAAK,CAACI,KAAK,CAAC,CAAA;YACpC,CAAC,MAAM;gBACLF,SAAS,CAACC,QAAQ,CAAC,GAAGC,KAAK,CAAA;YAC7B,CAAA;QACF,CAAA;IACF,CAAA;IAEA,OAAOF,SAAS,CAAA;AAClB,CAAA;AAEA;;;;;;CAMG,GACH,SAASK,YAAYA,CACnBN,MAAS,EACTO,IAAY,EAAA;IAEZ,MAAOP,MAAM,KAAK,IAAI,CAAE;QACtB,MAAMQ,IAAI,GAAG3E,wBAAwB,CAACmE,MAAM,EAAEO,IAAI,CAAC,CAAA;QAEnD,IAAIC,IAAI,EAAE;YACR,IAAIA,IAAI,CAACC,GAAG,EAAE;gBACZ,OAAO9D,OAAO,CAAC6D,IAAI,CAACC,GAAG,CAAC,CAAA;YAC1B,CAAA;YAEA,IAAI,OAAOD,IAAI,CAACL,KAAK,KAAK,UAAU,EAAE;gBACpC,OAAOxD,OAAO,CAAC6D,IAAI,CAACL,KAAK,CAAC,CAAA;YAC5B,CAAA;QACF,CAAA;QAEAH,MAAM,GAAGpE,cAAc,CAACoE,MAAM,CAAC,CAAA;IACjC,CAAA;IAEA,SAASU,aAAaA,GAAA;QACpB,OAAO,IAAI,CAAA;IACb,CAAA;IAEA,OAAOA,aAAa,CAAA;AACtB;AC3MO,MAAMC,MAAI,GAAG5E,MAAM,CAAC;IACzB,GAAG;IACH,MAAM;IACN,SAAS;IACT,SAAS;IACT,MAAM;IACN,SAAS;IACT,OAAO;IACP,OAAO;IACP,GAAG;IACH,KAAK;IACL,KAAK;IACL,KAAK;IACL,OAAO;IACP,YAAY;IACZ,MAAM;IACN,IAAI;IACJ,QAAQ;IACR,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,MAAM;IACN,KAAK;IACL,UAAU;IACV,SAAS;IACT,MAAM;IACN,UAAU;IACV,IAAI;IACJ,WAAW;IACX,KAAK;IACL,SAAS;IACT,KAAK;IACL,QAAQ;IACR,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,SAAS;IACT,IAAI;IACJ,UAAU;IACV,YAAY;IACZ,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,MAAM;IACN,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,IAAI;IACJ,MAAM;IACN,GAAG;IACH,KAAK;IACL,OAAO;IACP,KAAK;IACL,KAAK;IACL,OAAO;IACP,QAAQ;IACR,IAAI;IACJ,MAAM;IACN,KAAK;IACL,MAAM;IACN,SAAS;IACT,MAAM;IACN,UAAU;IACV,OAAO;IACP,KAAK;IACL,MAAM;IACN,IAAI;IACJ,UAAU;IACV,QAAQ;IACR,QAAQ;IACR,GAAG;IACH,SAAS;IACT,KAAK;IACL,UAAU;IACV,GAAG;IACH,IAAI;IACJ,IAAI;IACJ,MAAM;IACN,GAAG;IACH,MAAM;IACN,SAAS;IACT,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,KAAK;IACL,SAAS;IACT,KAAK;IACL,OAAO;IACP,OAAO;IACP,IAAI;IACJ,UAAU;IACV,UAAU;IACV,OAAO;IACP,IAAI;IACJ,OAAO;IACP,MAAM;IACN,IAAI;IACJ,OAAO;IACP,IAAI;IACJ,GAAG;IACH,IAAI;IACJ,KAAK;IACL,OAAO;IACP,KAAK;CACG,CAAC,CAAA;AAEJ,MAAM6E,KAAG,GAAG7E,MAAM,CAAC;IACxB,KAAK;IACL,GAAG;IACH,UAAU;IACV,aAAa;IACb,cAAc;IACd,cAAc;IACd,eAAe;IACf,kBAAkB;IAClB,QAAQ;IACR,UAAU;IACV,MAAM;IACN,MAAM;IACN,SAAS;IACT,QAAQ;IACR,MAAM;IACN,GAAG;IACH,OAAO;IACP,UAAU;IACV,OAAO;IACP,OAAO;IACP,MAAM;IACN,gBAAgB;IAChB,QAAQ;IACR,MAAM;IACN,UAAU;IACV,OAAO;IACP,MAAM;IACN,SAAS;IACT,SAAS;IACT,UAAU;IACV,gBAAgB;IAChB,MAAM;IACN,MAAM;IACN,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,UAAU;IACV,OAAO;IACP,MAAM;IACN,OAAO;IACP,MAAM;IACN,OAAO;CACC,CAAC,CAAA;AAEJ,MAAM8E,UAAU,GAAG9E,MAAM,CAAC;IAC/B,SAAS;IACT,eAAe;IACf,qBAAqB;IACrB,aAAa;IACb,kBAAkB;IAClB,mBAAmB;IACnB,mBAAmB;IACnB,gBAAgB;IAChB,cAAc;IACd,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,gBAAgB;IAChB,SAAS;IACT,SAAS;IACT,aAAa;IACb,cAAc;IACd,UAAU;IACV,cAAc;IACd,oBAAoB;IACpB,aAAa;IACb,QAAQ;IACR,cAAc;CACN,CAAC,CAAA;AAEX,uDAAA;AACA,yDAAA;AACA,mDAAA;AACA,cAAA;AACO,MAAM+E,aAAa,GAAG/E,MAAM,CAAC;IAClC,SAAS;IACT,eAAe;IACf,QAAQ;IACR,SAAS;IACT,WAAW;IACX,kBAAkB;IAClB,gBAAgB;IAChB,eAAe;IACf,eAAe;IACf,eAAe;IACf,OAAO;IACP,WAAW;IACX,MAAM;IACN,cAAc;IACd,WAAW;IACX,SAAS;IACT,eAAe;IACf,QAAQ;IACR,KAAK;IACL,YAAY;IACZ,SAAS;IACT,KAAK;CACG,CAAC,CAAA;AAEJ,MAAMgF,QAAM,GAAGhF,MAAM,CAAC;IAC3B,MAAM;IACN,UAAU;IACV,QAAQ;IACR,SAAS;IACT,OAAO;IACP,QAAQ;IACR,IAAI;IACJ,YAAY;IACZ,eAAe;IACf,IAAI;IACJ,IAAI;IACJ,OAAO;IACP,SAAS;IACT,UAAU;IACV,OAAO;IACP,MAAM;IACN,IAAI;IACJ,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,MAAM;IACN,MAAM;IACN,SAAS;IACT,QAAQ;IACR,KAAK;IACL,OAAO;IACP,KAAK;IACL,QAAQ;IACR,YAAY;IACZ,aAAa;CACL,CAAC,CAAA;AAEX,yDAAA;AACA,0CAAA;AACO,MAAMiF,gBAAgB,GAAGjF,MAAM,CAAC;IACrC,SAAS;IACT,aAAa;IACb,YAAY;IACZ,UAAU;IACV,WAAW;IACX,SAAS;IACT,SAAS;IACT,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,WAAW;IACX,YAAY;IACZ,gBAAgB;IAChB,aAAa;IACb,MAAM;CACE,CAAC,CAAA;AAEJ,MAAMkF,IAAI,GAAGlF,MAAM,CAAC;IAAC,OAAO;CAAU,CAAC;ACpRvC,MAAM4E,IAAI,GAAG5E,MAAM,CAAC;IACzB,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,KAAK;IACL,gBAAgB;IAChB,cAAc;IACd,sBAAsB;IACtB,UAAU;IACV,YAAY;IACZ,SAAS;IACT,QAAQ;IACR,SAAS;IACT,aAAa;IACb,aAAa;IACb,SAAS;IACT,MAAM;IACN,OAAO;IACP,OAAO;IACP,OAAO;IACP,MAAM;IACN,SAAS;IACT,UAAU;IACV,cAAc;IACd,QAAQ;IACR,aAAa;IACb,UAAU;IACV,UAAU;IACV,SAAS;IACT,KAAK;IACL,UAAU;IACV,yBAAyB;IACzB,uBAAuB;IACvB,UAAU;IACV,WAAW;IACX,SAAS;IACT,cAAc;IACd,MAAM;IACN,KAAK;IACL,SAAS;IACT,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,MAAM;IACN,UAAU;IACV,IAAI;IACJ,WAAW;IACX,WAAW;IACX,OAAO;IACP,MAAM;IACN,OAAO;IACP,MAAM;IACN,MAAM;IACN,SAAS;IACT,MAAM;IACN,KAAK;IACL,KAAK;IACL,WAAW;IACX,OAAO;IACP,QAAQ;IACR,KAAK;IACL,WAAW;IACX,UAAU;IACV,OAAO;IACP,MAAM;IACN,OAAO;IACP,SAAS;IACT,YAAY;IACZ,QAAQ;IACR,MAAM;IACN,SAAS;IACT,SAAS;IACT,aAAa;IACb,aAAa;IACb,SAAS;IACT,eAAe;IACf,qBAAqB;IACrB,QAAQ;IACR,SAAS;IACT,SAAS;IACT,YAAY;IACZ,UAAU;IACV,KAAK;IACL,UAAU;IACV,KAAK;IACL,UAAU;IACV,MAAM;IACN,MAAM;IACN,SAAS;IACT,YAAY;IACZ,OAAO;IACP,UAAU;IACV,OAAO;IACP,MAAM;IACN,OAAO;IACP,MAAM;IACN,SAAS;IACT,OAAO;IACP,KAAK;IACL,QAAQ;IACR,MAAM;IACN,OAAO;IACP,SAAS;IACT,UAAU;IACV,OAAO;IACP,WAAW;IACX,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,OAAO;IACP,MAAM;IACN,OAAO;IACP,MAAM;CACE,CAAC,CAAA;AAEJ,MAAM6E,GAAG,GAAG7E,MAAM,CAAC;IACxB,eAAe;IACf,YAAY;IACZ,UAAU;IACV,oBAAoB;IACpB,WAAW;IACX,QAAQ;IACR,eAAe;IACf,eAAe;IACf,SAAS;IACT,eAAe;IACf,gBAAgB;IAChB,OAAO;IACP,MAAM;IACN,IAAI;IACJ,OAAO;IACP,MAAM;IACN,eAAe;IACf,WAAW;IACX,WAAW;IACX,OAAO;IACP,qBAAqB;IACrB,6BAA6B;IAC7B,eAAe;IACf,iBAAiB;IACjB,IAAI;IACJ,IAAI;IACJ,GAAG;IACH,IAAI;IACJ,IAAI;IACJ,iBAAiB;IACjB,WAAW;IACX,SAAS;IACT,SAAS;IACT,KAAK;IACL,UAAU;IACV,WAAW;IACX,KAAK;IACL,UAAU;IACV,MAAM;IACN,cAAc;IACd,WAAW;IACX,QAAQ;IACR,aAAa;IACb,aAAa;IACb,eAAe;IACf,aAAa;IACb,WAAW;IACX,kBAAkB;IAClB,cAAc;IACd,YAAY;IACZ,cAAc;IACd,aAAa;IACb,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,YAAY;IACZ,UAAU;IACV,eAAe;IACf,mBAAmB;IACnB,QAAQ;IACR,MAAM;IACN,IAAI;IACJ,iBAAiB;IACjB,IAAI;IACJ,KAAK;IACL,WAAW;IACX,GAAG;IACH,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,SAAS;IACT,WAAW;IACX,YAAY;IACZ,UAAU;IACV,MAAM;IACN,cAAc;IACd,gBAAgB;IAChB,cAAc;IACd,kBAAkB;IAClB,gBAAgB;IAChB,OAAO;IACP,YAAY;IACZ,YAAY;IACZ,cAAc;IACd,cAAc;IACd,aAAa;IACb,aAAa;IACb,kBAAkB;IAClB,WAAW;IACX,KAAK;IACL,MAAM;IACN,OAAO;IACP,QAAQ;IACR,MAAM;IACN,KAAK;IACL,MAAM;IACN,YAAY;IACZ,QAAQ;IACR,UAAU;IACV,SAAS;IACT,OAAO;IACP,QAAQ;IACR,aAAa;IACb,QAAQ;IACR,UAAU;IACV,aAAa;IACb,MAAM;IACN,YAAY;IACZ,qBAAqB;IACrB,kBAAkB;IAClB,cAAc;IACd,QAAQ;IACR,eAAe;IACf,qBAAqB;IACrB,gBAAgB;IAChB,GAAG;IACH,IAAI;IACJ,IAAI;IACJ,QAAQ;IACR,MAAM;IACN,MAAM;IACN,aAAa;IACb,WAAW;IACX,SAAS;IACT,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,MAAM;IACN,iBAAiB;IACjB,OAAO;IACP,kBAAkB;IAClB,kBAAkB;IAClB,cAAc;IACd,aAAa;IACb,cAAc;IACd,aAAa;IACb,YAAY;IACZ,cAAc;IACd,kBAAkB;IAClB,mBAAmB;IACnB,gBAAgB;IAChB,iBAAiB;IACjB,mBAAmB;IACnB,gBAAgB;IAChB,QAAQ;IACR,cAAc;IACd,OAAO;IACP,cAAc;IACd,gBAAgB;IAChB,UAAU;IACV,aAAa;IACb,SAAS;IACT,SAAS;IACT,WAAW;IACX,kBAAkB;IAClB,aAAa;IACb,iBAAiB;IACjB,gBAAgB;IAChB,YAAY;IACZ,MAAM;IACN,IAAI;IACJ,IAAI;IACJ,SAAS;IACT,QAAQ;IACR,SAAS;IACT,YAAY;IACZ,SAAS;IACT,YAAY;IACZ,eAAe;IACf,eAAe;IACf,OAAO;IACP,cAAc;IACd,MAAM;IACN,cAAc;IACd,kBAAkB;IAClB,kBAAkB;IAClB,GAAG;IACH,IAAI;IACJ,IAAI;IACJ,OAAO;IACP,GAAG;IACH,IAAI;IACJ,IAAI;IACJ,GAAG;IACH,YAAY;CACJ,CAAC,CAAA;AAEJ,MAAMgF,MAAM,GAAGhF,MAAM,CAAC;IAC3B,QAAQ;IACR,aAAa;IACb,OAAO;IACP,UAAU;IACV,OAAO;IACP,cAAc;IACd,aAAa;IACb,YAAY;IACZ,YAAY;IACZ,OAAO;IACP,KAAK;IACL,SAAS;IACT,cAAc;IACd,UAAU;IACV,OAAO;IACP,OAAO;IACP,QAAQ;IACR,MAAM;IACN,IAAI;IACJ,SAAS;IACT,QAAQ;IACR,eAAe;IACf,QAAQ;IACR,QAAQ;IACR,gBAAgB;IAChB,WAAW;IACX,UAAU;IACV,aAAa;IACb,SAAS;IACT,SAAS;IACT,eAAe;IACf,UAAU;IACV,UAAU;IACV,MAAM;IACN,UAAU;IACV,UAAU;IACV,YAAY;IACZ,SAAS;IACT,QAAQ;IACR,QAAQ;IACR,aAAa;IACb,eAAe;IACf,sBAAsB;IACtB,WAAW;IACX,WAAW;IACX,YAAY;IACZ,UAAU;IACV,gBAAgB;IAChB,gBAAgB;IAChB,WAAW;IACX,SAAS;IACT,OAAO;IACP,OAAO;CACR,CAAC,CAAA;AAEK,MAAMmF,GAAG,GAAGnF,MAAM,CAAC;IACxB,YAAY;IACZ,QAAQ;IACR,aAAa;IACb,WAAW;IACX,aAAa;CACL,CAAC;AChXX,gDAAA;AACO,MAAMoF,aAAa,GAAGnF,IAAI,CAAC,2BAA2B,CAAC,CAAC,CAAA,+DAAA;AACxD,MAAMoF,QAAQ,GAAGpF,IAAI,CAAC,uBAAuB,CAAC,CAAA;AAC9C,MAAMqF,WAAW,GAAGrF,IAAI,CAAC,eAAe,CAAC,CAAC,CAAA,2CAAA;AAC1C,MAAMsF,SAAS,GAAGtF,IAAI,CAAC,8BAA8B,CAAC,CAAC,CAAA,wCAAA;AACvD,MAAMuF,SAAS,GAAGvF,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAA,wCAAA;AACzC,MAAMwF,cAAc,GAAGxF,IAAI,CAChC,kGAAkG,CAAA,wCAAA;;AAE7F,MAAMyF,iBAAiB,GAAGzF,IAAI,CAAC,uBAAuB,CAAC,CAAA;AACvD,MAAM0F,eAAe,GAAG1F,IAAI,CACjC,6DAA6D,CAAA,uCAAA;;AAExD,MAAM2F,YAAY,GAAG3F,IAAI,CAAC,SAAS,CAAC,CAAA;AACpC,MAAM4F,cAAc,GAAG5F,IAAI,CAAC,0BAA0B,CAAC;;;;;;;;;;;;;;AChB9D,4CAAA,GAkCA,iEAAA;AACA,MAAM6F,SAAS,GAAG;IAChBnC,OAAO,EAAE,CAAC;IACVoC,SAAS,EAAE,CAAC;IACZb,IAAI,EAAE,CAAC;IACPc,YAAY,EAAE,CAAC;IACfC,eAAe,EAAE,CAAC;IAAE,aAAA;IACpBC,UAAU,EAAE,CAAC;IAAE,aAAA;IACfC,sBAAsB,EAAE,CAAC;IACzBC,OAAO,EAAE,CAAC;IACVC,QAAQ,EAAE,CAAC;IACXC,YAAY,EAAE,EAAE;IAChBC,gBAAgB,EAAE,EAAE;IACpBC,QAAQ,EAAE,EAAE,CAAA,aAAA;CACb,CAAA;AAED,MAAMC,SAAS,GAAG,SAAZA,SAASA,GAAG;IAChB,OAAO,OAAOC,MAAM,KAAK,WAAW,UAAG,IAAI,GAAGA,MAAM,CAAA;AACtD,CAAC,CAAA;AAED;;;;;;;CAOG,GACH,MAAMC,yBAAyB,GAAG,SAA5BA,yBAAyBA,CAC7BC,YAAsC,EACtCC,iBAAoC,EAAA;IAEpC,IACE,OAAOD,YAAY,KAAK,QAAQ,IAChC,OAAOA,YAAY,CAACE,YAAY,KAAK,UAAU,EAC/C;QACA,OAAO,IAAI,CAAA;IACb,CAAA;IAEA,sDAAA;IACA,8EAAA;IACA,gEAAA;IACA,IAAIC,MAAM,GAAG,IAAI,CAAA;IACjB,MAAMC,SAAS,GAAG,uBAAuB,CAAA;IACzC,IAAIH,iBAAiB,IAAIA,iBAAiB,CAACI,YAAY,CAACD,SAAS,CAAC,EAAE;QAClED,MAAM,GAAGF,iBAAiB,CAACK,YAAY,CAACF,SAAS,CAAC,CAAA;IACpD,CAAA;IAEA,MAAMG,UAAU,GAAG,WAAW,GAAA,CAAIJ,MAAM,GAAG,GAAG,GAAGA,MAAM,GAAG,EAAE,CAAC,CAAA;IAE7D,IAAI;QACF,OAAOH,YAAY,CAACE,YAAY,CAACK,UAAU,EAAE;YAC3CC,UAAUA,EAACxC,IAAI,EAAA;gBACb,OAAOA,IAAI,CAAA;aACZ;YACDyC,eAAeA,EAACC,SAAS,EAAA;gBACvB,OAAOA,SAAS,CAAA;YAClB,CAAA;QACD,CAAA,CAAC,CAAA;KACH,CAAC,OAAOC,CAAC,EAAE;QACV,mEAAA;QACA,yEAAA;QACA,sBAAA;QACAC,OAAO,CAACC,IAAI,CACV,sBAAsB,GAAGN,UAAU,GAAG,wBAAwB,CAC/D,CAAA;QACD,OAAO,IAAI,CAAA;IACb,CAAA;AACF,CAAC,CAAA;AAED,MAAMO,eAAe,GAAG,SAAlBA,eAAeA,GAAG;IACtB,OAAO;QACLC,uBAAuB,EAAE,EAAE;QAC3BC,qBAAqB,EAAE,EAAE;QACzBC,sBAAsB,EAAE,EAAE;QAC1BC,wBAAwB,EAAE,EAAE;QAC5BC,sBAAsB,EAAE,EAAE;QAC1BC,uBAAuB,EAAE,EAAE;QAC3BC,qBAAqB,EAAE,EAAE;QACzBC,mBAAmB,EAAE,EAAE;QACvBC,sBAAsB,EAAE,EAAA;KACzB,CAAA;AACH,CAAC,CAAA;AAED,SAASC,eAAeA,GAAiC;IAAA,IAAhC1B,MAAqB,GAAAzD,SAAA,CAAAC,MAAA,GAAAD,CAAAA,IAAAA,SAAA,CAAAoF,CAAAA,CAAAA,KAAAA,SAAA,GAAApF,SAAA,CAAAwD,CAAAA,CAAAA,GAAAA,SAAS,EAAE,CAAA;IACvD,MAAM6B,SAAS,IAAeC,IAAgB,GAAKH,eAAe,CAACG,IAAI,CAAC,CAAA;IAExED,SAAS,CAACE,OAAO,GAAGC,OAAO,CAAA;IAE3BH,SAAS,CAACI,OAAO,GAAG,EAAE,CAAA;IAEtB,IACE,CAAChC,MAAM,IACP,CAACA,MAAM,CAACL,QAAQ,IAChBK,MAAM,CAACL,QAAQ,CAACsC,QAAQ,KAAK7C,SAAS,CAACO,QAAQ,IAC/C,CAACK,MAAM,CAACkC,OAAO,EACf;QACA,uDAAA;QACA,uCAAA;QACAN,SAAS,CAACO,WAAW,GAAG,KAAK,CAAA;QAE7B,OAAOP,SAAS,CAAA;IAClB,CAAA;IAEA,IAAI,EAAEjC,QAAAA,EAAU,GAAGK,MAAM,CAAA;IAEzB,MAAMoC,gBAAgB,GAAGzC,QAAQ,CAAA;IACjC,MAAM0C,aAAa,GACjBD,gBAAgB,CAACC,aAAkC,CAAA;IACrD,MAAM,EACJC,gBAAgB,EAChBC,mBAAmB,EACnBC,IAAI,EACJN,OAAO,EACPO,UAAU,EACVC,YAAY,GAAG1C,MAAM,CAAC0C,YAAY,IAAK1C,MAAc,CAAC2C,eAAe,EACrEC,eAAe,EACfC,SAAS,EACT3C,YAAAA,EACD,GAAGF,MAAM,CAAA;IAEV,MAAM8C,gBAAgB,GAAGZ,OAAO,CAAC9H,SAAS,CAAA;IAE1C,MAAM2I,SAAS,GAAGlF,YAAY,CAACiF,gBAAgB,EAAE,WAAW,CAAC,CAAA;IAC7D,MAAME,MAAM,GAAGnF,YAAY,CAACiF,gBAAgB,EAAE,QAAQ,CAAC,CAAA;IACvD,MAAMG,cAAc,GAAGpF,YAAY,CAACiF,gBAAgB,EAAE,aAAa,CAAC,CAAA;IACpE,MAAMI,aAAa,GAAGrF,YAAY,CAACiF,gBAAgB,EAAE,YAAY,CAAC,CAAA;IAClE,MAAMK,aAAa,GAAGtF,YAAY,CAACiF,gBAAgB,EAAE,YAAY,CAAC,CAAA;IAElE,kEAAA;IACA,+DAAA;IACA,oFAAA;IACA,uEAAA;IACA,oEAAA;IACA,gBAAA;IACA,IAAI,OAAOP,mBAAmB,KAAK,UAAU,EAAE;QAC7C,MAAMa,QAAQ,GAAGzD,QAAQ,CAAC0D,aAAa,CAAC,UAAU,CAAC,CAAA;QACnD,IAAID,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACE,OAAO,CAACC,aAAa,EAAE;YACtD5D,QAAQ,GAAGyD,QAAQ,CAACE,OAAO,CAACC,aAAa,CAAA;QAC3C,CAAA;IACF,CAAA;IAEA,IAAIC,kBAAkB,CAAA;IACtB,IAAIC,SAAS,GAAG,EAAE,CAAA;IAElB,MAAM,EACJC,cAAc,EACdC,kBAAkB,EAClBC,sBAAsB,EACtBC,oBAAAA,EACD,GAAGlE,QAAQ,CAAA;IACZ,MAAM,EAAEmE,UAAAA,EAAY,GAAG1B,gBAAgB,CAAA;IAEvC,IAAI2B,KAAK,GAAG/C,eAAe,EAAE,CAAA;IAE7B;;GAEG,GACHY,SAAS,CAACO,WAAW,GACnB,OAAOnJ,OAAO,KAAK,UAAU,IAC7B,OAAOmK,aAAa,KAAK,UAAU,IACnCO,cAAc,IACdA,cAAc,CAACM,kBAAkB,KAAKrC,SAAS,CAAA;IAEjD,MAAM,EACJjD,aAAa,EACbC,QAAQ,EACRC,WAAW,EACXC,SAAS,EACTC,SAAS,EACTE,iBAAiB,EACjBC,eAAe,EACfE,cAAAA,EACD,GAAG8E,WAAW,CAAA;IAEf,IAAI,EAAElF,gBAAAA,gBAAAA,EAAgB,GAAGkF,WAAW,CAAA;IAEpC;;;GAGG,GAEH,yBAAA,GACA,IAAIC,YAAY,GAAG,IAAI,CAAA;IACvB,MAAMC,oBAAoB,GAAGvH,QAAQ,CAAC,CAAA,CAAE,EAAE,CACxC;WAAGwH,MAAS,EACZ;WAAGA,KAAQ,EACX;WAAGA,UAAe,EAClB;WAAGA,QAAW,EACd;WAAGA,IAAS;KACb,CAAC,CAAA;IAEF,2BAAA,GACA,IAAIC,YAAY,GAAG,IAAI,CAAA;IACvB,MAAMC,oBAAoB,GAAG1H,QAAQ,CAAC,CAAA,CAAE,EAAE,CACxC;WAAG2H,IAAU,EACb;WAAGA,GAAS,EACZ;WAAGA,MAAY,EACf;WAAGA,GAAS;KACb,CAAC,CAAA;IAEF;;;;;GAKG,GACH,IAAIC,uBAAuB,GAAGnL,MAAM,CAACE,IAAI,CACvCC,MAAM,CAAC,IAAI,EAAE;QACXiL,YAAY,EAAE;YACZC,QAAQ,EAAE,IAAI;YACdC,YAAY,EAAE,KAAK;YACnBC,UAAU,EAAE,IAAI;YAChBlH,KAAK,EAAE,IAAA;SACR;QACDmH,kBAAkB,EAAE;YAClBH,QAAQ,EAAE,IAAI;YACdC,YAAY,EAAE,KAAK;YACnBC,UAAU,EAAE,IAAI;YAChBlH,KAAK,EAAE,IAAA;SACR;QACDoH,8BAA8B,EAAE;YAC9BJ,QAAQ,EAAE,IAAI;YACdC,YAAY,EAAE,KAAK;YACnBC,UAAU,EAAE,IAAI;YAChBlH,KAAK,EAAE,KAAA;QACR,CAAA;IACF,CAAA,CAAC,CACH,CAAA;IAED,+DAAA,GACA,IAAIqH,WAAW,GAAG,IAAI,CAAA;IAEtB,qEAAA,GACA,IAAIC,WAAW,GAAG,IAAI,CAAA;IAEtB,sCAAA,GACA,IAAIC,eAAe,GAAG,IAAI,CAAA;IAE1B,6CAAA,GACA,IAAIC,eAAe,GAAG,IAAI,CAAA;IAE1B,wCAAA,GACA,IAAIC,uBAAuB,GAAG,KAAK,CAAA;IAEnC;uDACuD,GACvD,IAAIC,wBAAwB,GAAG,IAAI,CAAA;IAEnC;;GAEG,GACH,IAAIC,kBAAkB,GAAG,KAAK,CAAA;IAE9B;;GAEG,GACH,IAAIC,YAAY,GAAG,IAAI,CAAA;IAEvB,wDAAA,GACA,IAAIC,cAAc,GAAG,KAAK,CAAA;IAE1B,sEAAA,GACA,IAAIC,UAAU,GAAG,KAAK,CAAA;IAEtB;0EAC0E,GAC1E,IAAIC,UAAU,GAAG,KAAK,CAAA;IAEtB;;;GAGG,GACH,IAAIC,UAAU,GAAG,KAAK,CAAA;IAEtB;sEACsE,GACtE,IAAIC,mBAAmB,GAAG,KAAK,CAAA;IAE/B;2CAC2C,GAC3C,IAAIC,mBAAmB,GAAG,KAAK,CAAA;IAE/B;;GAEG,GACH,IAAIC,YAAY,GAAG,IAAI,CAAA;IAEvB;;;;;;;;;;;;GAYG,GACH,IAAIC,oBAAoB,GAAG,KAAK,CAAA;IAChC,MAAMC,2BAA2B,GAAG,eAAe,CAAA;IAEnD,+CAAA,GACA,IAAIC,YAAY,GAAG,IAAI,CAAA;IAEvB;wEACwE,GACxE,IAAIC,QAAQ,GAAG,KAAK,CAAA;IAEpB,qDAAA,GACA,IAAIC,YAAY,GAA8B,CAAA,CAAE,CAAA;IAEhD,uDAAA,GACA,IAAIC,eAAe,GAAG,IAAI,CAAA;IAC1B,MAAMC,uBAAuB,GAAGxJ,QAAQ,CAAC,CAAA,CAAE,EAAE;QAC3C,gBAAgB;QAChB,OAAO;QACP,UAAU;QACV,MAAM;QACN,eAAe;QACf,MAAM;QACN,QAAQ;QACR,MAAM;QACN,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,WAAW;QACX,QAAQ;QACR,OAAO;QACP,KAAK;QACL,UAAU;QACV,OAAO;QACP,OAAO;QACP,OAAO;QACP,KAAK;KACN,CAAC,CAAA;IAEF,qCAAA,GACA,IAAIyJ,aAAa,GAAG,IAAI,CAAA;IACxB,MAAMC,qBAAqB,GAAG1J,QAAQ,CAAC,CAAA,CAAE,EAAE;QACzC,OAAO;QACP,OAAO;QACP,KAAK;QACL,QAAQ;QACR,OAAO;QACP,OAAO;KACR,CAAC,CAAA;IAEF,iDAAA,GACA,IAAI2J,mBAAmB,GAAG,IAAI,CAAA;IAC9B,MAAMC,2BAA2B,GAAG5J,QAAQ,CAAC,CAAA,CAAE,EAAE;QAC/C,KAAK;QACL,OAAO;QACP,KAAK;QACL,IAAI;QACJ,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,MAAM;QACN,SAAS;QACT,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;KACR,CAAC,CAAA;IAEF,MAAM6J,gBAAgB,GAAG,oCAAoC,CAAA;IAC7D,MAAMC,aAAa,GAAG,4BAA4B,CAAA;IAClD,MAAMC,cAAc,GAAG,8BAA8B,CAAA;IACrD,sBAAA,GACA,IAAIC,SAAS,GAAGD,cAAc,CAAA;IAC9B,IAAIE,cAAc,GAAG,KAAK,CAAA;IAE1B,gCAAA,GACA,IAAIC,kBAAkB,GAAG,IAAI,CAAA;IAC7B,MAAMC,0BAA0B,GAAGnK,QAAQ,CACzC,CAAA,CAAE,EACF;QAAC6J,gBAAgB;QAAEC,aAAa;QAAEC,cAAc;KAAC,EACjD1L,cAAc,CACf,CAAA;IAED,IAAI+L,8BAA8B,GAAGpK,QAAQ,CAAC,CAAA,CAAE,EAAE;QAChD,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,OAAO;KACR,CAAC,CAAA;IAEF,IAAIqK,uBAAuB,GAAGrK,QAAQ,CAAC,CAAA,CAAE,EAAE;QAAC,gBAAgB;KAAC,CAAC,CAAA;IAE9D,oDAAA;IACA,gDAAA;IACA,kDAAA;IACA,kBAAA;IACA,MAAMsK,4BAA4B,GAAGtK,QAAQ,CAAC,CAAA,CAAE,EAAE;QAChD,OAAO;QACP,OAAO;QACP,MAAM;QACN,GAAG;QACH,QAAQ;KACT,CAAC,CAAA;IAEF,qCAAA,GACA,IAAIuK,iBAAiB,GAAkC,IAAI,CAAA;IAC3D,MAAMC,4BAA4B,GAAG;QAAC,uBAAuB;QAAE,WAAW;KAAC,CAAA;IAC3E,MAAMC,yBAAyB,GAAG,WAAW,CAAA;IAC7C,IAAItK,iBAAiB,GAA0C,IAAI,CAAA;IAEnE,+CAAA,GACA,IAAIuK,MAAM,GAAkB,IAAI,CAAA;IAEhC,kDAAA,GACA,kDAAA,GAEA,MAAMC,WAAW,GAAG5H,QAAQ,CAAC0D,aAAa,CAAC,MAAM,CAAC,CAAA;IAElD,MAAMmE,iBAAiB,GAAG,SAApBA,iBAAiBA,CACrBC,SAAkB,EAAA;QAElB,OAAOA,SAAS,YAAY3L,MAAM,IAAI2L,SAAS,YAAYC,QAAQ,CAAA;KACpE,CAAA;IAED;;;;GAIG,GACH,sCAAA;IACA,MAAMC,YAAY,GAAG,SAAfA,YAAYA,GAA6B;QAAA,IAAhBC,GAAA,GAAArL,SAAA,CAAAC,MAAA,GAAA,CAAA,IAAAD,SAAA,CAAA,CAAA,CAAA,KAAAoF,SAAA,GAAApF,SAAA,CAAA,CAAA,CAAA,GAAc,CAAA,CAAE,CAAA;QAC7C,IAAI+K,MAAM,IAAIA,MAAM,KAAKM,GAAG,EAAE;YAC5B,OAAA;QACF,CAAA;QAEA,8CAAA,GACA,IAAI,CAACA,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;YACnCA,GAAG,GAAG,CAAA,CAAE,CAAA;QACV,CAAA;QAEA,wDAAA,GACAA,GAAG,GAAGtK,KAAK,CAACsK,GAAG,CAAC,CAAA;QAEhBT,iBAAiB,GACf,mDAAA;QACAC,4BAA4B,CAAC5L,OAAO,CAACoM,GAAG,CAACT,iBAAiB,CAAC,KAAK,CAAC,CAAC,GAC9DE,yBAAyB,GACzBO,GAAG,CAACT,iBAAiB,CAAA;QAE3B,iGAAA;QACApK,iBAAiB,GACfoK,iBAAiB,KAAK,uBAAuB,GACzClM,cAAc,GACdH,iBAAiB,CAAA;QAEvB,gCAAA,GACAoJ,YAAY,GAAGvI,oBAAoB,CAACiM,GAAG,EAAE,cAAc,CAAC,GACpDhL,QAAQ,CAAC,CAAA,CAAE,EAAEgL,GAAG,CAAC1D,YAAY,EAAEnH,iBAAiB,CAAC,GACjDoH,oBAAoB,CAAA;QACxBE,YAAY,GAAG1I,oBAAoB,CAACiM,GAAG,EAAE,cAAc,CAAC,GACpDhL,QAAQ,CAAC,CAAA,CAAE,EAAEgL,GAAG,CAACvD,YAAY,EAAEtH,iBAAiB,CAAC,GACjDuH,oBAAoB,CAAA;QACxBwC,kBAAkB,GAAGnL,oBAAoB,CAACiM,GAAG,EAAE,oBAAoB,CAAC,GAChEhL,QAAQ,CAAC,CAAA,CAAE,EAAEgL,GAAG,CAACd,kBAAkB,EAAE7L,cAAc,CAAC,GACpD8L,0BAA0B,CAAA;QAC9BR,mBAAmB,GAAG5K,oBAAoB,CAACiM,GAAG,EAAE,mBAAmB,CAAC,GAChEhL,QAAQ,CACNU,KAAK,CAACkJ,2BAA2B,CAAC,EAClCoB,GAAG,CAACC,iBAAiB,EACrB9K,iBAAiB,CAClB,GACDyJ,2BAA2B,CAAA;QAC/BH,aAAa,GAAG1K,oBAAoB,CAACiM,GAAG,EAAE,mBAAmB,CAAC,GAC1DhL,QAAQ,CACNU,KAAK,CAACgJ,qBAAqB,CAAC,EAC5BsB,GAAG,CAACE,iBAAiB,EACrB/K,iBAAiB,CAClB,GACDuJ,qBAAqB,CAAA;QACzBH,eAAe,GAAGxK,oBAAoB,CAACiM,GAAG,EAAE,iBAAiB,CAAC,GAC1DhL,QAAQ,CAAC,CAAA,CAAE,EAAEgL,GAAG,CAACzB,eAAe,EAAEpJ,iBAAiB,CAAC,GACpDqJ,uBAAuB,CAAA;QAC3BrB,WAAW,GAAGpJ,oBAAoB,CAACiM,GAAG,EAAE,aAAa,CAAC,GAClDhL,QAAQ,CAAC,CAAA,CAAE,EAAEgL,GAAG,CAAC7C,WAAW,EAAEhI,iBAAiB,CAAC,GAChDO,KAAK,CAAC,CAAA,CAAE,CAAC,CAAA;QACb0H,WAAW,GAAGrJ,oBAAoB,CAACiM,GAAG,EAAE,aAAa,CAAC,GAClDhL,QAAQ,CAAC,CAAA,CAAE,EAAEgL,GAAG,CAAC5C,WAAW,EAAEjI,iBAAiB,CAAC,GAChDO,KAAK,CAAC,CAAA,CAAE,CAAC,CAAA;QACb4I,YAAY,GAAGvK,oBAAoB,CAACiM,GAAG,EAAE,cAAc,CAAC,GACpDA,GAAG,CAAC1B,YAAY,GAChB,KAAK,CAAA;QACTjB,eAAe,GAAG2C,GAAG,CAAC3C,eAAe,KAAK,KAAK,CAAC,CAAA,eAAA;QAChDC,eAAe,GAAG0C,GAAG,CAAC1C,eAAe,KAAK,KAAK,CAAC,CAAA,eAAA;QAChDC,uBAAuB,GAAGyC,GAAG,CAACzC,uBAAuB,IAAI,KAAK,CAAC,CAAA,gBAAA;QAC/DC,wBAAwB,GAAGwC,GAAG,CAACxC,wBAAwB,KAAK,KAAK,CAAC,CAAA,eAAA;QAClEC,kBAAkB,GAAGuC,GAAG,CAACvC,kBAAkB,IAAI,KAAK,CAAC,CAAA,gBAAA;QACrDC,YAAY,GAAGsC,GAAG,CAACtC,YAAY,KAAK,KAAK,CAAC,CAAA,eAAA;QAC1CC,cAAc,GAAGqC,GAAG,CAACrC,cAAc,IAAI,KAAK,CAAC,CAAA,gBAAA;QAC7CG,UAAU,GAAGkC,GAAG,CAAClC,UAAU,IAAI,KAAK,CAAC,CAAA,gBAAA;QACrCC,mBAAmB,GAAGiC,GAAG,CAACjC,mBAAmB,IAAI,KAAK,CAAC,CAAA,gBAAA;QACvDC,mBAAmB,GAAGgC,GAAG,CAAChC,mBAAmB,IAAI,KAAK,CAAC,CAAA,gBAAA;QACvDH,UAAU,GAAGmC,GAAG,CAACnC,UAAU,IAAI,KAAK,CAAC,CAAA,gBAAA;QACrCI,YAAY,GAAG+B,GAAG,CAAC/B,YAAY,KAAK,KAAK,CAAC,CAAA,eAAA;QAC1CC,oBAAoB,GAAG8B,GAAG,CAAC9B,oBAAoB,IAAI,KAAK,CAAC,CAAA,gBAAA;QACzDE,YAAY,GAAG4B,GAAG,CAAC5B,YAAY,KAAK,KAAK,CAAC,CAAA,eAAA;QAC1CC,QAAQ,GAAG2B,GAAG,CAAC3B,QAAQ,IAAI,KAAK,CAAC,CAAA,gBAAA;QACjClH,gBAAc,GAAG6I,GAAG,CAACG,kBAAkB,IAAI9D,cAA0B,CAAA;QACrE2C,SAAS,GAAGgB,GAAG,CAAChB,SAAS,IAAID,cAAc,CAAA;QAC3CK,8BAA8B,GAC5BY,GAAG,CAACZ,8BAA8B,IAAIA,8BAA8B,CAAA;QACtEC,uBAAuB,GACrBW,GAAG,CAACX,uBAAuB,IAAIA,uBAAuB,CAAA;QAExDzC,uBAAuB,GAAGoD,GAAG,CAACpD,uBAAuB,IAAI,CAAA,CAAE,CAAA;QAC3D,IACEoD,GAAG,CAACpD,uBAAuB,IAC3BgD,iBAAiB,CAACI,GAAG,CAACpD,uBAAuB,CAACC,YAAY,CAAC,EAC3D;YACAD,uBAAuB,CAACC,YAAY,GAClCmD,GAAG,CAACpD,uBAAuB,CAACC,YAAY,CAAA;QAC5C,CAAA;QAEA,IACEmD,GAAG,CAACpD,uBAAuB,IAC3BgD,iBAAiB,CAACI,GAAG,CAACpD,uBAAuB,CAACK,kBAAkB,CAAC,EACjE;YACAL,uBAAuB,CAACK,kBAAkB,GACxC+C,GAAG,CAACpD,uBAAuB,CAACK,kBAAkB,CAAA;QAClD,CAAA;QAEA,IACE+C,GAAG,CAACpD,uBAAuB,IAC3B,OAAOoD,GAAG,CAACpD,uBAAuB,CAACM,8BAA8B,KAC/D,SAAS,EACX;YACAN,uBAAuB,CAACM,8BAA8B,GACpD8C,GAAG,CAACpD,uBAAuB,CAACM,8BAA8B,CAAA;QAC9D,CAAA;QAEA,IAAIO,kBAAkB,EAAE;YACtBH,eAAe,GAAG,KAAK,CAAA;QACzB,CAAA;QAEA,IAAIS,mBAAmB,EAAE;YACvBD,UAAU,GAAG,IAAI,CAAA;QACnB,CAAA;QAEA,sBAAA,GACA,IAAIQ,YAAY,EAAE;YAChBhC,YAAY,GAAGtH,QAAQ,CAAC,CAAA,CAAE,EAAEwH,IAAS,CAAC,CAAA;YACtCC,YAAY,GAAG,EAAE,CAAA;YACjB,IAAI6B,YAAY,CAAChI,IAAI,KAAK,IAAI,EAAE;gBAC9BtB,QAAQ,CAACsH,YAAY,EAAEE,MAAS,CAAC,CAAA;gBACjCxH,QAAQ,CAACyH,YAAY,EAAEE,IAAU,CAAC,CAAA;YACpC,CAAA;YAEA,IAAI2B,YAAY,CAAC/H,GAAG,KAAK,IAAI,EAAE;gBAC7BvB,QAAQ,CAACsH,YAAY,EAAEE,KAAQ,CAAC,CAAA;gBAChCxH,QAAQ,CAACyH,YAAY,EAAEE,GAAS,CAAC,CAAA;gBACjC3H,QAAQ,CAACyH,YAAY,EAAEE,GAAS,CAAC,CAAA;YACnC,CAAA;YAEA,IAAI2B,YAAY,CAAC9H,UAAU,KAAK,IAAI,EAAE;gBACpCxB,QAAQ,CAACsH,YAAY,EAAEE,UAAe,CAAC,CAAA;gBACvCxH,QAAQ,CAACyH,YAAY,EAAEE,GAAS,CAAC,CAAA;gBACjC3H,QAAQ,CAACyH,YAAY,EAAEE,GAAS,CAAC,CAAA;YACnC,CAAA;YAEA,IAAI2B,YAAY,CAAC5H,MAAM,KAAK,IAAI,EAAE;gBAChC1B,QAAQ,CAACsH,YAAY,EAAEE,QAAW,CAAC,CAAA;gBACnCxH,QAAQ,CAACyH,YAAY,EAAEE,MAAY,CAAC,CAAA;gBACpC3H,QAAQ,CAACyH,YAAY,EAAEE,GAAS,CAAC,CAAA;YACnC,CAAA;QACF,CAAA;QAEA,kCAAA,GACA,IAAIqD,GAAG,CAACI,QAAQ,EAAE;YAChB,IAAI9D,YAAY,KAAKC,oBAAoB,EAAE;gBACzCD,YAAY,GAAG5G,KAAK,CAAC4G,YAAY,CAAC,CAAA;YACpC,CAAA;YAEAtH,QAAQ,CAACsH,YAAY,EAAE0D,GAAG,CAACI,QAAQ,EAAEjL,iBAAiB,CAAC,CAAA;QACzD,CAAA;QAEA,IAAI6K,GAAG,CAACK,QAAQ,EAAE;YAChB,IAAI5D,YAAY,KAAKC,oBAAoB,EAAE;gBACzCD,YAAY,GAAG/G,KAAK,CAAC+G,YAAY,CAAC,CAAA;YACpC,CAAA;YAEAzH,QAAQ,CAACyH,YAAY,EAAEuD,GAAG,CAACK,QAAQ,EAAElL,iBAAiB,CAAC,CAAA;QACzD,CAAA;QAEA,IAAI6K,GAAG,CAACC,iBAAiB,EAAE;YACzBjL,QAAQ,CAAC2J,mBAAmB,EAAEqB,GAAG,CAACC,iBAAiB,EAAE9K,iBAAiB,CAAC,CAAA;QACzE,CAAA;QAEA,IAAI6K,GAAG,CAACzB,eAAe,EAAE;YACvB,IAAIA,eAAe,KAAKC,uBAAuB,EAAE;gBAC/CD,eAAe,GAAG7I,KAAK,CAAC6I,eAAe,CAAC,CAAA;YAC1C,CAAA;YAEAvJ,QAAQ,CAACuJ,eAAe,EAAEyB,GAAG,CAACzB,eAAe,EAAEpJ,iBAAiB,CAAC,CAAA;QACnE,CAAA;QAEA,iDAAA,GACA,IAAIiJ,YAAY,EAAE;YAChB9B,YAAY,CAAC,OAAO,CAAC,GAAG,IAAI,CAAA;QAC9B,CAAA;QAEA,0EAAA,GACA,IAAIqB,cAAc,EAAE;YAClB3I,QAAQ,CAACsH,YAAY,EAAE;gBAAC,MAAM;gBAAE,MAAM;gBAAE,MAAM;aAAC,CAAC,CAAA;QAClD,CAAA;QAEA,0EAAA,GACA,IAAIA,YAAY,CAACgE,KAAK,EAAE;YACtBtL,QAAQ,CAACsH,YAAY,EAAE;gBAAC,OAAO;aAAC,CAAC,CAAA;YACjC,OAAOa,WAAW,CAACoD,KAAK,CAAA;QAC1B,CAAA;QAEA,IAAIP,GAAG,CAACQ,oBAAoB,EAAE;YAC5B,IAAI,OAAOR,GAAG,CAACQ,oBAAoB,CAAC1H,UAAU,KAAK,UAAU,EAAE;gBAC7D,MAAM1E,eAAe,CACnB,6EAA6E,CAC9E,CAAA;YACH,CAAA;YAEA,IAAI,OAAO4L,GAAG,CAACQ,oBAAoB,CAACzH,eAAe,KAAK,UAAU,EAAE;gBAClE,MAAM3E,eAAe,CACnB,kFAAkF,CACnF,CAAA;YACH,CAAA;YAEA,0CAAA;YACAwH,kBAAkB,GAAGoE,GAAG,CAACQ,oBAAoB,CAAA;YAE7C,+CAAA;YACA3E,SAAS,GAAGD,kBAAkB,CAAC9C,UAAU,CAAC,EAAE,CAAC,CAAA;QAC/C,CAAC,MAAM;YACL,6EAAA;YACA,IAAI8C,kBAAkB,KAAK7B,SAAS,EAAE;gBACpC6B,kBAAkB,GAAGvD,yBAAyB,CAC5CC,YAAY,EACZmC,aAAa,CACd,CAAA;YACH,CAAA;YAEA,qEAAA;YACA,IAAImB,kBAAkB,KAAK,IAAI,IAAI,OAAOC,SAAS,KAAK,QAAQ,EAAE;gBAChEA,SAAS,GAAGD,kBAAkB,CAAC9C,UAAU,CAAC,EAAE,CAAC,CAAA;YAC/C,CAAA;QACF,CAAA;QAEA,iDAAA;QACA,uCAAA;QACA,IAAIpH,MAAM,EAAE;YACVA,MAAM,CAACsO,GAAG,CAAC,CAAA;QACb,CAAA;QAEAN,MAAM,GAAGM,GAAG,CAAA;KACb,CAAA;IAED;;gBAEgB,GAChB,MAAMS,YAAY,GAAGzL,QAAQ,CAAC,CAAA,CAAE,EAAE,CAChC;WAAGwH,KAAQ,EACX;WAAGA,UAAe,EAClB;WAAGA,aAAkB;KACtB,CAAC,CAAA;IACF,MAAMkE,eAAe,GAAG1L,QAAQ,CAAC,CAAA,CAAE,EAAE,CACnC;WAAGwH,QAAW,EACd;WAAGA,gBAAqB;KACzB,CAAC,CAAA;IAEF;;;;;GAKG,GACH,MAAMmE,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAatL,OAAgB,EAAA;QACrD,IAAIuL,MAAM,GAAGrF,aAAa,CAAClG,OAAO,CAAC,CAAA;QAEnC,wDAAA;QACA,qDAAA;QACA,IAAI,CAACuL,MAAM,IAAI,CAACA,MAAM,CAACC,OAAO,EAAE;YAC9BD,MAAM,GAAG;gBACPE,YAAY,EAAE9B,SAAS;gBACvB6B,OAAO,EAAE,UAAA;aACV,CAAA;QACH,CAAA;QAEA,MAAMA,OAAO,GAAG3N,iBAAiB,CAACmC,OAAO,CAACwL,OAAO,CAAC,CAAA;QAClD,MAAME,aAAa,GAAG7N,iBAAiB,CAAC0N,MAAM,CAACC,OAAO,CAAC,CAAA;QAEvD,IAAI,CAAC3B,kBAAkB,CAAC7J,OAAO,CAACyL,YAAY,CAAC,EAAE;YAC7C,OAAO,KAAK,CAAA;QACd,CAAA;QAEA,IAAIzL,OAAO,CAACyL,YAAY,KAAKhC,aAAa,EAAE;YAC1C,oDAAA;YACA,sDAAA;YACA,uBAAA;YACA,IAAI8B,MAAM,CAACE,YAAY,KAAK/B,cAAc,EAAE;gBAC1C,OAAO8B,OAAO,KAAK,KAAK,CAAA;YAC1B,CAAA;YAEA,oDAAA;YACA,qDAAA;YACA,2BAAA;YACA,IAAID,MAAM,CAACE,YAAY,KAAKjC,gBAAgB,EAAE;gBAC5C,OACEgC,OAAO,KAAK,KAAK,IAAA,CAChBE,aAAa,KAAK,gBAAgB,IACjC3B,8BAA8B,CAAC2B,aAAa,CAAC,CAAC,CAAA;YAEpD,CAAA;YAEA,iDAAA;YACA,oDAAA;YACA,OAAOC,OAAO,CAACP,YAAY,CAACI,OAAO,CAAC,CAAC,CAAA;QACvC,CAAA;QAEA,IAAIxL,OAAO,CAACyL,YAAY,KAAKjC,gBAAgB,EAAE;YAC7C,uDAAA;YACA,uDAAA;YACA,uBAAA;YACA,IAAI+B,MAAM,CAACE,YAAY,KAAK/B,cAAc,EAAE;gBAC1C,OAAO8B,OAAO,KAAK,MAAM,CAAA;YAC3B,CAAA;YAEA,mDAAA;YACA,qCAAA;YACA,IAAID,MAAM,CAACE,YAAY,KAAKhC,aAAa,EAAE;gBACzC,OAAO+B,OAAO,KAAK,MAAM,IAAIxB,uBAAuB,CAAC0B,aAAa,CAAC,CAAA;YACrE,CAAA;YAEA,oDAAA;YACA,uDAAA;YACA,OAAOC,OAAO,CAACN,eAAe,CAACG,OAAO,CAAC,CAAC,CAAA;QAC1C,CAAA;QAEA,IAAIxL,OAAO,CAACyL,YAAY,KAAK/B,cAAc,EAAE;YAC3C,iDAAA;YACA,mDAAA;YACA,wCAAA;YACA,IACE6B,MAAM,CAACE,YAAY,KAAKhC,aAAa,IACrC,CAACO,uBAAuB,CAAC0B,aAAa,CAAC,EACvC;gBACA,OAAO,KAAK,CAAA;YACd,CAAA;YAEA,IACEH,MAAM,CAACE,YAAY,KAAKjC,gBAAgB,IACxC,CAACO,8BAA8B,CAAC2B,aAAa,CAAC,EAC9C;gBACA,OAAO,KAAK,CAAA;YACd,CAAA;YAEA,gDAAA;YACA,mDAAA;YACA,OACE,CAACL,eAAe,CAACG,OAAO,CAAC,IAAA,CACxBvB,4BAA4B,CAACuB,OAAO,CAAC,IAAI,CAACJ,YAAY,CAACI,OAAO,CAAC,CAAC,CAAA;QAErE,CAAA;QAEA,6DAAA;QACA,IACEtB,iBAAiB,KAAK,uBAAuB,IAC7CL,kBAAkB,CAAC7J,OAAO,CAACyL,YAAY,CAAC,EACxC;YACA,OAAO,IAAI,CAAA;QACb,CAAA;QAEA,qDAAA;QACA,qDAAA;QACA,wDAAA;QACA,6BAAA;QACA,OAAO,KAAK,CAAA;KACb,CAAA;IAED;;;;GAIG,GACH,MAAMG,YAAY,GAAG,SAAfA,YAAYA,CAAaC,IAAU,EAAA;QACvCpO,SAAS,CAACkH,SAAS,CAACI,OAAO,EAAE;YAAE/E,OAAO,EAAE6L,IAAAA;QAAM,CAAA,CAAC,CAAA;QAE/C,IAAI;YACF,0DAAA;YACA3F,aAAa,CAAC2F,IAAI,CAAC,CAACC,WAAW,CAACD,IAAI,CAAC,CAAA;SACtC,CAAC,OAAOjI,CAAC,EAAE;YACVmC,MAAM,CAAC8F,IAAI,CAAC,CAAA;QACd,CAAA;KACD,CAAA;IAED;;;;;GAKG,GACH,MAAME,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAaC,IAAY,EAAEhM,OAAgB,EAAA;QAC/D,IAAI;YACFvC,SAAS,CAACkH,SAAS,CAACI,OAAO,EAAE;gBAC3B3C,SAAS,EAAEpC,OAAO,CAACiM,gBAAgB,CAACD,IAAI,CAAC;gBACzCE,IAAI,EAAElM,OAAAA;YACP,CAAA,CAAC,CAAA;SACH,CAAC,OAAO4D,CAAC,EAAE;YACVnG,SAAS,CAACkH,SAAS,CAACI,OAAO,EAAE;gBAC3B3C,SAAS,EAAE,IAAI;gBACf8J,IAAI,EAAElM,OAAAA;YACP,CAAA,CAAC,CAAA;QACJ,CAAA;QAEAA,OAAO,CAACmM,eAAe,CAACH,IAAI,CAAC,CAAA;QAE7B,2DAAA;QACA,IAAIA,IAAI,KAAK,IAAI,EAAE;YACjB,IAAIvD,UAAU,IAAIC,mBAAmB,EAAE;gBACrC,IAAI;oBACFkD,YAAY,CAAC5L,OAAO,CAAC,CAAA;gBACvB,CAAC,CAAC,OAAO4D,CAAC,EAAE,CAAA,CAAC;YACf,CAAC,MAAM;gBACL,IAAI;oBACF5D,OAAO,CAACoM,YAAY,CAACJ,IAAI,EAAE,EAAE,CAAC,CAAA;gBAChC,CAAC,CAAC,OAAOpI,CAAC,EAAE,CAAA,CAAC;YACf,CAAA;QACF,CAAA;KACD,CAAA;IAED;;;;;GAKG,GACH,MAAMyI,aAAa,GAAG,SAAhBA,aAAaA,CAAaC,KAAa,EAAA;QAC3C,0BAAA,GACA,IAAIC,GAAG,GAAG,IAAI,CAAA;QACd,IAAIC,iBAAiB,GAAG,IAAI,CAAA;QAE5B,IAAIhE,UAAU,EAAE;YACd8D,KAAK,GAAG,mBAAmB,GAAGA,KAAK,CAAA;QACrC,CAAC,MAAM;YACL,+EAAA,GACA,MAAMG,OAAO,GAAGvO,WAAW,CAACoO,KAAK,EAAE,aAAa,CAAC,CAAA;YACjDE,iBAAiB,GAAGC,OAAO,IAAIA,OAAO,CAAC,CAAC,CAAC,CAAA;QAC3C,CAAA;QAEA,IACEvC,iBAAiB,KAAK,uBAAuB,IAC7CP,SAAS,KAAKD,cAAc,EAC5B;YACA,4GAAA;YACA4C,KAAK,GACH,gEAAgE,GAChEA,KAAK,GACL,gBAAgB,CAAA;QACpB,CAAA;QAEA,MAAMI,YAAY,GAAGnG,kBAAkB,GACnCA,kBAAkB,CAAC9C,UAAU,CAAC6I,KAAK,CAAC,GACpCA,KAAK,CAAA;QACT;;;KAGG,GACH,IAAI3C,SAAS,KAAKD,cAAc,EAAE;YAChC,IAAI;gBACF6C,GAAG,GAAG,IAAI3G,SAAS,EAAE,CAAC+G,eAAe,CAACD,YAAY,EAAExC,iBAAiB,CAAC,CAAA;YACxE,CAAC,CAAC,OAAOtG,CAAC,EAAE,CAAA,CAAC;QACf,CAAA;QAEA,6DAAA,GACA,IAAI,CAAC2I,GAAG,IAAI,CAACA,GAAG,CAACK,eAAe,EAAE;YAChCL,GAAG,GAAG9F,cAAc,CAACoG,cAAc,CAAClD,SAAS,EAAE,UAAU,EAAE,IAAI,CAAC,CAAA;YAChE,IAAI;gBACF4C,GAAG,CAACK,eAAe,CAACE,SAAS,GAAGlD,cAAc,GAC1CpD,SAAS,GACTkG,YAAY,CAAA;aACjB,CAAC,OAAO9I,CAAC,EAAE;YACV,8CAAA;YAAA,CAAA;QAEJ,CAAA;QAEA,MAAMmJ,IAAI,GAAGR,GAAG,CAACQ,IAAI,IAAIR,GAAG,CAACK,eAAe,CAAA;QAE5C,IAAIN,KAAK,IAAIE,iBAAiB,EAAE;YAC9BO,IAAI,CAACC,YAAY,CACftK,QAAQ,CAACuK,cAAc,CAACT,iBAAiB,CAAC,EAC1CO,IAAI,CAACG,UAAU,CAAC,CAAC,CAAC,IAAI,IAAI,CAC3B,CAAA;QACH,CAAA;QAEA,2CAAA,GACA,IAAIvD,SAAS,KAAKD,cAAc,EAAE;YAChC,OAAO9C,oBAAoB,CAACuG,IAAI,CAC9BZ,GAAG,EACHjE,cAAc,GAAG,MAAM,GAAG,MAAM,CACjC,CAAC,CAAC,CAAC,CAAA;QACN,CAAA;QAEA,OAAOA,cAAc,GAAGiE,GAAG,CAACK,eAAe,GAAGG,IAAI,CAAA;KACnD,CAAA;IAED;;;;;GAKG,GACH,MAAMK,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAaxI,IAAU,EAAA;QAC9C,OAAO8B,kBAAkB,CAACyG,IAAI,CAC5BvI,IAAI,CAAC0B,aAAa,IAAI1B,IAAI,EAC1BA,IAAI,EACJ,sCAAA;QACAY,UAAU,CAAC6H,YAAY,GACrB7H,UAAU,CAAC8H,YAAY,GACvB9H,UAAU,CAAC+H,SAAS,GACpB/H,UAAU,CAACgI,2BAA2B,GACtChI,UAAU,CAACiI,kBAAkB,EAC/B,IAAI,CACL,CAAA;KACF,CAAA;IAED;;;;;GAKG,GACH,MAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAa1N,OAAgB,EAAA;QAC7C,OACEA,OAAO,YAAY2F,eAAe,IAAA,CACjC,OAAO3F,OAAO,CAAC2N,QAAQ,KAAK,QAAQ,IACnC,OAAO3N,OAAO,CAAC4N,WAAW,KAAK,QAAQ,IACvC,OAAO5N,OAAO,CAAC8L,WAAW,KAAK,UAAU,IACzC,CAAA,CAAE9L,OAAO,CAAC6N,UAAU,YAAYpI,YAAY,CAAC,IAC7C,OAAOzF,OAAO,CAACmM,eAAe,KAAK,UAAU,IAC7C,OAAOnM,OAAO,CAACoM,YAAY,KAAK,UAAU,IAC1C,OAAOpM,OAAO,CAACyL,YAAY,KAAK,QAAQ,IACxC,OAAOzL,OAAO,CAACgN,YAAY,KAAK,UAAU,IAC1C,OAAOhN,OAAO,CAAC8N,aAAa,KAAK,UAAU,CAAC,CAAA;KAEjD,CAAA;IAED;;;;;GAKG,GACH,MAAMC,OAAO,GAAG,SAAVA,OAAOA,CAAatN,KAAc,EAAA;QACtC,OAAO,OAAO8E,IAAI,KAAK,UAAU,IAAI9E,KAAK,YAAY8E,IAAI,CAAA;KAC3D,CAAA;IAED,SAASyI,aAAaA,CAOpBlH,KAAU,EAAEmH,WAA6B,EAAEC,IAAsB,EAAA;QACjElR,YAAY,CAAC8J,KAAK,GAAGqH,IAAI,IAAI;YAC3BA,IAAI,CAAChB,IAAI,CAACxI,SAAS,EAAEsJ,WAAW,EAAEC,IAAI,EAAE7D,MAAM,CAAC,CAAA;QACjD,CAAC,CAAC,CAAA;IACJ,CAAA;IAEA;;;;;;;;GAQG,GACH,MAAM+D,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAaH,WAAgB,EAAA;QAClD,IAAI5H,OAAO,GAAG,IAAI,CAAA;QAElB,6BAAA,GACA2H,aAAa,CAAClH,KAAK,CAAC1C,sBAAsB,EAAE6J,WAAW,EAAE,IAAI,CAAC,CAAA;QAE9D,gDAAA,GACA,IAAIP,YAAY,CAACO,WAAW,CAAC,EAAE;YAC7BrC,YAAY,CAACqC,WAAW,CAAC,CAAA;YACzB,OAAO,IAAI,CAAA;QACb,CAAA;QAEA,+CAAA,GACA,MAAMzC,OAAO,GAAG1L,iBAAiB,CAACmO,WAAW,CAACN,QAAQ,CAAC,CAAA;QAEvD,6BAAA,GACAK,aAAa,CAAClH,KAAK,CAACvC,mBAAmB,EAAE0J,WAAW,EAAE;YACpDzC,OAAO;YACP6C,WAAW,EAAEpH,YAAAA;QACd,CAAA,CAAC,CAAA;QAEF,oDAAA,GACA,IACEoB,YAAY,IACZ4F,WAAW,CAACH,aAAa,EAAE,IAC3B,CAACC,OAAO,CAACE,WAAW,CAACK,iBAAiB,CAAC,IACvC1P,UAAU,CAAC,UAAU,EAAEqP,WAAW,CAACnB,SAAS,CAAC,IAC7ClO,UAAU,CAAC,UAAU,EAAEqP,WAAW,CAACL,WAAW,CAAC,EAC/C;YACAhC,YAAY,CAACqC,WAAW,CAAC,CAAA;YACzB,OAAO,IAAI,CAAA;QACb,CAAA;QAEA,oDAAA,GACA,IAAIA,WAAW,CAACjJ,QAAQ,KAAK7C,SAAS,CAACK,sBAAsB,EAAE;YAC7DoJ,YAAY,CAACqC,WAAW,CAAC,CAAA;YACzB,OAAO,IAAI,CAAA;QACb,CAAA;QAEA,gDAAA,GACA,IACE5F,YAAY,IACZ4F,WAAW,CAACjJ,QAAQ,KAAK7C,SAAS,CAACM,OAAO,IAC1C7D,UAAU,CAAC,SAAS,EAAEqP,WAAW,CAACC,IAAI,CAAC,EACvC;YACAtC,YAAY,CAACqC,WAAW,CAAC,CAAA;YACzB,OAAO,IAAI,CAAA;QACb,CAAA;QAEA,mDAAA,GACA,IAAI,CAAChH,YAAY,CAACuE,OAAO,CAAC,IAAI1D,WAAW,CAAC0D,OAAO,CAAC,EAAE;YAClD,+CAAA,GACA,IAAI,CAAC1D,WAAW,CAAC0D,OAAO,CAAC,IAAI+C,qBAAqB,CAAC/C,OAAO,CAAC,EAAE;gBAC3D,IACEjE,uBAAuB,CAACC,YAAY,YAAY3I,MAAM,IACtDD,UAAU,CAAC2I,uBAAuB,CAACC,YAAY,EAAEgE,OAAO,CAAC,EACzD;oBACA,OAAO,KAAK,CAAA;gBACd,CAAA;gBAEA,IACEjE,uBAAuB,CAACC,YAAY,YAAYiD,QAAQ,IACxDlD,uBAAuB,CAACC,YAAY,CAACgE,OAAO,CAAC,EAC7C;oBACA,OAAO,KAAK,CAAA;gBACd,CAAA;YACF,CAAA;YAEA,+CAAA,GACA,IAAIzC,YAAY,IAAI,CAACG,eAAe,CAACsC,OAAO,CAAC,EAAE;gBAC7C,MAAMgD,UAAU,GAAGtI,aAAa,CAAC+H,WAAW,CAAC,IAAIA,WAAW,CAACO,UAAU,CAAA;gBACvE,MAAMtB,UAAU,GAAGjH,aAAa,CAACgI,WAAW,CAAC,IAAIA,WAAW,CAACf,UAAU,CAAA;gBAEvE,IAAIA,UAAU,IAAIsB,UAAU,EAAE;oBAC5B,MAAMC,UAAU,GAAGvB,UAAU,CAAC3N,MAAM,CAAA;oBAEpC,IAAK,IAAImP,CAAC,GAAGD,UAAU,GAAG,CAAC,EAAEC,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,CAAE;wBACxC,MAAMC,UAAU,GAAG7I,SAAS,CAACoH,UAAU,CAACwB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;wBACjDC,UAAU,CAACC,cAAc,GAAG,CAACX,WAAW,CAACW,cAAc,IAAI,CAAC,IAAI,CAAC,CAAA;wBACjEJ,UAAU,CAACxB,YAAY,CAAC2B,UAAU,EAAE3I,cAAc,CAACiI,WAAW,CAAC,CAAC,CAAA;oBAClE,CAAA;gBACF,CAAA;YACF,CAAA;YAEArC,YAAY,CAACqC,WAAW,CAAC,CAAA;YACzB,OAAO,IAAI,CAAA;QACb,CAAA;QAEA,+CAAA,GACA,IAAIA,WAAW,YAAYhJ,OAAO,IAAI,CAACqG,oBAAoB,CAAC2C,WAAW,CAAC,EAAE;YACxErC,YAAY,CAACqC,WAAW,CAAC,CAAA;YACzB,OAAO,IAAI,CAAA;QACb,CAAA;QAEA,6DAAA,GACA,IACE,CAACzC,OAAO,KAAK,UAAU,IACrBA,OAAO,KAAK,SAAS,IACrBA,OAAO,KAAK,UAAU,KACxB5M,UAAU,CAAC,6BAA6B,EAAEqP,WAAW,CAACnB,SAAS,CAAC,EAChE;YACAlB,YAAY,CAACqC,WAAW,CAAC,CAAA;YACzB,OAAO,IAAI,CAAA;QACb,CAAA;QAEA,gDAAA,GACA,IAAI7F,kBAAkB,IAAI6F,WAAW,CAACjJ,QAAQ,KAAK7C,SAAS,CAACZ,IAAI,EAAE;YACjE,kCAAA,GACA8E,OAAO,GAAG4H,WAAW,CAACL,WAAW,CAAA;YAEjC5Q,YAAY,CAAC;gBAACyE,aAAa;gBAAEC,QAAQ;gBAAEC,WAAW;aAAC,GAAGkN,IAAI,IAAI;gBAC5DxI,OAAO,GAAGjI,aAAa,CAACiI,OAAO,EAAEwI,IAAI,EAAE,GAAG,CAAC,CAAA;YAC7C,CAAC,CAAC,CAAA;YAEF,IAAIZ,WAAW,CAACL,WAAW,KAAKvH,OAAO,EAAE;gBACvC5I,SAAS,CAACkH,SAAS,CAACI,OAAO,EAAE;oBAAE/E,OAAO,EAAEiO,WAAW,CAACnI,SAAS,EAAE;gBAAA,CAAE,CAAC,CAAA;gBAClEmI,WAAW,CAACL,WAAW,GAAGvH,OAAO,CAAA;YACnC,CAAA;QACF,CAAA;QAEA,6BAAA,GACA2H,aAAa,CAAClH,KAAK,CAAC7C,qBAAqB,EAAEgK,WAAW,EAAE,IAAI,CAAC,CAAA;QAE7D,OAAO,KAAK,CAAA;KACb,CAAA;IAED;;;;;;;GAOG,GACH,sCAAA;IACA,MAAMa,iBAAiB,GAAG,SAApBA,iBAAiBA,CACrBC,KAAa,EACbC,MAAc,EACdvO,KAAa,EAAA;QAEb,sCAAA,GACA,IACEmI,YAAY,IAAA,CACXoG,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,MAAM,CAAC,IAAA,CACrCvO,KAAK,IAAIiC,QAAQ,IAAIjC,KAAK,IAAI6J,WAAW,CAAC,EAC3C;YACA,OAAO,KAAK,CAAA;QACd,CAAA;QAEA;;;gEAG8D,GAC9D,IACErC,eAAe,IACf,CAACF,WAAW,CAACiH,MAAM,CAAC,IACpBpQ,UAAU,CAACgD,SAAS,EAAEoN,MAAM,CAAC,EAC7B,CAED;aAAM,IAAIhH,eAAe,IAAIpJ,UAAU,CAACiD,SAAS,EAAEmN,MAAM,CAAC,EAAE,CAG5D;aAAM,IAAI,CAAC5H,YAAY,CAAC4H,MAAM,CAAC,IAAIjH,WAAW,CAACiH,MAAM,CAAC,EAAE;YACvD,IACE,kGAAA;YACA,qGAAA;YACA,sHAAA;YACCT,qBAAqB,CAACQ,KAAK,CAAC,IAAA,CACzBxH,uBAAuB,CAACC,YAAY,YAAY3I,MAAM,IACtDD,UAAU,CAAC2I,uBAAuB,CAACC,YAAY,EAAEuH,KAAK,CAAC,IACtDxH,uBAAuB,CAACC,YAAY,YAAYiD,QAAQ,IACvDlD,uBAAuB,CAACC,YAAY,CAACuH,KAAK,CAAE,CAAC,IAAA,CAC/CxH,uBAAuB,CAACK,kBAAkB,YAAY/I,MAAM,IAC5DD,UAAU,CAAC2I,uBAAuB,CAACK,kBAAkB,EAAEoH,MAAM,CAAC,IAC7DzH,uBAAuB,CAACK,kBAAkB,YAAY6C,QAAQ,IAC7DlD,uBAAuB,CAACK,kBAAkB,CAACoH,MAAM,CAAE,CAAC,IAC1D,sEAAA;YACA,6FAAA;YACCA,MAAM,KAAK,IAAI,IACdzH,uBAAuB,CAACM,8BAA8B,IAAA,CACpDN,uBAAuB,CAACC,YAAY,YAAY3I,MAAM,IACtDD,UAAU,CAAC2I,uBAAuB,CAACC,YAAY,EAAE/G,KAAK,CAAC,IACtD8G,uBAAuB,CAACC,YAAY,YAAYiD,QAAQ,IACvDlD,uBAAuB,CAACC,YAAY,CAAC/G,KAAK,CAAE,CAAE,EACpD,CAGD;iBAAM;gBACL,OAAO,KAAK,CAAA;YACd,CAAA;QACA,6DAAA,GACF,CAAC,MAAM,IAAI6I,mBAAmB,CAAC0F,MAAM,CAAC,EAAE,CAIvC;aAAM,IACLpQ,UAAU,CAACkD,gBAAc,EAAE1D,aAAa,CAACqC,KAAK,EAAEuB,eAAe,EAAE,EAAE,CAAC,CAAC,EACrE,CAID;aAAM,IACL,CAACgN,MAAM,KAAK,KAAK,IAAIA,MAAM,KAAK,YAAY,IAAIA,MAAM,KAAK,MAAM,KACjED,KAAK,KAAK,QAAQ,IAClBzQ,aAAa,CAACmC,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,IACnC2I,aAAa,CAAC2F,KAAK,CAAC,EACpB,CAKD;aAAM,IACL7G,uBAAuB,IACvB,CAACtJ,UAAU,CAACmD,iBAAiB,EAAE3D,aAAa,CAACqC,KAAK,EAAEuB,eAAe,EAAE,EAAE,CAAC,CAAC,EACzE,CAGD;aAAM,IAAIvB,KAAK,EAAE;YAChB,OAAO,KAAK,CAAA;QACd,CAAC,MAAM,CAEL;QAGF,OAAO,IAAI,CAAA;KACZ,CAAA;IAED;;;;;;;GAOG,GACH,MAAM8N,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAa/C,OAAe,EAAA;QACrD,OAAOA,OAAO,KAAK,gBAAgB,IAAItN,WAAW,CAACsN,OAAO,EAAEtJ,cAAc,CAAC,CAAA;KAC5E,CAAA;IAED;;;;;;;;;GASG,GACH,MAAM+M,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAahB,WAAoB,EAAA;QACxD,6BAAA,GACAD,aAAa,CAAClH,KAAK,CAAC3C,wBAAwB,EAAE8J,WAAW,EAAE,IAAI,CAAC,CAAA;QAEhE,MAAM,EAAEJ,UAAAA,EAAY,GAAGI,WAAW,CAAA;QAElC,iEAAA,GACA,IAAI,CAACJ,UAAU,IAAIH,YAAY,CAACO,WAAW,CAAC,EAAE;YAC5C,OAAA;QACF,CAAA;QAEA,MAAMiB,SAAS,GAAG;YAChBC,QAAQ,EAAE,EAAE;YACZC,SAAS,EAAE,EAAE;YACbC,QAAQ,EAAE,IAAI;YACdC,iBAAiB,EAAElI,YAAY;YAC/BmI,aAAa,EAAE7K,SAAAA;SAChB,CAAA;QACD,IAAI3E,CAAC,GAAG8N,UAAU,CAACtO,MAAM,CAAA;QAEzB,4DAAA,GACA,MAAOQ,CAAC,EAAE,CAAE;YACV,MAAMyP,IAAI,GAAG3B,UAAU,CAAC9N,CAAC,CAAC,CAAA;YAC1B,MAAM,EAAEiM,IAAI,EAAEP,YAAY,EAAEhL,KAAK,EAAE2O,SAAAA,EAAW,GAAGI,IAAI,CAAA;YACrD,MAAMR,MAAM,GAAGlP,iBAAiB,CAACkM,IAAI,CAAC,CAAA;YAEtC,MAAMyD,SAAS,GAAGL,SAAS,CAAA;YAC3B,IAAI3O,KAAK,GAAGuL,IAAI,KAAK,OAAO,GAAGyD,SAAS,GAAGjR,UAAU,CAACiR,SAAS,CAAC,CAAA;YAEhE,6BAAA,GACAP,SAAS,CAACC,QAAQ,GAAGH,MAAM,CAAA;YAC3BE,SAAS,CAACE,SAAS,GAAG3O,KAAK,CAAA;YAC3ByO,SAAS,CAACG,QAAQ,GAAG,IAAI,CAAA;YACzBH,SAAS,CAACK,aAAa,GAAG7K,SAAS,CAAC,CAAA,2DAAA;YACpCsJ,aAAa,CAAClH,KAAK,CAACxC,qBAAqB,EAAE2J,WAAW,EAAEiB,SAAS,CAAC,CAAA;YAClEzO,KAAK,GAAGyO,SAAS,CAACE,SAAS,CAAA;YAE3B;;OAEG,GACH,IAAIvG,oBAAoB,IAAA,CAAKmG,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,MAAM,CAAC,EAAE;gBAClE,uCAAA;gBACAjD,gBAAgB,CAACC,IAAI,EAAEiC,WAAW,CAAC,CAAA;gBAEnC,8EAAA;gBACAxN,KAAK,GAAGqI,2BAA2B,GAAGrI,KAAK,CAAA;YAC7C,CAAA;YAEA,gEAAA,GACA,IAAI4H,YAAY,IAAIzJ,UAAU,CAAC,+BAA+B,EAAE6B,KAAK,CAAC,EAAE;gBACtEsL,gBAAgB,CAACC,IAAI,EAAEiC,WAAW,CAAC,CAAA;gBACnC,SAAA;YACF,CAAA;YAEA,2CAAA,GACA,IAAIiB,SAAS,CAACK,aAAa,EAAE;gBAC3B,SAAA;YACF,CAAA;YAEA,2CAAA,GACA,IAAI,CAACL,SAAS,CAACG,QAAQ,EAAE;gBACvBtD,gBAAgB,CAACC,IAAI,EAAEiC,WAAW,CAAC,CAAA;gBACnC,SAAA;YACF,CAAA;YAEA,8CAAA,GACA,IAAI,CAAC9F,wBAAwB,IAAIvJ,UAAU,CAAC,MAAM,EAAE6B,KAAK,CAAC,EAAE;gBAC1DsL,gBAAgB,CAACC,IAAI,EAAEiC,WAAW,CAAC,CAAA;gBACnC,SAAA;YACF,CAAA;YAEA,kDAAA,GACA,IAAI7F,kBAAkB,EAAE;gBACtBpL,YAAY,CAAC;oBAACyE,aAAa;oBAAEC,QAAQ;oBAAEC,WAAW;iBAAC,GAAGkN,IAAI,IAAI;oBAC5DpO,KAAK,GAAGrC,aAAa,CAACqC,KAAK,EAAEoO,IAAI,EAAE,GAAG,CAAC,CAAA;gBACzC,CAAC,CAAC,CAAA;YACJ,CAAA;YAEA,wCAAA,GACA,MAAME,KAAK,GAAGjP,iBAAiB,CAACmO,WAAW,CAACN,QAAQ,CAAC,CAAA;YACrD,IAAI,CAACmB,iBAAiB,CAACC,KAAK,EAAEC,MAAM,EAAEvO,KAAK,CAAC,EAAE;gBAC5CsL,gBAAgB,CAACC,IAAI,EAAEiC,WAAW,CAAC,CAAA;gBACnC,SAAA;YACF,CAAA;YAEA,gDAAA,GACA,IACE1H,kBAAkB,IAClB,OAAOtD,YAAY,KAAK,QAAQ,IAChC,OAAOA,YAAY,CAACyM,gBAAgB,KAAK,UAAU,EACnD;gBACA,IAAIjE,YAAY,EAAE,CAEjB;qBAAM;oBACL,OAAQxI,YAAY,CAACyM,gBAAgB,CAACX,KAAK,EAAEC,MAAM,CAAC;wBAClD,KAAK,aAAa;4BAAE;gCAClBvO,KAAK,GAAG8F,kBAAkB,CAAC9C,UAAU,CAAChD,KAAK,CAAC,CAAA;gCAC5C,MAAA;4BACF,CAAA;wBAEA,KAAK,kBAAkB;4BAAE;gCACvBA,KAAK,GAAG8F,kBAAkB,CAAC7C,eAAe,CAACjD,KAAK,CAAC,CAAA;gCACjD,MAAA;4BACF,CAAA;oBAKF,CAAA;gBACF,CAAA;YACF,CAAA;YAEA,0DAAA,GACA,IAAIA,KAAK,KAAKgP,SAAS,EAAE;gBACvB,IAAI;oBACF,IAAIhE,YAAY,EAAE;wBAChBwC,WAAW,CAAC0B,cAAc,CAAClE,YAAY,EAAEO,IAAI,EAAEvL,KAAK,CAAC,CAAA;oBACvD,CAAC,MAAM;wBACL,mFAAA,GACAwN,WAAW,CAAC7B,YAAY,CAACJ,IAAI,EAAEvL,KAAK,CAAC,CAAA;oBACvC,CAAA;oBAEA,IAAIiN,YAAY,CAACO,WAAW,CAAC,EAAE;wBAC7BrC,YAAY,CAACqC,WAAW,CAAC,CAAA;oBAC3B,CAAC,MAAM;wBACL1Q,QAAQ,CAACoH,SAAS,CAACI,OAAO,CAAC,CAAA;oBAC7B,CAAA;iBACD,CAAC,OAAOnB,CAAC,EAAE;oBACVmI,gBAAgB,CAACC,IAAI,EAAEiC,WAAW,CAAC,CAAA;gBACrC,CAAA;YACF,CAAA;QACF,CAAA;QAEA,6BAAA,GACAD,aAAa,CAAClH,KAAK,CAAC9C,uBAAuB,EAAEiK,WAAW,EAAE,IAAI,CAAC,CAAA;KAChE,CAAA;IAED;;;;GAIG,GACH,MAAM2B,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAaC,QAA0B,EAAA;QAC7D,IAAIC,UAAU,GAAG,IAAI,CAAA;QACrB,MAAMC,cAAc,GAAG3C,mBAAmB,CAACyC,QAAQ,CAAC,CAAA;QAEpD,6BAAA,GACA7B,aAAa,CAAClH,KAAK,CAACzC,uBAAuB,EAAEwL,QAAQ,EAAE,IAAI,CAAC,CAAA;QAE5D,MAAQC,UAAU,GAAGC,cAAc,CAACC,QAAQ,EAAE,CAAG;YAC/C,6BAAA,GACAhC,aAAa,CAAClH,KAAK,CAACtC,sBAAsB,EAAEsL,UAAU,EAAE,IAAI,CAAC,CAAA;YAE7D,8BAAA,GACA1B,iBAAiB,CAAC0B,UAAU,CAAC,CAAA;YAE7B,yBAAA,GACAb,mBAAmB,CAACa,UAAU,CAAC,CAAA;YAE/B,4BAAA,GACA,IAAIA,UAAU,CAACzJ,OAAO,YAAYhB,gBAAgB,EAAE;gBAClDuK,kBAAkB,CAACE,UAAU,CAACzJ,OAAO,CAAC,CAAA;YACxC,CAAA;QACF,CAAA;QAEA,6BAAA,GACA2H,aAAa,CAAClH,KAAK,CAAC5C,sBAAsB,EAAE2L,QAAQ,EAAE,IAAI,CAAC,CAAA;KAC5D,CAAA;IAED,sCAAA;IACAlL,SAAS,CAACsL,QAAQ,GAAG,SAAU3D,KAAK,EAAU;QAAA,IAAR3B,GAAG,GAAArL,SAAA,CAAAC,MAAA,GAAA,CAAA,IAAAD,SAAA,CAAA,CAAA,CAAA,KAAAoF,SAAA,GAAApF,SAAA,CAAA,CAAA,CAAA,GAAG,CAAA,CAAE,CAAA;QAC5C,IAAIyN,IAAI,GAAG,IAAI,CAAA;QACf,IAAImD,YAAY,GAAG,IAAI,CAAA;QACvB,IAAIjC,WAAW,GAAG,IAAI,CAAA;QACtB,IAAIkC,UAAU,GAAG,IAAI,CAAA;QACrB;;+DAE6D,GAC7DvG,cAAc,GAAG,CAAC0C,KAAK,CAAA;QACvB,IAAI1C,cAAc,EAAE;YAClB0C,KAAK,GAAG,OAAO,CAAA;QACjB,CAAA;QAEA,yCAAA,GACA,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,CAACyB,OAAO,CAACzB,KAAK,CAAC,EAAE;YAChD,IAAI,OAAOA,KAAK,CAACrO,QAAQ,KAAK,UAAU,EAAE;gBACxCqO,KAAK,GAAGA,KAAK,CAACrO,QAAQ,EAAE,CAAA;gBACxB,IAAI,OAAOqO,KAAK,KAAK,QAAQ,EAAE;oBAC7B,MAAMvN,eAAe,CAAC,iCAAiC,CAAC,CAAA;gBAC1D,CAAA;YACF,CAAC,MAAM;gBACL,MAAMA,eAAe,CAAC,4BAA4B,CAAC,CAAA;YACrD,CAAA;QACF,CAAA;QAEA,6CAAA,GACA,IAAI,CAAC4F,SAAS,CAACO,WAAW,EAAE;YAC1B,OAAOoH,KAAK,CAAA;QACd,CAAA;QAEA,sBAAA,GACA,IAAI,CAAC/D,UAAU,EAAE;YACfmC,YAAY,CAACC,GAAG,CAAC,CAAA;QACnB,CAAA;QAEA,6BAAA,GACAhG,SAAS,CAACI,OAAO,GAAG,EAAE,CAAA;QAEtB,kDAAA,GACA,IAAI,OAAOuH,KAAK,KAAK,QAAQ,EAAE;YAC7BtD,QAAQ,GAAG,KAAK,CAAA;QAClB,CAAA;QAEA,IAAIA,QAAQ,EAAE;YACZ,6DAAA,GACA,IAAKsD,KAAc,CAACqB,QAAQ,EAAE;gBAC5B,MAAMnC,OAAO,GAAG1L,iBAAiB,CAAEwM,KAAc,CAACqB,QAAQ,CAAC,CAAA;gBAC3D,IAAI,CAAC1G,YAAY,CAACuE,OAAO,CAAC,IAAI1D,WAAW,CAAC0D,OAAO,CAAC,EAAE;oBAClD,MAAMzM,eAAe,CACnB,yDAAyD,CAC1D,CAAA;gBACH,CAAA;YACF,CAAA;QACF,CAAC,MAAM,IAAIuN,KAAK,YAAY/G,IAAI,EAAE;YAChC;+CAC2C,GAC3CwH,IAAI,GAAGV,aAAa,CAAC,SAAS,CAAC,CAAA;YAC/B6D,YAAY,GAAGnD,IAAI,CAACzG,aAAa,CAACO,UAAU,CAACyF,KAAK,EAAE,IAAI,CAAC,CAAA;YACzD,IACE4D,YAAY,CAAClL,QAAQ,KAAK7C,SAAS,CAACnC,OAAO,IAC3CkQ,YAAY,CAACvC,QAAQ,KAAK,MAAM,EAChC;gBACA,qCAAA,GACAZ,IAAI,GAAGmD,YAAY,CAAA;YACrB,CAAC,MAAM,IAAIA,YAAY,CAACvC,QAAQ,KAAK,MAAM,EAAE;gBAC3CZ,IAAI,GAAGmD,YAAY,CAAA;YACrB,CAAC,MAAM;gBACL,0DAAA;gBACAnD,IAAI,CAACqD,WAAW,CAACF,YAAY,CAAC,CAAA;YAChC,CAAA;QACF,CAAC,MAAM;YACL,0CAAA,GACA,IACE,CAACzH,UAAU,IACX,CAACL,kBAAkB,IACnB,CAACE,cAAc,IACf,mDAAA;YACAgE,KAAK,CAAC/N,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EACzB;gBACA,OAAOgI,kBAAkB,IAAIoC,mBAAmB,GAC5CpC,kBAAkB,CAAC9C,UAAU,CAAC6I,KAAK,CAAC,GACpCA,KAAK,CAAA;YACX,CAAA;YAEA,sCAAA,GACAS,IAAI,GAAGV,aAAa,CAACC,KAAK,CAAC,CAAA;YAE3B,0CAAA,GACA,IAAI,CAACS,IAAI,EAAE;gBACT,OAAOtE,UAAU,GAAG,IAAI,GAAGE,mBAAmB,GAAGnC,SAAS,GAAG,EAAE,CAAA;YACjE,CAAA;QACF,CAAA;QAEA,yDAAA,GACA,IAAIuG,IAAI,IAAIvE,UAAU,EAAE;YACtBoD,YAAY,CAACmB,IAAI,CAACsD,UAAU,CAAC,CAAA;QAC/B,CAAA;QAEA,qBAAA,GACA,MAAMC,YAAY,GAAGlD,mBAAmB,CAACpE,QAAQ,GAAGsD,KAAK,GAAGS,IAAI,CAAC,CAAA;QAEjE,iDAAA,GACA,MAAQkB,WAAW,GAAGqC,YAAY,CAACN,QAAQ,EAAE,CAAG;YAC9C,8BAAA,GACA5B,iBAAiB,CAACH,WAAW,CAAC,CAAA;YAE9B,yBAAA,GACAgB,mBAAmB,CAAChB,WAAW,CAAC,CAAA;YAEhC,oCAAA,GACA,IAAIA,WAAW,CAAC5H,OAAO,YAAYhB,gBAAgB,EAAE;gBACnDuK,kBAAkB,CAAC3B,WAAW,CAAC5H,OAAO,CAAC,CAAA;YACzC,CAAA;QACF,CAAA;QAEA,gDAAA,GACA,IAAI2C,QAAQ,EAAE;YACZ,OAAOsD,KAAK,CAAA;QACd,CAAA;QAEA,kCAAA,GACA,IAAI7D,UAAU,EAAE;YACd,IAAIC,mBAAmB,EAAE;gBACvByH,UAAU,GAAGxJ,sBAAsB,CAACwG,IAAI,CAACJ,IAAI,CAACzG,aAAa,CAAC,CAAA;gBAE5D,MAAOyG,IAAI,CAACsD,UAAU,CAAE;oBACtB,0DAAA;oBACAF,UAAU,CAACC,WAAW,CAACrD,IAAI,CAACsD,UAAU,CAAC,CAAA;gBACzC,CAAA;YACF,CAAC,MAAM;gBACLF,UAAU,GAAGpD,IAAI,CAAA;YACnB,CAAA;YAEA,IAAI3F,YAAY,CAACmJ,UAAU,IAAInJ,YAAY,CAACoJ,cAAc,EAAE;gBAC1D;;;;;;QAME,GACFL,UAAU,GAAGtJ,UAAU,CAACsG,IAAI,CAAChI,gBAAgB,EAAEgL,UAAU,EAAE,IAAI,CAAC,CAAA;YAClE,CAAA;YAEA,OAAOA,UAAU,CAAA;QACnB,CAAA;QAEA,IAAIM,cAAc,GAAGnI,cAAc,GAAGyE,IAAI,CAAC2D,SAAS,GAAG3D,IAAI,CAACD,SAAS,CAAA;QAErE,gCAAA,GACA,IACExE,cAAc,IACdrB,YAAY,CAAC,UAAU,CAAC,IACxB8F,IAAI,CAACzG,aAAa,IAClByG,IAAI,CAACzG,aAAa,CAACqK,OAAO,IAC1B5D,IAAI,CAACzG,aAAa,CAACqK,OAAO,CAAC3E,IAAI,IAC/BpN,UAAU,CAACoI,YAAwB,EAAE+F,IAAI,CAACzG,aAAa,CAACqK,OAAO,CAAC3E,IAAI,CAAC,EACrE;YACAyE,cAAc,GACZ,YAAY,GAAG1D,IAAI,CAACzG,aAAa,CAACqK,OAAO,CAAC3E,IAAI,GAAG,KAAK,GAAGyE,cAAc,CAAA;QAC3E,CAAA;QAEA,uCAAA,GACA,IAAIrI,kBAAkB,EAAE;YACtBpL,YAAY,CAAC;gBAACyE,aAAa;gBAAEC,QAAQ;gBAAEC,WAAW;aAAC,GAAGkN,IAAI,IAAI;gBAC5D4B,cAAc,GAAGrS,aAAa,CAACqS,cAAc,EAAE5B,IAAI,EAAE,GAAG,CAAC,CAAA;YAC3D,CAAC,CAAC,CAAA;QACJ,CAAA;QAEA,OAAOtI,kBAAkB,IAAIoC,mBAAmB,GAC5CpC,kBAAkB,CAAC9C,UAAU,CAACgN,cAAc,CAAC,GAC7CA,cAAc,CAAA;KACnB,CAAA;IAED9L,SAAS,CAACiM,SAAS,GAAG,YAAkB;QAAA,IAARjG,GAAG,GAAArL,SAAA,CAAAC,MAAA,GAAA,CAAA,IAAAD,SAAA,CAAA,CAAA,CAAA,KAAAoF,SAAA,GAAApF,SAAA,CAAA,CAAA,CAAA,GAAG,CAAA,CAAE,CAAA;QACtCoL,YAAY,CAACC,GAAG,CAAC,CAAA;QACjBpC,UAAU,GAAG,IAAI,CAAA;KAClB,CAAA;IAED5D,SAAS,CAACkM,WAAW,GAAG,YAAA;QACtBxG,MAAM,GAAG,IAAI,CAAA;QACb9B,UAAU,GAAG,KAAK,CAAA;KACnB,CAAA;IAED5D,SAAS,CAACmM,gBAAgB,GAAG,SAAUC,GAAG,EAAEvB,IAAI,EAAE/O,KAAK,EAAA;QACrD,+CAAA,GACA,IAAI,CAAC4J,MAAM,EAAE;YACXK,YAAY,CAAC,CAAA,CAAE,CAAC,CAAA;QAClB,CAAA;QAEA,MAAMqE,KAAK,GAAGjP,iBAAiB,CAACiR,GAAG,CAAC,CAAA;QACpC,MAAM/B,MAAM,GAAGlP,iBAAiB,CAAC0P,IAAI,CAAC,CAAA;QACtC,OAAOV,iBAAiB,CAACC,KAAK,EAAEC,MAAM,EAAEvO,KAAK,CAAC,CAAA;KAC/C,CAAA;IAEDkE,SAAS,CAACqM,OAAO,GAAG,SAAUC,UAAU,EAAEC,YAAY,EAAA;QACpD,IAAI,OAAOA,YAAY,KAAK,UAAU,EAAE;YACtC,OAAA;QACF,CAAA;QAEAzT,SAAS,CAACqJ,KAAK,CAACmK,UAAU,CAAC,EAAEC,YAAY,CAAC,CAAA;KAC3C,CAAA;IAEDvM,SAAS,CAACwM,UAAU,GAAG,SAAUF,UAAU,EAAEC,YAAY,EAAA;QACvD,IAAIA,YAAY,KAAKxM,SAAS,EAAE;YAC9B,MAAMvE,KAAK,GAAG9C,gBAAgB,CAACyJ,KAAK,CAACmK,UAAU,CAAC,EAAEC,YAAY,CAAC,CAAA;YAE/D,OAAO/Q,KAAK,KAAK,CAAC,CAAC,GACfuE,SAAS,GACT/G,WAAW,CAACmJ,KAAK,CAACmK,UAAU,CAAC,EAAE9Q,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QACjD,CAAA;QAEA,OAAO5C,QAAQ,CAACuJ,KAAK,CAACmK,UAAU,CAAC,CAAC,CAAA;KACnC,CAAA;IAEDtM,SAAS,CAACyM,WAAW,GAAG,SAAUH,UAAU,EAAA;QAC1CnK,KAAK,CAACmK,UAAU,CAAC,GAAG,EAAE,CAAA;KACvB,CAAA;IAEDtM,SAAS,CAAC0M,cAAc,GAAG,YAAA;QACzBvK,KAAK,GAAG/C,eAAe,EAAE,CAAA;KAC1B,CAAA;IAED,OAAOY,SAAS,CAAA;AAClB,CAAA;AAEA,IAAA,SAAeF,eAAe,EAAE", "debugId": null}}]}