@import "tailwindcss";
:root {
    --background: #ffffff;
    --foreground: #171717;
}


/* Custom Animations */

@keyframes blob {
    0% {
        transform: translate(0px, 0px) scale(1);
    }
    33% {
        transform: translate(30px, -50px) scale(1.1);
    }
    66% {
        transform: translate(-20px, 20px) scale(0.9);
    }
    100% {
        transform: translate(0px, 0px) scale(1);
    }
}

.animate-blob {
    animation: blob 7s infinite;
}

.animation-delay-2000 {
    animation-delay: 2s;
}

.animation-delay-4000 {
    animation-delay: 4s;
}


/* Background Grid Pattern */

.bg-grid-pattern {
    background-image: linear-gradient(rgba(0, 0, 0, 0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(0, 0, 0, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
}


/* Glassmorphism Effect */

.glass {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
}


/* Custom Scrollbar */

::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}


/* Hover Effects */

.hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}


/* Enhanced Text Colors */

.text-dark {
    color: #1a1a1a !important;
}

.text-darker {
    color: #0f0f0f !important;
}


/* Form Input Enhancements */

input[type="text"],
input[type="email"],
input[type="password"],
input[type="tel"],
input[type="number"],
textarea,
select {
    color: #1a1a1a !important;
    font-weight: 500 !important;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
input[type="tel"]:focus,
input[type="number"]:focus,
textarea:focus,
select:focus {
    color: #0f0f0f !important;
    font-weight: 600 !important;
}


/* Placeholder Text */

input::placeholder,
textarea::placeholder {
    color: #6b7280 !important;
    font-weight: 400 !important;
}


/* Label Text */

label {
    color: #1f2937 !important;
    font-weight: 600 !important;
}


/* Table Text */

table th {
    color: #1f2937 !important;
    font-weight: 700 !important;
}

table td {
    color: #1a1a1a !important;
    font-weight: 500 !important;
}


/* Button Text */

button {
    font-weight: 600 !important;
}


/* Mobile Responsive Enhancements */

@media (max-width: 768px) {
    /* Mobile Typography */
    h1 {
        font-size: 1.875rem !important;
        line-height: 2.25rem !important;
    }
    h2 {
        font-size: 1.5rem !important;
        line-height: 2rem !important;
    }
    h3 {
        font-size: 1.25rem !important;
        line-height: 1.75rem !important;
    }
    /* Mobile Form Inputs */
    input[type="text"],
    input[type="email"],
    input[type="password"],
    input[type="tel"],
    input[type="number"],
    textarea,
    select {
        font-size: 16px !important;
        /* Prevents zoom on iOS */
        padding: 12px !important;
        min-height: 44px !important;
        /* Touch target size */
    }
    /* Mobile Buttons */
    button {
        min-height: 44px !important;
        padding: 12px 16px !important;
        font-size: 16px !important;
    }
    /* Mobile Tables */
    table {
        font-size: 14px !important;
    }
    /* Mobile Cards */
    .card {
        padding: 16px !important;
        margin: 8px !important;
    }
    /* Mobile Modals */
    .modal {
        margin: 16px !important;
        max-height: calc(100vh - 32px) !important;
    }
}

@media (max-width: 640px) {
    /* Small Mobile Adjustments */
    .container {
        padding-left: 16px !important;
        padding-right: 16px !important;
    }
    /* Smaller text for very small screens */
    table {
        font-size: 12px !important;
    }
    /* Stack elements vertically */
    .flex-mobile-stack {
        flex-direction: column !important;
        gap: 8px !important;
    }
}


/* Touch-friendly enhancements */

@media (hover: none) and (pointer: coarse) {
    /* Touch device specific styles */
    button:hover {
        transform: none !important;
    }
    .hover-lift:hover {
        transform: none !important;
    }
    /* Larger touch targets */
    a,
    button,
    input,
    select,
    textarea {
        min-height: 44px !important;
    }
}

@theme inline {
    --color-background: var(--background);
    --color-foreground: var(--foreground);
    --font-sans: var(--font-geist-sans);
    --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
     :root {
        --background: #0a0a0a;
        --foreground: #ededed;
    }
}

body {
    background: var(--background);
    color: var(--foreground);
    font-family: Arial, Helvetica, sans-serif;
    overflow-x: hidden;
    /* منع التمرير الأفقي */
}


/* Mobile-specific improvements */

@media (max-width: 768px) {
    /* Better mobile table handling */
    .table-responsive {
        overflow-x: auto !important;
        -webkit-overflow-scrolling: touch !important;
    }
    /* Mobile-friendly cards */
    .mobile-card {
        margin: 8px !important;
        padding: 16px !important;
        border-radius: 12px !important;
    }
    /* Mobile content spacing */
    .mobile-content {
        padding: 12px !important;
    }
    /* Stack buttons vertically on mobile */
    .button-group-mobile {
        flex-direction: column !important;
        gap: 8px !important;
    }
    /* Mobile modal adjustments */
    .mobile-modal {
        margin: 8px !important;
        max-height: calc(100vh - 16px) !important;
    }
}


/* Very small screens */

@media (max-width: 480px) {
    .mobile-content {
        padding: 8px !important;
    }
    /* Smaller text for very small screens */
    h1 {
        font-size: 1.5rem !important;
    }
    h2 {
        font-size: 1.25rem !important;
    }
}