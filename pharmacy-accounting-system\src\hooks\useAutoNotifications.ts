'use client'

import { useEffect } from 'react'
import { useNotifications } from '@/contexts/NotificationContext'

// Hook لإنشاء التنبيهات التلقائية بناءً على أحداث النظام
export const useAutoNotifications = () => {
  const { addNotification } = useNotifications()

  // دالة لإنشاء تنبيه عند نفاد المخزون
  const notifyOutOfStock = (medicineName: string, batchCode?: string) => {
    addNotification({
      type: 'error',
      title: 'نفاد مخزون',
      message: `${medicineName}${batchCode ? ` - دفعة ${batchCode}` : ''} - الكمية المتبقية: 0`,
      category: 'inventory',
      priority: 'critical',
      actionUrl: '/inventory',
      actionLabel: 'إضافة مخزون'
    })
  }

  // دالة لإنشاء تنبيه عند انخفاض المخزون
  const notifyLowStock = (medicineName: string, currentQuantity: number, minQuantity: number) => {
    addNotification({
      type: 'warning',
      title: 'مخزون منخفض',
      message: `${medicineName} - الكمية المتبقية: ${currentQuantity} (الحد الأدنى: ${minQuantity})`,
      category: 'inventory',
      priority: 'high',
      actionUrl: '/inventory',
      actionLabel: 'عرض التفاصيل'
    })
  }

  // دالة لإنشاء تنبيه عند اقتراب انتهاء الصلاحية
  const notifyExpiringMedicine = (medicineName: string, expiryDate: string, daysLeft: number) => {
    addNotification({
      type: 'warning',
      title: 'دواء قارب على الانتهاء',
      message: `${medicineName} - ينتهي خلال ${daysLeft} يوم (${new Date(expiryDate).toLocaleDateString('ar-EG')})`,
      category: 'inventory',
      priority: daysLeft <= 7 ? 'critical' : 'high',
      actionUrl: '/inventory?filter=expiring',
      actionLabel: 'عرض الأدوية'
    })
  }

  // دالة لإنشاء تنبيه عند إنشاء مبيعات جديدة
  const notifyNewSale = (invoiceNumber: string, amount: number, customerName?: string) => {
    addNotification({
      type: 'success',
      title: 'مبيعات جديدة',
      message: `فاتورة ${invoiceNumber}${customerName ? ` - ${customerName}` : ''} - ${amount.toLocaleString()} د.ع`,
      category: 'sales',
      priority: 'low',
      actionUrl: '/sales-records',
      actionLabel: 'عرض الفاتورة'
    })
  }

  // دالة لإنشاء تنبيه عند إنشاء مشتريات جديدة
  const notifyNewPurchase = (invoiceNumber: string, amount: number, supplierName?: string) => {
    addNotification({
      type: 'info',
      title: 'مشتريات جديدة',
      message: `فاتورة ${invoiceNumber}${supplierName ? ` - ${supplierName}` : ''} - ${amount.toLocaleString()} د.ع`,
      category: 'sales',
      priority: 'low',
      actionUrl: '/purchases-records',
      actionLabel: 'عرض الفاتورة'
    })
  }

  // دالة لإنشاء تنبيه عند إضافة مستخدم جديد
  const notifyNewUser = (username: string, role: string) => {
    addNotification({
      type: 'info',
      title: 'مستخدم جديد',
      message: `تم إضافة مستخدم جديد: ${username} (${role})`,
      category: 'user',
      priority: 'low',
      actionUrl: '/users',
      actionLabel: 'إدارة المستخدمين'
    })
  }

  // دالة لإنشاء تنبيه عند تحديث النظام
  const notifySystemUpdate = (version: string, features?: string[]) => {
    addNotification({
      type: 'success',
      title: 'تحديث النظام',
      message: `تم تحديث النظام بنجاح إلى الإصدار ${version}`,
      category: 'system',
      priority: 'low',
      data: { features }
    })
  }

  // دالة لإنشاء تنبيه عند وجود فواتير معلقة
  const notifyPendingInvoices = (count: number, totalAmount: number) => {
    addNotification({
      type: 'warning',
      title: 'فواتير معلقة',
      message: `يوجد ${count} فاتورة معلقة الدفع بقيمة ${totalAmount.toLocaleString()} د.ع`,
      category: 'financial',
      priority: 'high',
      actionUrl: '/sales-records?filter=pending',
      actionLabel: 'عرض الفواتير'
    })
  }

  // دالة لإنشاء تنبيه عند تحقيق هدف مبيعات
  const notifySalesTarget = (dailyAmount: number, target: number) => {
    const percentage = Math.round((dailyAmount / target) * 100)
    
    addNotification({
      type: percentage >= 100 ? 'success' : 'info',
      title: percentage >= 100 ? 'تم تحقيق الهدف!' : 'تقدم المبيعات',
      message: `مبيعات اليوم: ${dailyAmount.toLocaleString()} د.ع (${percentage}% من الهدف)`,
      category: 'sales',
      priority: 'low',
      actionUrl: '/reports',
      actionLabel: 'عرض التقارير'
    })
  }

  // دالة لإنشاء تنبيه عند حدوث خطأ في النظام
  const notifySystemError = (errorMessage: string, component?: string) => {
    addNotification({
      type: 'error',
      title: 'خطأ في النظام',
      message: `${component ? `${component}: ` : ''}${errorMessage}`,
      category: 'system',
      priority: 'high'
    })
  }

  // دالة لإنشاء تنبيه عند نجاح عملية النسخ الاحتياطي
  const notifyBackupSuccess = () => {
    addNotification({
      type: 'success',
      title: 'نسخ احتياطي ناجح',
      message: 'تم إنشاء النسخة الاحتياطية بنجاح',
      category: 'system',
      priority: 'low'
    })
  }

  // دالة لإنشاء تنبيه عند فشل عملية النسخ الاحتياطي
  const notifyBackupFailure = (reason?: string) => {
    addNotification({
      type: 'error',
      title: 'فشل النسخ الاحتياطي',
      message: `فشل في إنشاء النسخة الاحتياطية${reason ? `: ${reason}` : ''}`,
      category: 'system',
      priority: 'critical'
    })
  }

  // دالة لإنشاء تنبيه عند تسجيل دخول مشبوه
  const notifySuspiciousLogin = (username: string, ipAddress: string) => {
    addNotification({
      type: 'warning',
      title: 'تسجيل دخول مشبوه',
      message: `محاولة دخول من عنوان IP غير معتاد: ${username} من ${ipAddress}`,
      category: 'user',
      priority: 'high',
      actionUrl: '/activity-log',
      actionLabel: 'عرض السجل'
    })
  }

  return {
    notifyOutOfStock,
    notifyLowStock,
    notifyExpiringMedicine,
    notifyNewSale,
    notifyNewPurchase,
    notifyNewUser,
    notifySystemUpdate,
    notifyPendingInvoices,
    notifySalesTarget,
    notifySystemError,
    notifyBackupSuccess,
    notifyBackupFailure,
    notifySuspiciousLogin
  }
}

// Hook لمراقبة المخزون وإنشاء التنبيهات التلقائية
export const useInventoryNotifications = () => {
  const { notifyOutOfStock, notifyLowStock, notifyExpiringMedicine } = useAutoNotifications()

  const checkInventoryLevels = (inventory: any[]) => {
    inventory.forEach(item => {
      // فحص نفاد المخزون
      if (item.quantity <= 0) {
        notifyOutOfStock(item.medicine_name, item.batch_code)
      }
      // فحص انخفاض المخزون
      else if (item.quantity <= (item.min_quantity || 10)) {
        notifyLowStock(item.medicine_name, item.quantity, item.min_quantity || 10)
      }

      // فحص اقتراب انتهاء الصلاحية
      if (item.expiry_date) {
        const expiryDate = new Date(item.expiry_date)
        const today = new Date()
        const daysLeft = Math.ceil((expiryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24))
        
        if (daysLeft <= 30 && daysLeft > 0) {
          notifyExpiringMedicine(item.medicine_name, item.expiry_date, daysLeft)
        }
      }
    })
  }

  return { checkInventoryLevels }
}

// Hook لمراقبة المبيعات وإنشاء التنبيهات
export const useSalesNotifications = () => {
  const { notifyNewSale, notifyPendingInvoices, notifySalesTarget } = useAutoNotifications()

  const checkDailySales = (sales: any[], target: number = 1000000) => {
    const today = new Date().toDateString()
    const todaySales = sales.filter(sale => 
      new Date(sale.created_at).toDateString() === today
    )
    
    const dailyTotal = todaySales.reduce((sum, sale) => sum + (sale.final_amount || 0), 0)
    
    if (dailyTotal > 0) {
      notifySalesTarget(dailyTotal, target)
    }

    // فحص الفواتير المعلقة
    const pendingInvoices = sales.filter(sale => sale.payment_status === 'pending')
    if (pendingInvoices.length > 0) {
      const pendingAmount = pendingInvoices.reduce((sum, sale) => sum + (sale.final_amount || 0), 0)
      notifyPendingInvoices(pendingInvoices.length, pendingAmount)
    }
  }

  return { checkDailySales }
}
