import { Pill, Loader2 } from 'lucide-react'

export default function Loading() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center">
      <div className="text-center">
        {/* Logo Animation */}
        <div className="mb-8">
          <div className="relative">
            <div className="w-20 h-20 bg-gradient-to-br from-blue-600 to-indigo-700 rounded-2xl flex items-center justify-center mx-auto mb-4 animate-pulse">
              <Pill className="h-10 w-10 text-white" />
            </div>
            <div className="absolute -inset-2 bg-gradient-to-r from-blue-600 to-indigo-700 rounded-2xl opacity-20 animate-ping"></div>
          </div>
        </div>

        {/* Loading Content */}
        <div className="bg-white/80 backdrop-blur-lg rounded-2xl shadow-2xl border border-white/20 p-8 max-w-sm mx-auto">
          <div className="flex items-center justify-center gap-3 mb-4">
            <Loader2 className="h-6 w-6 text-blue-600 animate-spin" />
            <h2 className="text-xl font-semibold text-gray-900">
              جاري التحميل...
            </h2>
          </div>
          
          <p className="text-gray-600 mb-6">
            نظام إدارة الصيدلية - مكتب لارين العلمي
          </p>

          {/* Loading Bar */}
          <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
            <div className="bg-gradient-to-r from-blue-600 to-indigo-700 h-2 rounded-full animate-pulse" style={{width: '60%'}}></div>
          </div>

          <div className="text-sm text-gray-500">
            يرجى الانتظار...
          </div>
        </div>

        {/* Loading Dots */}
        <div className="flex justify-center gap-2 mt-8">
          <div className="w-3 h-3 bg-blue-600 rounded-full animate-bounce"></div>
          <div className="w-3 h-3 bg-indigo-600 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
          <div className="w-3 h-3 bg-purple-600 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
        </div>
      </div>
    </div>
  )
}
