// Utility functions for consistent formatting across server and client

/**
 * Format number with Arabic locale consistently
 * This prevents hydration errors by ensuring same output on server and client
 */
export function formatNumber(num: number): string {
  if (typeof num !== 'number' || isNaN(num)) {
    return '0'
  }
  
  // Use a consistent format that works on both server and client
  return new Intl.NumberFormat('en-US').format(num)
}

/**
 * Format currency with Iraqi Dinar
 */
export function formatCurrency(amount: number): string {
  return `${formatNumber(amount)} د.ع`
}

/**
 * Format date consistently
 */
export function formatDate(date: string | Date): string {
  if (!date) return ''
  
  const dateObj = typeof date === 'string' ? new Date(date) : date
  
  if (isNaN(dateObj.getTime())) {
    return ''
  }
  
  // Use consistent date format
  return dateObj.toLocaleDateString('en-GB') // DD/MM/YYYY format
}

/**
 * Format percentage
 */
export function formatPercentage(value: number): string {
  if (typeof value !== 'number' || isNaN(value)) {
    return '0%'
  }
  
  return `${formatNumber(value)}%`
}

/**
 * Format phone number
 */
export function formatPhone(phone: string): string {
  if (!phone) return ''
  
  // Remove any non-digit characters
  const cleaned = phone.replace(/\D/g, '')
  
  // Format Iraqi phone numbers
  if (cleaned.startsWith('964')) {
    // International format
    return `+${cleaned.slice(0, 3)} ${cleaned.slice(3, 6)} ${cleaned.slice(6, 9)} ${cleaned.slice(9)}`
  } else if (cleaned.startsWith('07')) {
    // Local format
    return `${cleaned.slice(0, 4)} ${cleaned.slice(4, 7)} ${cleaned.slice(7)}`
  }
  
  return phone
}

/**
 * Truncate text with ellipsis
 */
export function truncateText(text: string, maxLength: number): string {
  if (!text || text.length <= maxLength) {
    return text
  }
  
  return text.slice(0, maxLength) + '...'
}

/**
 * Format file size
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return `${formatNumber(parseFloat((bytes / Math.pow(k, i)).toFixed(2)))} ${sizes[i]}`
}

/**
 * Format time duration
 */
export function formatDuration(seconds: number): string {
  if (seconds < 60) {
    return `${Math.round(seconds)} ثانية`
  } else if (seconds < 3600) {
    return `${Math.round(seconds / 60)} دقيقة`
  } else {
    return `${Math.round(seconds / 3600)} ساعة`
  }
}

/**
 * Format status text with proper Arabic
 */
export function formatStatus(status: string): string {
  const statusMap: Record<string, string> = {
    'active': 'نشط',
    'inactive': 'غير نشط',
    'pending': 'معلق',
    'approved': 'مقبول',
    'rejected': 'مرفوض',
    'paid': 'مدفوع',
    'unpaid': 'غير مدفوع',
    'partial': 'جزئي',
    'completed': 'مكتمل',
    'cancelled': 'ملغي',
    'draft': 'مسودة',
    'published': 'منشور'
  }
  
  return statusMap[status] || status
}

/**
 * Format payment method
 */
export function formatPaymentMethod(method: string): string {
  const methodMap: Record<string, string> = {
    'cash': 'نقداً',
    'credit': 'آجل',
    'card': 'بطاقة',
    'bank': 'تحويل بنكي',
    'check': 'شيك'
  }
  
  return methodMap[method] || method
}

/**
 * Safe number parsing
 */
export function parseNumber(value: string | number): number {
  if (typeof value === 'number') {
    return isNaN(value) ? 0 : value
  }
  
  if (typeof value === 'string') {
    const parsed = parseFloat(value.replace(/[^\d.-]/g, ''))
    return isNaN(parsed) ? 0 : parsed
  }
  
  return 0
}

/**
 * Format quantity with unit
 */
export function formatQuantity(quantity: number, unit?: string): string {
  const formattedQty = formatNumber(quantity)
  return unit ? `${formattedQty} ${unit}` : formattedQty
}

/**
 * Format medicine batch code
 */
export function formatBatchCode(code: string): string {
  if (!code) return 'غير محدد'
  return code.toUpperCase()
}

/**
 * Format invoice number
 */
export function formatInvoiceNumber(number: string | number): string {
  if (!number) return 'غير محدد'
  return `#${number}`
}

/**
 * Calculate and format discount percentage
 */
export function formatDiscountPercentage(originalAmount: number, discountAmount: number): string {
  if (originalAmount <= 0) return '0%'
  
  const percentage = (discountAmount / originalAmount) * 100
  return formatPercentage(Math.round(percentage * 100) / 100)
}

/**
 * Format expiry status
 */
export function formatExpiryStatus(expiryDate: string): { status: string, color: string, text: string } {
  if (!expiryDate) {
    return { status: 'unknown', color: 'gray', text: 'غير محدد' }
  }
  
  const today = new Date()
  const expiry = new Date(expiryDate)
  const diffTime = expiry.getTime() - today.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  if (diffDays < 0) {
    return { status: 'expired', color: 'red', text: 'منتهي الصلاحية' }
  } else if (diffDays <= 30) {
    return { status: 'expiring', color: 'orange', text: 'ينتهي قريباً' }
  } else if (diffDays <= 90) {
    return { status: 'warning', color: 'yellow', text: 'تحذير' }
  } else {
    return { status: 'good', color: 'green', text: 'طبيعي' }
  }
}

/**
 * Format stock status
 */
export function formatStockStatus(quantity: number, minStock: number = 10): { status: string, color: string, text: string } {
  if (quantity <= 0) {
    return { status: 'out', color: 'red', text: 'نفد المخزون' }
  } else if (quantity <= minStock) {
    return { status: 'low', color: 'orange', text: 'كمية قليلة' }
  } else if (quantity <= minStock * 2) {
    return { status: 'medium', color: 'yellow', text: 'كمية متوسطة' }
  } else {
    return { status: 'good', color: 'green', text: 'كمية جيدة' }
  }
}
