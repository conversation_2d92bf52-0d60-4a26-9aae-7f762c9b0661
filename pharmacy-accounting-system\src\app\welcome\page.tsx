'use client'

import Link from 'next/link'
import { 
  Shield, 
  Users, 
  ShoppingCart, 
  Package, 
  BarChart3,
  CheckCircle,
  ArrowRight,
  Pill
} from 'lucide-react'

export default function WelcomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-12">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-6">
            <div className="bg-blue-600 p-4 rounded-full">
              <Pill className="h-12 w-12 text-white" />
            </div>
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            🎉 مرحباً بك في نظام إدارة الصيدلية
          </h1>
          <p className="text-xl text-gray-600 mb-2">
            مكتب لارين العلمي
          </p>
          <p className="text-lg text-gray-500">
            نظام شامل لإدارة الصيدليات مع نظام مصادقة متقدم
          </p>
        </div>

        {/* Success Message */}
        <div className="max-w-4xl mx-auto mb-12">
          <div className="bg-green-50 border border-green-200 rounded-xl p-8 text-center">
            <CheckCircle className="h-16 w-16 text-green-600 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-green-800 mb-2">
              ✅ تم تشغيل النظام بنجاح!
            </h2>
            <p className="text-green-600">
              جميع المكونات تعمل بشكل صحيح ونظام المصادقة جاهز للاستخدام
            </p>
          </div>
        </div>

        {/* Features Grid */}
        <div className="max-w-6xl mx-auto mb-12">
          <h3 className="text-2xl font-bold text-gray-900 text-center mb-8">
            🚀 الميزات المتاحة
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Authentication System */}
            <div className="bg-white rounded-lg shadow-lg p-6 border-t-4 border-blue-500">
              <Shield className="h-12 w-12 text-blue-600 mb-4" />
              <h4 className="text-lg font-semibold text-gray-900 mb-2">
                نظام مصادقة متقدم
              </h4>
              <p className="text-gray-600 text-sm mb-4">
                تسجيل دخول آمن مع 5 أدوار مختلفة وصلاحيات دقيقة
              </p>
              <div className="space-y-1 text-xs text-gray-500">
                <div>• مدير النظام (Admin)</div>
                <div>• مدير (Manager)</div>
                <div>• صيدلي (Pharmacist)</div>
                <div>• كاشير (Cashier)</div>
                <div>• مشاهد (Viewer)</div>
              </div>
            </div>

            {/* Sales Management */}
            <div className="bg-white rounded-lg shadow-lg p-6 border-t-4 border-green-500">
              <ShoppingCart className="h-12 w-12 text-green-600 mb-4" />
              <h4 className="text-lg font-semibold text-gray-900 mb-2">
                إدارة المبيعات
              </h4>
              <p className="text-gray-600 text-sm mb-4">
                نظام مبيعات شامل مع طباعة الفواتير وإدارة الهدايا
              </p>
              <div className="space-y-1 text-xs text-gray-500">
                <div>• إنشاء فواتير المبيعات</div>
                <div>• إدارة الهدايا</div>
                <div>• طباعة احترافية</div>
                <div>• تتبع المدفوعات</div>
              </div>
            </div>

            {/* Inventory Management */}
            <div className="bg-white rounded-lg shadow-lg p-6 border-t-4 border-purple-500">
              <Package className="h-12 w-12 text-purple-600 mb-4" />
              <h4 className="text-lg font-semibold text-gray-900 mb-2">
                إدارة المخزون
              </h4>
              <p className="text-gray-600 text-sm mb-4">
                تتبع شامل للأدوية والكميات وتواريخ الانتهاء
              </p>
              <div className="space-y-1 text-xs text-gray-500">
                <div>• تتبع الكميات</div>
                <div>• تواريخ الانتهاء</div>
                <div>• إدارة الدفعات</div>
                <div>• تنبيهات المخزون</div>
              </div>
            </div>

            {/* User Management */}
            <div className="bg-white rounded-lg shadow-lg p-6 border-t-4 border-red-500">
              <Users className="h-12 w-12 text-red-600 mb-4" />
              <h4 className="text-lg font-semibold text-gray-900 mb-2">
                إدارة المستخدمين
              </h4>
              <p className="text-gray-600 text-sm mb-4">
                إدارة شاملة للمستخدمين والصلاحيات
              </p>
              <div className="space-y-1 text-xs text-gray-500">
                <div>• إضافة مستخدمين</div>
                <div>• تخصيص الصلاحيات</div>
                <div>• تفعيل/تعطيل الحسابات</div>
                <div>• سجل النشاطات</div>
              </div>
            </div>

            {/* Reports */}
            <div className="bg-white rounded-lg shadow-lg p-6 border-t-4 border-yellow-500">
              <BarChart3 className="h-12 w-12 text-yellow-600 mb-4" />
              <h4 className="text-lg font-semibold text-gray-900 mb-2">
                التقارير المتقدمة
              </h4>
              <p className="text-gray-600 text-sm mb-4">
                تقارير شاملة مع طباعة احترافية
              </p>
              <div className="space-y-1 text-xs text-gray-500">
                <div>• تقارير المبيعات</div>
                <div>• تقارير المخزون</div>
                <div>• التقارير المالية</div>
                <div>• تصدير Excel</div>
              </div>
            </div>

            {/* Security */}
            <div className="bg-white rounded-lg shadow-lg p-6 border-t-4 border-indigo-500">
              <Shield className="h-12 w-12 text-indigo-600 mb-4" />
              <h4 className="text-lg font-semibold text-gray-900 mb-2">
                الأمان والحماية
              </h4>
              <p className="text-gray-600 text-sm mb-4">
                نظام أمان متقدم مع تسجيل النشاطات
              </p>
              <div className="space-y-1 text-xs text-gray-500">
                <div>• تشفير كلمات المرور</div>
                <div>• جلسات آمنة</div>
                <div>• تسجيل النشاطات</div>
                <div>• حماية الصفحات</div>
              </div>
            </div>
          </div>
        </div>

        {/* Login Instructions */}
        <div className="max-w-2xl mx-auto mb-12">
          <div className="bg-blue-50 border border-blue-200 rounded-xl p-8">
            <h3 className="text-xl font-bold text-blue-900 mb-4 text-center">
              🔐 معلومات تسجيل الدخول للاختبار
            </h3>
            
            <div className="bg-white rounded-lg p-6 border border-blue-200">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <strong className="text-blue-800">اسم المستخدم:</strong>
                  <div className="bg-gray-100 p-2 rounded mt-1 font-mono">admin</div>
                </div>
                <div>
                  <strong className="text-blue-800">كلمة المرور:</strong>
                  <div className="bg-gray-100 p-2 rounded mt-1 font-mono">admin123</div>
                </div>
              </div>
              
              <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
                <p className="text-yellow-800 text-sm">
                  <strong>ملاحظة:</strong> هذه بيانات اختبار مؤقتة. في الإنتاج، ستحتاج لإعداد قاعدة البيانات الحقيقية.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="text-center space-y-4">
          <div className="space-x-4 space-x-reverse">
            <Link
              href="/login"
              className="inline-flex items-center gap-2 bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium"
            >
              <Shield className="h-5 w-5" />
              تسجيل الدخول
              <ArrowRight className="h-4 w-4" />
            </Link>
            
            <Link
              href="/test"
              className="inline-flex items-center gap-2 bg-gray-600 text-white px-8 py-3 rounded-lg hover:bg-gray-700 transition-colors font-medium"
            >
              اختبار النظام
            </Link>
          </div>
          
          <p className="text-sm text-gray-500">
            أو تصفح الصفحات المختلفة لاختبار الميزات
          </p>
        </div>

        {/* Footer */}
        <div className="mt-16 text-center border-t border-gray-200 pt-8">
          <p className="text-gray-500 text-sm">
            © 2024 مكتب لارين العلمي - جميع الحقوق محفوظة
          </p>
          <p className="text-gray-400 text-xs mt-2">
            نظام إدارة الصيدلية الإصدار 1.0.0
          </p>
        </div>
      </div>
    </div>
  )
}
