'use client'

import { useState, useEffect } from 'react'
import AppLayout from '@/components/AppLayout'
import { Bug, Plus, Save, Eye } from 'lucide-react'

export default function DebugSalesPage() {
  const [availableMedicines, setAvailableMedicines] = useState<any[]>([])
  const [selectedMedicine, setSelectedMedicine] = useState<any>(null)
  const [quantity, setQuantity] = useState(1)
  const [unitPrice, setUnitPrice] = useState(0)
  const [testItems, setTestItems] = useState<any[]>([])
  const [debugLog, setDebugLog] = useState<string[]>([])

  useEffect(() => {
    loadMedicines()
  }, [])

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    setDebugLog(prev => [...prev, `[${timestamp}] ${message}`])
    console.log(message)
  }

  const loadMedicines = () => {
    try {
      const medicines = JSON.parse(localStorage.getItem('medicines') || '[]')
      const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')
      
      // Combine medicines with their batches
      const medicinesWithBatches = medicines.map((medicine: any) => ({
        ...medicine,
        batches: batches.filter((batch: any) => batch.medicine_id === medicine.id)
      })).filter((medicine: any) => medicine.batches.length > 0)
      
      setAvailableMedicines(medicinesWithBatches)
      addLog(`✅ تم تحميل ${medicinesWithBatches.length} دواء مع دفعات`)
    } catch (error) {
      addLog(`❌ خطأ في تحميل الأدوية: ${error}`)
    }
  }

  const addTestItem = () => {
    if (!selectedMedicine || !selectedMedicine.batches[0]) {
      addLog('❌ يرجى اختيار دواء صحيح')
      return
    }

    const batch = selectedMedicine.batches[0]
    const newItem = {
      id: `test_${Date.now()}`,
      batchId: batch.id,
      medicineName: selectedMedicine.name,
      medicine_name: selectedMedicine.name,
      quantity: quantity,
      unitPrice: unitPrice,
      totalPrice: quantity * unitPrice,
      batch: batch
    }

    setTestItems(prev => [...prev, newItem])
    addLog(`✅ تم إضافة عنصر: ${selectedMedicine.name} - الكمية: ${quantity}`)
  }

  const testSaveProcess = async () => {
    if (testItems.length === 0) {
      addLog('❌ لا توجد عناصر للاختبار')
      return
    }

    addLog('🔄 بدء اختبار عملية الحفظ...')

    try {
      // Simulate the same process as sales page
      const invoiceData = {
        invoice_number: `TEST-${Date.now()}`,
        customer_id: null,
        customer_name: 'عميل تجريبي',
        total_amount: testItems.reduce((sum, item) => sum + item.totalPrice, 0),
        discount_amount: 0,
        final_amount: testItems.reduce((sum, item) => sum + item.totalPrice, 0),
        payment_method: 'cash',
        payment_status: 'paid',
        notes: 'فاتورة تجريبية للاختبار',
        private_notes: ''
      }

      // Prepare items exactly like sales page
      const dbItems = testItems.map(item => ({
        medicine_batch_id: item.batchId,
        quantity: item.quantity,
        unit_price: item.unitPrice,
        total_price: item.totalPrice,
        is_gift: false,
        medicine_name: item.medicineName,
        medicineName: item.medicineName
      }))

      addLog('📄 بيانات الفاتورة التجريبية:')
      addLog(JSON.stringify(invoiceData, null, 2))
      addLog('📦 عناصر الفاتورة التجريبية:')
      addLog(JSON.stringify(dbItems, null, 2))

      // Import the function dynamically to test it
      const { completeSalesTransaction } = await import('@/lib/database')
      
      addLog('💾 استدعاء completeSalesTransaction...')
      const result = await completeSalesTransaction(invoiceData, dbItems)
      
      addLog('✅ نتيجة الحفظ:')
      addLog(JSON.stringify(result, null, 2))

      if (result.success) {
        addLog('🎉 تم حفظ الفاتورة التجريبية بنجاح!')

        // Check what was actually saved
        const savedItems = JSON.parse(localStorage.getItem('sales_invoice_items') || '[]')
        const lastItems = savedItems.slice(-dbItems.length)

        addLog('🔍 العناصر المحفوظة فعلياً:')
        lastItems.forEach((item: any, index: number) => {
          addLog(`العنصر ${index + 1}: ${item.medicine_name || item.medicineName || 'غير محدد'}`)
        })

        // Now test the print retrieval process
        addLog('🖨️ اختبار عملية استرجاع البيانات للطباعة...')
        const invoiceId = result.data.invoiceId

        const { getSalesInvoiceForPrint } = await import('@/lib/database')
        const printResult = await getSalesInvoiceForPrint(invoiceId)

        addLog('📄 نتيجة استرجاع بيانات الطباعة:')
        addLog(JSON.stringify(printResult, null, 2))

        if (printResult.success && printResult.data) {
          addLog('🔍 أسماء الأدوية في بيانات الطباعة:')
          printResult.data.sales_invoice_items?.forEach((item: any, index: number) => {
            const medicineName = item.medicine_batches?.medicines?.name || item.medicine_name || item.medicineName || 'غير محدد'
            addLog(`عنصر الطباعة ${index + 1}: ${medicineName}`)
            addLog(`  - medicine_name: ${item.medicine_name}`)
            addLog(`  - medicineName: ${item.medicineName}`)
            addLog(`  - medicine_batches.medicines.name: ${item.medicine_batches?.medicines?.name}`)
            addLog(`  - batch_id: ${item.medicine_batch_id}`)
          })

          // Test the actual print template
          addLog('🖨️ اختبار قالب الطباعة الفعلي...')
          testPrintTemplate(printResult.data)

          // Test actual print function
          addLog('🖨️ اختبار دالة الطباعة الفعلية...')
          testActualPrint(printResult.data)
        }
      } else {
        addLog(`❌ فشل في حفظ الفاتورة: ${result.error}`)
      }

    } catch (error) {
      addLog(`💥 خطأ في اختبار الحفظ: ${error}`)
    }
  }

  const testActualPrint = async (invoiceData: any) => {
    try {
      addLog('📄 اختبار دالة الطباعة الفعلية من النظام...')

      // Import the actual print function
      const { printInvoice } = await import('@/hooks/usePrintSettings')
      const { usePrintSettings } = await import('@/hooks/usePrintSettings')

      // Get default print settings
      const defaultSettings = {
        companyName: 'صيدلية الشفاء',
        companyNameEn: 'Al-Shifa Pharmacy',
        companyAddress: 'بغداد - الكرادة - شارع الرئيسي',
        footerText: 'شكراً لتعاملكم معنا'
      }

      addLog('⚙️ إعدادات الطباعة:')
      addLog(JSON.stringify(defaultSettings, null, 2))

      addLog('📋 بيانات الفاتورة للطباعة:')
      addLog(JSON.stringify(invoiceData, null, 2))

      // Call the actual print function
      addLog('🖨️ استدعاء دالة printInvoice...')
      await printInvoice(invoiceData, 'sales', defaultSettings)

      addLog('✅ تم استدعاء دالة الطباعة - تحقق من النافذة المفتوحة!')

    } catch (error) {
      addLog(`❌ خطأ في اختبار الطباعة الفعلية: ${error}`)
    }
  }

  const testPrintTemplate = (invoiceData: any) => {
    addLog('📄 اختبار قالب الطباعة...')

    // Test the exact same logic used in print templates
    invoiceData.sales_invoice_items?.forEach((item: any, index: number) => {
      // Test different ways of getting medicine name (same as print template)
      const method1 = item.medicine_batches?.medicines?.name
      const method2 = item.medicine_name
      const method3 = item.medicineName
      const finalName = method1 || method2 || method3 || 'غير محدد'

      addLog(`قالب الطباعة - العنصر ${index + 1}:`)
      addLog(`  الطريقة 1 (medicine_batches.medicines.name): ${method1}`)
      addLog(`  الطريقة 2 (medicine_name): ${method2}`)
      addLog(`  الطريقة 3 (medicineName): ${method3}`)
      addLog(`  النتيجة النهائية: ${finalName}`)
    })

    // Create actual print window to test
    addLog('🖨️ إنشاء نافذة طباعة تجريبية...')

    const printContent = `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <title>فاتورة تجريبية - ${invoiceData.invoice_number}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
          .header { text-align: center; margin-bottom: 20px; }
          .items-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
          .items-table th, .items-table td { border: 1px solid #ddd; padding: 8px; text-align: center; }
          .items-table th { background-color: #f5f5f5; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>فاتورة تجريبية</h1>
          <h2>رقم الفاتورة: ${invoiceData.invoice_number}</h2>
        </div>

        <table class="items-table">
          <thead>
            <tr>
              <th>اسم الدواء</th>
              <th>الكمية</th>
              <th>سعر الوحدة</th>
              <th>الإجمالي</th>
            </tr>
          </thead>
          <tbody>
            ${invoiceData.sales_invoice_items?.map((item: any) => {
              const medicineName = item.medicine_batches?.medicines?.name || item.medicine_name || item.medicineName || 'غير محدد'
              return `
                <tr>
                  <td>${medicineName}</td>
                  <td>${item.quantity}</td>
                  <td>${item.unit_price.toFixed(2)}</td>
                  <td>${item.total_price.toFixed(2)}</td>
                </tr>
              `
            }).join('') || '<tr><td colspan="4">لا توجد عناصر</td></tr>'}
          </tbody>
        </table>

        <div style="margin-top: 20px;">
          <p><strong>المجموع النهائي:</strong> ${invoiceData.final_amount?.toFixed(2) || '0.00'} جنيه</p>
        </div>
      </body>
      </html>
    `

    const printWindow = window.open('', '_blank')
    if (printWindow) {
      printWindow.document.write(printContent)
      printWindow.document.close()
      addLog('✅ تم فتح نافذة الطباعة التجريبية - تحقق من أسماء الأدوية!')
    } else {
      addLog('❌ فشل في فتح نافذة الطباعة')
    }
  }

  const clearTest = () => {
    setTestItems([])
    setDebugLog([])
    setSelectedMedicine(null)
    setQuantity(1)
    setUnitPrice(0)
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center gap-3 mb-6">
            <div className="bg-red-50 p-3 rounded-lg">
              <Bug className="h-6 w-6 text-red-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">اختبار عملية إنشاء الفواتير</h1>
              <p className="text-gray-600">اختبار مباشر لعملية حفظ الفواتير وأسماء الأدوية</p>
            </div>
          </div>

          {/* Medicine Selection */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">اختر دواء:</label>
              <select
                value={selectedMedicine?.id || ''}
                onChange={(e) => {
                  const medicine = availableMedicines.find(m => m.id === e.target.value)
                  setSelectedMedicine(medicine)
                  setUnitPrice(medicine?.batches[0]?.selling_price || 0)
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg"
              >
                <option value="">اختر دواء...</option>
                {availableMedicines.map((medicine) => (
                  <option key={medicine.id} value={medicine.id}>
                    {medicine.name} - {medicine.batches.length} دفعة
                  </option>
                ))}
              </select>
            </div>

            <div className="grid grid-cols-2 gap-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">الكمية:</label>
                <input
                  type="number"
                  value={quantity}
                  onChange={(e) => setQuantity(Number(e.target.value))}
                  min="1"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">سعر الوحدة:</label>
                <input
                  type="number"
                  value={unitPrice}
                  onChange={(e) => setUnitPrice(Number(e.target.value))}
                  min="0"
                  step="0.01"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg"
                />
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex gap-3 mb-6">
            <button
              onClick={addTestItem}
              disabled={!selectedMedicine}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:bg-gray-300 flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              إضافة عنصر
            </button>
            
            <button
              onClick={testSaveProcess}
              disabled={testItems.length === 0}
              className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 disabled:bg-gray-300 flex items-center gap-2"
            >
              <Save className="h-4 w-4" />
              اختبار الحفظ
            </button>
            
            <button
              onClick={clearTest}
              className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 flex items-center gap-2"
            >
              مسح الاختبار
            </button>
          </div>

          {/* Test Items */}
          {testItems.length > 0 && (
            <div className="bg-blue-50 rounded-lg p-4 mb-6">
              <h3 className="font-medium text-gray-900 mb-3">العناصر التجريبية:</h3>
              <div className="space-y-2">
                {testItems.map((item, index) => (
                  <div key={item.id} className="bg-white p-3 rounded border">
                    <div className="grid grid-cols-4 gap-4 text-sm">
                      <div><strong>الدواء:</strong> {item.medicineName}</div>
                      <div><strong>الكمية:</strong> {item.quantity}</div>
                      <div><strong>السعر:</strong> {item.unitPrice}</div>
                      <div><strong>الإجمالي:</strong> {item.totalPrice}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Debug Log */}
          <div className="bg-black text-green-400 p-4 rounded font-mono text-sm max-h-96 overflow-y-auto">
            <h3 className="text-white mb-2">سجل التشخيص:</h3>
            {debugLog.map((log, index) => (
              <div key={index} className="mb-1">{log}</div>
            ))}
            {debugLog.length === 0 && (
              <div className="text-gray-500">لا توجد رسائل تشخيص بعد...</div>
            )}
          </div>
        </div>
      </div>
    </AppLayout>
  )
}
