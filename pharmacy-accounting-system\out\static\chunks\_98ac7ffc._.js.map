{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%287%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useEffect, useState } from 'react'\n\n// أنواع البيانات المؤقتة\ninterface User {\n  id: string\n  username: string\n  email: string\n  full_name: string\n  role: string\n  permissions: UserPermissions\n  is_active: boolean\n  last_login: string | null\n  created_at: string\n}\n\ninterface UserPermissions {\n  sales_view: boolean\n  sales_create: boolean\n  sales_edit: boolean\n  sales_delete: boolean\n  sales_print: boolean\n  sales_view_prices: boolean\n  purchases_view: boolean\n  purchases_create: boolean\n  purchases_edit: boolean\n  purchases_delete: boolean\n  purchases_print: boolean\n  inventory_view: boolean\n  inventory_create: boolean\n  inventory_edit: boolean\n  inventory_delete: boolean\n  inventory_print: boolean\n  customers_view: boolean\n  customers_create: boolean\n  customers_edit: boolean\n  customers_delete: boolean\n  suppliers_view: boolean\n  suppliers_create: boolean\n  suppliers_edit: boolean\n  suppliers_delete: boolean\n  reports_view: boolean\n  reports_financial: boolean\n  reports_detailed: boolean\n  reports_export: boolean\n  users_view: boolean\n  users_create: boolean\n  users_edit: boolean\n  users_delete: boolean\n  settings_view: boolean\n  settings_edit: boolean\n  cashbox_view: boolean\n  cashbox_manage: boolean\n  returns_view: boolean\n  returns_create: boolean\n  returns_edit: boolean\n  returns_delete: boolean\n}\n\n// دوال مؤقتة للاختبار\nconst authenticateUser = async (username: string, password: string, rememberMe: boolean = false) => {\n  // محاكاة تسجيل دخول ناجح\n  if (username === 'admin' && password === 'admin123') {\n    const mockUser: User = {\n      id: '1',\n      username: 'admin',\n      email: '<EMAIL>',\n      full_name: 'مدير النظام',\n      role: 'admin',\n      permissions: {\n        sales_view: true,\n        sales_create: true,\n        sales_edit: true,\n        sales_delete: true,\n        sales_print: true,\n        sales_view_prices: true,\n        purchases_view: true,\n        purchases_create: true,\n        purchases_edit: true,\n        purchases_delete: true,\n        purchases_print: true,\n        inventory_view: true,\n        inventory_create: true,\n        inventory_edit: true,\n        inventory_delete: true,\n        inventory_print: true,\n        customers_view: true,\n        customers_create: true,\n        customers_edit: true,\n        customers_delete: true,\n        suppliers_view: true,\n        suppliers_create: true,\n        suppliers_edit: true,\n        suppliers_delete: true,\n        reports_view: true,\n        reports_financial: true,\n        reports_detailed: true,\n        reports_export: true,\n        users_view: true,\n        users_create: true,\n        users_edit: true,\n        users_delete: true,\n        settings_view: true,\n        settings_edit: true,\n        cashbox_view: true,\n        cashbox_manage: true,\n        returns_view: true,\n        returns_create: true,\n        returns_edit: true,\n        returns_delete: true,\n      },\n      is_active: true,\n      last_login: new Date().toISOString(),\n      created_at: '2024-01-01T00:00:00Z'\n    }\n\n    return {\n      success: true,\n      user: mockUser,\n      session: { token: 'mock-session-token' }\n    }\n  }\n\n  return {\n    success: false,\n    error: 'اسم المستخدم أو كلمة المرور غير صحيحة'\n  }\n}\n\nconst logoutUser = async (token: string) => {\n  return { success: true }\n}\n\nconst validateSession = async (token: string) => {\n  if (token === 'mock-session-token') {\n    return {\n      success: true,\n      user: {\n        id: '1',\n        username: 'admin',\n        email: '<EMAIL>',\n        full_name: 'مدير النظام',\n        role: 'admin',\n        permissions: {\n          sales_view: true,\n          sales_create: true,\n          sales_edit: true,\n          sales_delete: true,\n          sales_print: true,\n          sales_view_prices: true,\n          purchases_view: true,\n          purchases_create: true,\n          purchases_edit: true,\n          purchases_delete: true,\n          purchases_print: true,\n          inventory_view: true,\n          inventory_create: true,\n          inventory_edit: true,\n          inventory_delete: true,\n          inventory_print: true,\n          customers_view: true,\n          customers_create: true,\n          customers_edit: true,\n          customers_delete: true,\n          suppliers_view: true,\n          suppliers_create: true,\n          suppliers_edit: true,\n          suppliers_delete: true,\n          reports_view: true,\n          reports_financial: true,\n          reports_detailed: true,\n          reports_export: true,\n          users_view: true,\n          users_create: true,\n          users_edit: true,\n          users_delete: true,\n          settings_view: true,\n          settings_edit: true,\n          cashbox_view: true,\n          cashbox_manage: true,\n          returns_view: true,\n          returns_create: true,\n          returns_edit: true,\n          returns_delete: true,\n        },\n        is_active: true\n      }\n    }\n  }\n\n  return { success: false, error: 'جلسة غير صالحة' }\n}\n\nconst logActivity = async (data: any) => {\n  console.log('Activity logged:', data)\n  return { success: true }\n}\n\ninterface AuthContextType {\n  user: User | null\n  permissions: UserPermissions | null\n  isLoading: boolean\n  isAuthenticated: boolean\n  login: (username: string, password: string, rememberMe?: boolean) => Promise<{ success: boolean; error?: string }>\n  logout: () => Promise<void>\n  hasPermission: (permission: keyof UserPermissions) => boolean\n  hasAnyPermission: (permissions: (keyof UserPermissions)[]) => boolean\n  hasRole: (role: string) => boolean\n  refreshUser: () => Promise<void>\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n\ninterface AuthProviderProps {\n  children: React.ReactNode\n}\n\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\n  const [user, setUser] = useState<User | null>(null)\n  const [permissions, setPermissions] = useState<UserPermissions | null>(null)\n  const [isLoading, setIsLoading] = useState(true)\n\n  // التحقق من الجلسة عند تحميل التطبيق\n  useEffect(() => {\n    checkSession()\n  }, [])\n\n  const checkSession = async () => {\n    try {\n      const sessionToken = localStorage.getItem('sessionToken')\n      if (!sessionToken) {\n        setIsLoading(false)\n        return\n      }\n\n      const result = await validateSession(sessionToken)\n      if (result.success && result.user) {\n        setUser(result.user as User)\n        setPermissions(result.user.permissions as UserPermissions)\n      } else {\n        localStorage.removeItem('sessionToken')\n      }\n    } catch (error) {\n      console.error('Error checking session:', error)\n      localStorage.removeItem('sessionToken')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const login = async (username: string, password: string, rememberMe: boolean = false) => {\n    try {\n      setIsLoading(true)\n      const result = await authenticateUser(username, password, rememberMe)\n      \n      if (result.success && result.user && result.session) {\n        setUser(result.user as User)\n        setPermissions(result.user.permissions as UserPermissions)\n        localStorage.setItem('sessionToken', result.session.token)\n        \n        return { success: true }\n      } else {\n        return { success: false, error: result.error || 'فشل في تسجيل الدخول' }\n      }\n    } catch (error: any) {\n      return { success: false, error: error.message || 'حدث خطأ غير متوقع' }\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const logout = async () => {\n    try {\n      const sessionToken = localStorage.getItem('sessionToken')\n      if (sessionToken) {\n        await logoutUser(sessionToken)\n      }\n      \n      setUser(null)\n      setPermissions(null)\n      localStorage.removeItem('sessionToken')\n    } catch (error) {\n      console.error('Error during logout:', error)\n      // حتى لو فشل في الخادم، نقوم بتنظيف البيانات المحلية\n      setUser(null)\n      setPermissions(null)\n      localStorage.removeItem('sessionToken')\n    }\n  }\n\n  const hasPermission = (permission: keyof UserPermissions): boolean => {\n    if (!permissions) return false\n    return permissions[permission] === true\n  }\n\n  const hasAnyPermission = (permissionsList: (keyof UserPermissions)[]): boolean => {\n    if (!permissions) return false\n    return permissionsList.some(permission => permissions[permission] === true)\n  }\n\n  const hasRole = (role: string): boolean => {\n    if (!user) return false\n    return user.role === role\n  }\n\n  const refreshUser = async () => {\n    await checkSession()\n  }\n\n  const value: AuthContextType = {\n    user,\n    permissions,\n    isLoading,\n    isAuthenticated: !!user,\n    login,\n    logout,\n    hasPermission,\n    hasAnyPermission,\n    hasRole,\n    refreshUser\n  }\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  )\n}\n\n// Hook للتحقق من الصلاحيات\nexport const usePermissions = () => {\n  const { permissions, hasPermission, hasAnyPermission } = useAuth()\n  \n  return {\n    permissions,\n    hasPermission,\n    hasAnyPermission,\n    \n    // صلاحيات المبيعات\n    canViewSales: hasPermission('sales_view'),\n    canCreateSales: hasPermission('sales_create'),\n    canEditSales: hasPermission('sales_edit'),\n    canDeleteSales: hasPermission('sales_delete'),\n    canPrintSales: hasPermission('sales_print'),\n    canViewPrices: hasPermission('sales_view_prices'),\n    \n    // صلاحيات المشتريات\n    canViewPurchases: hasPermission('purchases_view'),\n    canCreatePurchases: hasPermission('purchases_create'),\n    canEditPurchases: hasPermission('purchases_edit'),\n    canDeletePurchases: hasPermission('purchases_delete'),\n    canPrintPurchases: hasPermission('purchases_print'),\n    \n    // صلاحيات المخزون\n    canViewInventory: hasPermission('inventory_view'),\n    canCreateInventory: hasPermission('inventory_create'),\n    canEditInventory: hasPermission('inventory_edit'),\n    canDeleteInventory: hasPermission('inventory_delete'),\n    canPrintInventory: hasPermission('inventory_print'),\n    \n    // صلاحيات العملاء\n    canViewCustomers: hasPermission('customers_view'),\n    canCreateCustomers: hasPermission('customers_create'),\n    canEditCustomers: hasPermission('customers_edit'),\n    canDeleteCustomers: hasPermission('customers_delete'),\n    \n    // صلاحيات الموردين\n    canViewSuppliers: hasPermission('suppliers_view'),\n    canCreateSuppliers: hasPermission('suppliers_create'),\n    canEditSuppliers: hasPermission('suppliers_edit'),\n    canDeleteSuppliers: hasPermission('suppliers_delete'),\n    \n    // صلاحيات التقارير\n    canViewReports: hasPermission('reports_view'),\n    canViewFinancialReports: hasPermission('reports_financial'),\n    canViewDetailedReports: hasPermission('reports_detailed'),\n    canExportReports: hasPermission('reports_export'),\n    \n    // صلاحيات المستخدمين\n    canViewUsers: hasPermission('users_view'),\n    canCreateUsers: hasPermission('users_create'),\n    canEditUsers: hasPermission('users_edit'),\n    canDeleteUsers: hasPermission('users_delete'),\n    \n    // صلاحيات الإعدادات\n    canViewSettings: hasPermission('settings_view'),\n    canEditSettings: hasPermission('settings_edit'),\n    \n    // صلاحيات الصندوق\n    canViewCashbox: hasPermission('cashbox_view'),\n    canManageCashbox: hasPermission('cashbox_manage'),\n    \n    // صلاحيات المرتجعات\n    canViewReturns: hasPermission('returns_view'),\n    canCreateReturns: hasPermission('returns_create'),\n    canEditReturns: hasPermission('returns_edit'),\n    canDeleteReturns: hasPermission('returns_delete'),\n    \n    // دوال مساعدة\n    canAccessSalesModule: hasAnyPermission(['sales_view', 'sales_create', 'sales_edit']),\n    canAccessPurchasesModule: hasAnyPermission(['purchases_view', 'purchases_create', 'purchases_edit']),\n    canAccessInventoryModule: hasAnyPermission(['inventory_view', 'inventory_create', 'inventory_edit']),\n    canAccessReportsModule: hasPermission('reports_view'),\n    canAccessUsersModule: hasAnyPermission(['users_view', 'users_create', 'users_edit']),\n    canAccessSettingsModule: hasAnyPermission(['settings_view', 'settings_edit']),\n  }\n}\n\n// Hook لتسجيل النشاطات\nexport const useActivityLogger = () => {\n  const { user } = useAuth()\n  \n  const logUserActivity = async (action: string, description: string, additionalData?: {\n    table_name?: string\n    record_id?: string\n    old_values?: any\n    new_values?: any\n  }) => {\n    if (!user) return\n    \n    try {\n      await logActivity({\n        user_id: user.id,\n        action,\n        description,\n        ...additionalData\n      })\n    } catch (error) {\n      console.error('Error logging activity:', error)\n    }\n  }\n  \n  return { logUserActivity }\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;;;AAFA;;AA4DA,sBAAsB;AACtB,MAAM,mBAAmB,eAAO,UAAkB;QAAkB,8EAAsB;IACxF,yBAAyB;IACzB,IAAI,aAAa,WAAW,aAAa,YAAY;QACnD,MAAM,WAAiB;YACrB,IAAI;YACJ,UAAU;YACV,OAAO;YACP,WAAW;YACX,MAAM;YACN,aAAa;gBACX,YAAY;gBACZ,cAAc;gBACd,YAAY;gBACZ,cAAc;gBACd,aAAa;gBACb,mBAAmB;gBACnB,gBAAgB;gBAChB,kBAAkB;gBAClB,gBAAgB;gBAChB,kBAAkB;gBAClB,iBAAiB;gBACjB,gBAAgB;gBAChB,kBAAkB;gBAClB,gBAAgB;gBAChB,kBAAkB;gBAClB,iBAAiB;gBACjB,gBAAgB;gBAChB,kBAAkB;gBAClB,gBAAgB;gBAChB,kBAAkB;gBAClB,gBAAgB;gBAChB,kBAAkB;gBAClB,gBAAgB;gBAChB,kBAAkB;gBAClB,cAAc;gBACd,mBAAmB;gBACnB,kBAAkB;gBAClB,gBAAgB;gBAChB,YAAY;gBACZ,cAAc;gBACd,YAAY;gBACZ,cAAc;gBACd,eAAe;gBACf,eAAe;gBACf,cAAc;gBACd,gBAAgB;gBAChB,cAAc;gBACd,gBAAgB;gBAChB,cAAc;gBACd,gBAAgB;YAClB;YACA,WAAW;YACX,YAAY,IAAI,OAAO,WAAW;YAClC,YAAY;QACd;QAEA,OAAO;YACL,SAAS;YACT,MAAM;YACN,SAAS;gBAAE,OAAO;YAAqB;QACzC;IACF;IAEA,OAAO;QACL,SAAS;QACT,OAAO;IACT;AACF;AAEA,MAAM,aAAa,OAAO;IACxB,OAAO;QAAE,SAAS;IAAK;AACzB;AAEA,MAAM,kBAAkB,OAAO;IAC7B,IAAI,UAAU,sBAAsB;QAClC,OAAO;YACL,SAAS;YACT,MAAM;gBACJ,IAAI;gBACJ,UAAU;gBACV,OAAO;gBACP,WAAW;gBACX,MAAM;gBACN,aAAa;oBACX,YAAY;oBACZ,cAAc;oBACd,YAAY;oBACZ,cAAc;oBACd,aAAa;oBACb,mBAAmB;oBACnB,gBAAgB;oBAChB,kBAAkB;oBAClB,gBAAgB;oBAChB,kBAAkB;oBAClB,iBAAiB;oBACjB,gBAAgB;oBAChB,kBAAkB;oBAClB,gBAAgB;oBAChB,kBAAkB;oBAClB,iBAAiB;oBACjB,gBAAgB;oBAChB,kBAAkB;oBAClB,gBAAgB;oBAChB,kBAAkB;oBAClB,gBAAgB;oBAChB,kBAAkB;oBAClB,gBAAgB;oBAChB,kBAAkB;oBAClB,cAAc;oBACd,mBAAmB;oBACnB,kBAAkB;oBAClB,gBAAgB;oBAChB,YAAY;oBACZ,cAAc;oBACd,YAAY;oBACZ,cAAc;oBACd,eAAe;oBACf,eAAe;oBACf,cAAc;oBACd,gBAAgB;oBAChB,cAAc;oBACd,gBAAgB;oBAChB,cAAc;oBACd,gBAAgB;gBAClB;gBACA,WAAW;YACb;QACF;IACF;IAEA,OAAO;QAAE,SAAS;QAAO,OAAO;IAAiB;AACnD;AAEA,MAAM,cAAc,OAAO;IACzB,QAAQ,GAAG,CAAC,oBAAoB;IAChC,OAAO;QAAE,SAAS;IAAK;AACzB;AAeA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,MAAM,UAAU;;IACrB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANa;AAYN,MAAM,eAA4C;QAAC,EAAE,QAAQ,EAAE;;IACpE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B;IACvE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qCAAqC;IACrC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;QACF;iCAAG,EAAE;IAEL,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,eAAe,aAAa,OAAO,CAAC;YAC1C,IAAI,CAAC,cAAc;gBACjB,aAAa;gBACb;YACF;YAEA,MAAM,SAAS,MAAM,gBAAgB;YACrC,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;gBACjC,QAAQ,OAAO,IAAI;gBACnB,eAAe,OAAO,IAAI,CAAC,WAAW;YACxC,OAAO;gBACL,aAAa,UAAU,CAAC;YAC1B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,aAAa,UAAU,CAAC;QAC1B,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,QAAQ,eAAO,UAAkB;YAAkB,8EAAsB;QAC7E,IAAI;YACF,aAAa;YACb,MAAM,SAAS,MAAM,iBAAiB,UAAU,UAAU;YAE1D,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,IAAI,OAAO,OAAO,EAAE;gBACnD,QAAQ,OAAO,IAAI;gBACnB,eAAe,OAAO,IAAI,CAAC,WAAW;gBACtC,aAAa,OAAO,CAAC,gBAAgB,OAAO,OAAO,CAAC,KAAK;gBAEzD,OAAO;oBAAE,SAAS;gBAAK;YACzB,OAAO;gBACL,OAAO;oBAAE,SAAS;oBAAO,OAAO,OAAO,KAAK,IAAI;gBAAsB;YACxE;QACF,EAAE,OAAO,OAAY;YACnB,OAAO;gBAAE,SAAS;gBAAO,OAAO,MAAM,OAAO,IAAI;YAAoB;QACvE,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,SAAS;QACb,IAAI;YACF,MAAM,eAAe,aAAa,OAAO,CAAC;YAC1C,IAAI,cAAc;gBAChB,MAAM,WAAW;YACnB;YAEA,QAAQ;YACR,eAAe;YACf,aAAa,UAAU,CAAC;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,qDAAqD;YACrD,QAAQ;YACR,eAAe;YACf,aAAa,UAAU,CAAC;QAC1B;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,CAAC,aAAa,OAAO;QACzB,OAAO,WAAW,CAAC,WAAW,KAAK;IACrC;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,CAAC,aAAa,OAAO;QACzB,OAAO,gBAAgB,IAAI,CAAC,CAAA,aAAc,WAAW,CAAC,WAAW,KAAK;IACxE;IAEA,MAAM,UAAU,CAAC;QACf,IAAI,CAAC,MAAM,OAAO;QAClB,OAAO,KAAK,IAAI,KAAK;IACvB;IAEA,MAAM,cAAc;QAClB,MAAM;IACR;IAEA,MAAM,QAAyB;QAC7B;QACA;QACA;QACA,iBAAiB,CAAC,CAAC;QACnB;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;IA9Ga;KAAA;AAiHN,MAAM,iBAAiB;;IAC5B,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,gBAAgB,EAAE,GAAG;IAEzD,OAAO;QACL;QACA;QACA;QAEA,mBAAmB;QACnB,cAAc,cAAc;QAC5B,gBAAgB,cAAc;QAC9B,cAAc,cAAc;QAC5B,gBAAgB,cAAc;QAC9B,eAAe,cAAc;QAC7B,eAAe,cAAc;QAE7B,oBAAoB;QACpB,kBAAkB,cAAc;QAChC,oBAAoB,cAAc;QAClC,kBAAkB,cAAc;QAChC,oBAAoB,cAAc;QAClC,mBAAmB,cAAc;QAEjC,kBAAkB;QAClB,kBAAkB,cAAc;QAChC,oBAAoB,cAAc;QAClC,kBAAkB,cAAc;QAChC,oBAAoB,cAAc;QAClC,mBAAmB,cAAc;QAEjC,kBAAkB;QAClB,kBAAkB,cAAc;QAChC,oBAAoB,cAAc;QAClC,kBAAkB,cAAc;QAChC,oBAAoB,cAAc;QAElC,mBAAmB;QACnB,kBAAkB,cAAc;QAChC,oBAAoB,cAAc;QAClC,kBAAkB,cAAc;QAChC,oBAAoB,cAAc;QAElC,mBAAmB;QACnB,gBAAgB,cAAc;QAC9B,yBAAyB,cAAc;QACvC,wBAAwB,cAAc;QACtC,kBAAkB,cAAc;QAEhC,qBAAqB;QACrB,cAAc,cAAc;QAC5B,gBAAgB,cAAc;QAC9B,cAAc,cAAc;QAC5B,gBAAgB,cAAc;QAE9B,oBAAoB;QACpB,iBAAiB,cAAc;QAC/B,iBAAiB,cAAc;QAE/B,kBAAkB;QAClB,gBAAgB,cAAc;QAC9B,kBAAkB,cAAc;QAEhC,oBAAoB;QACpB,gBAAgB,cAAc;QAC9B,kBAAkB,cAAc;QAChC,gBAAgB,cAAc;QAC9B,kBAAkB,cAAc;QAEhC,cAAc;QACd,sBAAsB,iBAAiB;YAAC;YAAc;YAAgB;SAAa;QACnF,0BAA0B,iBAAiB;YAAC;YAAkB;YAAoB;SAAiB;QACnG,0BAA0B,iBAAiB;YAAC;YAAkB;YAAoB;SAAiB;QACnG,wBAAwB,cAAc;QACtC,sBAAsB,iBAAiB;YAAC;YAAc;YAAgB;SAAa;QACnF,yBAAyB,iBAAiB;YAAC;YAAiB;SAAgB;IAC9E;AACF;IA5Ea;;QAC8C;;;AA8EpD,MAAM,oBAAoB;;IAC/B,MAAM,EAAE,IAAI,EAAE,GAAG;IAEjB,MAAM,kBAAkB,OAAO,QAAgB,aAAqB;QAMlE,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,MAAM,YAAY;gBAChB,SAAS,KAAK,EAAE;gBAChB;gBACA;gBACA,GAAG,cAAc;YACnB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,OAAO;QAAE;IAAgB;AAC3B;IAxBa;;QACM", "debugId": null}}, {"offset": {"line": 412, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%287%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/contexts/NotificationContext.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useState, useEffect } from 'react'\nimport { supabase } from '@/lib/supabase'\n\nexport interface Notification {\n  id: string\n  type: 'info' | 'warning' | 'error' | 'success'\n  title: string\n  message: string\n  category: 'inventory' | 'sales' | 'system' | 'user' | 'financial'\n  priority: 'low' | 'medium' | 'high' | 'critical'\n  isRead: boolean\n  actionUrl?: string\n  actionLabel?: string\n  data?: any\n  createdAt: string\n  expiresAt?: string\n}\n\ninterface NotificationContextType {\n  notifications: Notification[]\n  unreadCount: number\n  addNotification: (notification: Omit<Notification, 'id' | 'isRead' | 'createdAt'>) => void\n  markAsRead: (id: string) => void\n  markAllAsRead: () => void\n  removeNotification: (id: string) => void\n  clearAll: () => void\n  getNotificationsByCategory: (category: string) => Notification[]\n  getNotificationsByPriority: (priority: string) => Notification[]\n  refreshNotifications: () => Promise<void>\n}\n\nconst NotificationContext = createContext<NotificationContextType | undefined>(undefined)\n\nexport const useNotifications = () => {\n  const context = useContext(NotificationContext)\n  if (context === undefined) {\n    throw new Error('useNotifications must be used within a NotificationProvider')\n  }\n  return context\n}\n\ninterface NotificationProviderProps {\n  children: React.ReactNode\n}\n\nexport const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {\n  const [notifications, setNotifications] = useState<Notification[]>([])\n\n  // تحديث التنبيهات عند تحميل المكون\n  useEffect(() => {\n    loadNotifications()\n    \n    // تحديث التنبيهات كل دقيقة\n    const interval = setInterval(loadNotifications, 60000)\n    \n    return () => clearInterval(interval)\n  }, [])\n\n  const loadNotifications = async () => {\n    try {\n      // تحميل التنبيهات من قاعدة البيانات أو إنشاء تنبيهات تجريبية\n      const mockNotifications = await generateSystemNotifications()\n      setNotifications(mockNotifications)\n    } catch (error) {\n      console.error('Error loading notifications:', error)\n    }\n  }\n\n  const generateSystemNotifications = async (): Promise<Notification[]> => {\n    const notifications: Notification[] = []\n    const now = new Date()\n\n    // تنبيهات المخزون\n    notifications.push({\n      id: '1',\n      type: 'warning',\n      title: 'أدوية قاربت على الانتهاء',\n      message: 'يوجد 5 أدوية ستنتهي صلاحيتها خلال 30 يوم',\n      category: 'inventory',\n      priority: 'high',\n      isRead: false,\n      actionUrl: '/inventory?filter=expiring',\n      actionLabel: 'عرض الأدوية',\n      createdAt: new Date(now.getTime() - 2 * 60 * 60 * 1000).toISOString(), // منذ ساعتين\n    })\n\n    notifications.push({\n      id: '2',\n      type: 'error',\n      title: 'نفاد مخزون',\n      message: 'باراسيتامول 500mg - الكمية المتبقية: 0',\n      category: 'inventory',\n      priority: 'critical',\n      isRead: false,\n      actionUrl: '/inventory?search=باراسيتامول',\n      actionLabel: 'إضافة مخزون',\n      createdAt: new Date(now.getTime() - 30 * 60 * 1000).toISOString(), // منذ 30 دقيقة\n    })\n\n    notifications.push({\n      id: '3',\n      type: 'warning',\n      title: 'مخزون منخفض',\n      message: 'يوجد 8 أدوية كميتها أقل من الحد الأدنى',\n      category: 'inventory',\n      priority: 'medium',\n      isRead: false,\n      actionUrl: '/inventory?filter=low-stock',\n      actionLabel: 'عرض التفاصيل',\n      createdAt: new Date(now.getTime() - 4 * 60 * 60 * 1000).toISOString(), // منذ 4 ساعات\n    })\n\n    // تنبيهات المبيعات\n    notifications.push({\n      id: '4',\n      type: 'info',\n      title: 'مبيعات اليوم',\n      message: 'تم تحقيق 2,450,000 د.ع من المبيعات اليوم',\n      category: 'sales',\n      priority: 'low',\n      isRead: true,\n      actionUrl: '/sales-records',\n      actionLabel: 'عرض التفاصيل',\n      createdAt: new Date(now.getTime() - 6 * 60 * 60 * 1000).toISOString(), // منذ 6 ساعات\n    })\n\n    // تنبيهات النظام\n    notifications.push({\n      id: '5',\n      type: 'success',\n      title: 'تحديث النظام',\n      message: 'تم تحديث النظام بنجاح إلى الإصدار 1.0.1',\n      category: 'system',\n      priority: 'low',\n      isRead: false,\n      createdAt: new Date(now.getTime() - 24 * 60 * 60 * 1000).toISOString(), // منذ يوم\n    })\n\n    // تنبيهات المستخدمين\n    notifications.push({\n      id: '6',\n      type: 'info',\n      title: 'مستخدم جديد',\n      message: 'تم إضافة مستخدم جديد: أحمد الصيدلي',\n      category: 'user',\n      priority: 'low',\n      isRead: true,\n      actionUrl: '/users',\n      actionLabel: 'إدارة المستخدمين',\n      createdAt: new Date(now.getTime() - 12 * 60 * 60 * 1000).toISOString(), // منذ 12 ساعة\n    })\n\n    // تنبيهات مالية\n    notifications.push({\n      id: '7',\n      type: 'warning',\n      title: 'فواتير معلقة',\n      message: 'يوجد 12 فاتورة معلقة الدفع بقيمة 850,000 د.ع',\n      category: 'financial',\n      priority: 'high',\n      isRead: false,\n      actionUrl: '/sales-records?filter=pending',\n      actionLabel: 'عرض الفواتير',\n      createdAt: new Date(now.getTime() - 8 * 60 * 60 * 1000).toISOString(), // منذ 8 ساعات\n    })\n\n    return notifications.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())\n  }\n\n  const addNotification = (notificationData: Omit<Notification, 'id' | 'isRead' | 'createdAt'>) => {\n    const newNotification: Notification = {\n      ...notificationData,\n      id: Date.now().toString(),\n      isRead: false,\n      createdAt: new Date().toISOString(),\n    }\n    \n    setNotifications(prev => [newNotification, ...prev])\n  }\n\n  const markAsRead = (id: string) => {\n    setNotifications(prev =>\n      prev.map(notification =>\n        notification.id === id\n          ? { ...notification, isRead: true }\n          : notification\n      )\n    )\n  }\n\n  const markAllAsRead = () => {\n    setNotifications(prev =>\n      prev.map(notification => ({ ...notification, isRead: true }))\n    )\n  }\n\n  const removeNotification = (id: string) => {\n    setNotifications(prev => prev.filter(notification => notification.id !== id))\n  }\n\n  const clearAll = () => {\n    setNotifications([])\n  }\n\n  const getNotificationsByCategory = (category: string) => {\n    return notifications.filter(notification => notification.category === category)\n  }\n\n  const getNotificationsByPriority = (priority: string) => {\n    return notifications.filter(notification => notification.priority === priority)\n  }\n\n  const refreshNotifications = async () => {\n    await loadNotifications()\n  }\n\n  const unreadCount = notifications.filter(n => !n.isRead).length\n\n  const value: NotificationContextType = {\n    notifications,\n    unreadCount,\n    addNotification,\n    markAsRead,\n    markAllAsRead,\n    removeNotification,\n    clearAll,\n    getNotificationsByCategory,\n    getNotificationsByPriority,\n    refreshNotifications,\n  }\n\n  return (\n    <NotificationContext.Provider value={value}>\n      {children}\n    </NotificationContext.Provider>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAiCA,MAAM,oCAAsB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAuC;AAExE,MAAM,mBAAmB;;IAC9B,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANa;AAYN,MAAM,uBAA4D;QAAC,EAAE,QAAQ,EAAE;;IACpF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IAErE,mCAAmC;IACnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR;YAEA,2BAA2B;YAC3B,MAAM,WAAW,YAAY,mBAAmB;YAEhD;kDAAO,IAAM,cAAc;;QAC7B;yCAAG,EAAE;IAEL,MAAM,oBAAoB;QACxB,IAAI;YACF,6DAA6D;YAC7D,MAAM,oBAAoB,MAAM;YAChC,iBAAiB;QACnB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF;IAEA,MAAM,8BAA8B;QAClC,MAAM,gBAAgC,EAAE;QACxC,MAAM,MAAM,IAAI;QAEhB,kBAAkB;QAClB,cAAc,IAAI,CAAC;YACjB,IAAI;YACJ,MAAM;YACN,OAAO;YACP,SAAS;YACT,UAAU;YACV,UAAU;YACV,QAAQ;YACR,WAAW;YACX,aAAa;YACb,WAAW,IAAI,KAAK,IAAI,OAAO,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;QACrE;QAEA,cAAc,IAAI,CAAC;YACjB,IAAI;YACJ,MAAM;YACN,OAAO;YACP,SAAS;YACT,UAAU;YACV,UAAU;YACV,QAAQ;YACR,WAAW;YACX,aAAa;YACb,WAAW,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,KAAK,MAAM,WAAW;QACjE;QAEA,cAAc,IAAI,CAAC;YACjB,IAAI;YACJ,MAAM;YACN,OAAO;YACP,SAAS;YACT,UAAU;YACV,UAAU;YACV,QAAQ;YACR,WAAW;YACX,aAAa;YACb,WAAW,IAAI,KAAK,IAAI,OAAO,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;QACrE;QAEA,mBAAmB;QACnB,cAAc,IAAI,CAAC;YACjB,IAAI;YACJ,MAAM;YACN,OAAO;YACP,SAAS;YACT,UAAU;YACV,UAAU;YACV,QAAQ;YACR,WAAW;YACX,aAAa;YACb,WAAW,IAAI,KAAK,IAAI,OAAO,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;QACrE;QAEA,iBAAiB;QACjB,cAAc,IAAI,CAAC;YACjB,IAAI;YACJ,MAAM;YACN,OAAO;YACP,SAAS;YACT,UAAU;YACV,UAAU;YACV,QAAQ;YACR,WAAW,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,KAAK,KAAK,MAAM,WAAW;QACtE;QAEA,qBAAqB;QACrB,cAAc,IAAI,CAAC;YACjB,IAAI;YACJ,MAAM;YACN,OAAO;YACP,SAAS;YACT,UAAU;YACV,UAAU;YACV,QAAQ;YACR,WAAW;YACX,aAAa;YACb,WAAW,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,KAAK,KAAK,MAAM,WAAW;QACtE;QAEA,gBAAgB;QAChB,cAAc,IAAI,CAAC;YACjB,IAAI;YACJ,MAAM;YACN,OAAO;YACP,SAAS;YACT,UAAU;YACV,UAAU;YACV,QAAQ;YACR,WAAW;YACX,aAAa;YACb,WAAW,IAAI,KAAK,IAAI,OAAO,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;QACrE;QAEA,OAAO,cAAc,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;IACrG;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,kBAAgC;YACpC,GAAG,gBAAgB;YACnB,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,QAAQ;YACR,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,iBAAiB,CAAA,OAAQ;gBAAC;mBAAoB;aAAK;IACrD;IAEA,MAAM,aAAa,CAAC;QAClB,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,eACP,aAAa,EAAE,KAAK,KAChB;oBAAE,GAAG,YAAY;oBAAE,QAAQ;gBAAK,IAChC;IAGV;IAEA,MAAM,gBAAgB;QACpB,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,eAAgB,CAAC;oBAAE,GAAG,YAAY;oBAAE,QAAQ;gBAAK,CAAC;IAE/D;IAEA,MAAM,qBAAqB,CAAC;QAC1B,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,eAAgB,aAAa,EAAE,KAAK;IAC3E;IAEA,MAAM,WAAW;QACf,iBAAiB,EAAE;IACrB;IAEA,MAAM,6BAA6B,CAAC;QAClC,OAAO,cAAc,MAAM,CAAC,CAAA,eAAgB,aAAa,QAAQ,KAAK;IACxE;IAEA,MAAM,6BAA6B,CAAC;QAClC,OAAO,cAAc,MAAM,CAAC,CAAA,eAAgB,aAAa,QAAQ,KAAK;IACxE;IAEA,MAAM,uBAAuB;QAC3B,MAAM;IACR;IAEA,MAAM,cAAc,cAAc,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,MAAM,EAAE,MAAM;IAE/D,MAAM,QAAiC;QACrC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,oBAAoB,QAAQ;QAAC,OAAO;kBAClC;;;;;;AAGP;IA/La;KAAA", "debugId": null}}, {"offset": {"line": 622, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%287%29/New%20folder%20%2819%29/pharmacy-accounting-system/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return type.displayName || \"Context\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\"),\n      REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      react_stack_bottom_frame: function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React.react_stack_bottom_frame.bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,KAAK,WAAW,IAAI;YAC7B,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aACP,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACL,KAAK,EACL,UAAU,EACV,SAAS;QAET,OAAO,MAAM,GAAG;QAChB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,OAAO,OAAO,IAAI,IACnC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI,EACJ,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,MACA,QACA,YACA,UACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,uHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,0BAA0B,SAAU,iBAAiB;YACnD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,MAAM,wBAAwB,CAAC,IAAI,CAC9D,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SACf,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;QAEJ,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,QACA,MACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 829, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%287%29/New%20folder%20%2819%29/pharmacy-accounting-system/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA;;KAEO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}]}