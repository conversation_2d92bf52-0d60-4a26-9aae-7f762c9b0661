{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%287%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/components/Sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { usePermissions } from '@/contexts/AuthContext'\nimport {\n  Home,\n  ShoppingCart,\n  Package,\n  Users,\n  UserCheck,\n  RotateCcw,\n  BarChart3,\n  Settings,\n  Pill,\n  FileText,\n  Wallet,\n  Shield,\n  Activity,\n  Bell,\n  Wrench,\n  Printer,\n  Bug\n} from 'lucide-react'\n\ninterface MenuItem {\n  title: string\n  href: string\n  icon: any\n  permission?: string\n  requireAny?: string[]\n}\n\nconst getMenuItems = (permissions: any): MenuItem[] => [\n  {\n    title: 'الرئيسية',\n    href: '/',\n    icon: Home\n  },\n  {\n    title: 'إدارة المخزون',\n    href: '/inventory',\n    icon: Package,\n    permission: 'inventory_view'\n  },\n  {\n    title: 'المبيعات',\n    href: '/sales',\n    icon: ShoppingCart,\n    permission: 'sales_view'\n  },\n\n  {\n    title: 'المشتريات',\n    href: '/purchases',\n    icon: Pill,\n    permission: 'purchases_view'\n  },\n\n  {\n    title: 'العملاء',\n    href: '/customers',\n    icon: Users,\n    permission: 'customers_view'\n  },\n  {\n    title: 'الموردين',\n    href: '/suppliers',\n    icon: UserCheck,\n    permission: 'suppliers_view'\n  },\n  {\n    title: 'المرتجعات',\n    href: '/returns',\n    icon: RotateCcw,\n    permission: 'returns_view'\n  },\n\n  {\n    title: 'الصندوق',\n    href: '/cashbox',\n    icon: Wallet,\n    permission: 'cashbox_view'\n  },\n  {\n    title: 'التقارير',\n    href: '/reports',\n    icon: BarChart3,\n    permission: 'reports_view'\n  },\n  {\n    title: 'إدارة المستخدمين',\n    href: '/users',\n    icon: Shield,\n    permission: 'users_view'\n  },\n  {\n    title: 'سجل النشاطات',\n    href: '/activity-log',\n    icon: Activity,\n    permission: 'users_view'\n  },\n  {\n    title: 'التنبيهات',\n    href: '/notifications',\n    icon: Bell\n  },\n  {\n    title: 'الإعدادات',\n    href: '/settings',\n    icon: Settings,\n    permission: 'settings_view'\n  },\n  {\n    title: 'إصلاح البيانات',\n    href: '/fix-data',\n    icon: Wrench,\n    permission: 'settings_view'\n  },\n  {\n    title: 'اختبار الطباعة',\n    href: '/test-print',\n    icon: Printer,\n    permission: 'sales_view'\n  },\n  {\n    title: 'تشخيص البيانات',\n    href: '/debug-data',\n    icon: Bug,\n    permission: 'settings_view'\n  },\n  {\n    title: 'اختبار المبيعات',\n    href: '/debug-sales',\n    icon: ShoppingCart,\n    permission: 'sales_view'\n  }\n]\n\ninterface SidebarProps {\n  isOpen?: boolean\n  onClose?: () => void\n}\n\nexport default function Sidebar({ isOpen = false, onClose }: SidebarProps = {}) {\n  const pathname = usePathname()\n  const { hasPermission, permissions } = usePermissions()\n\n  const menuItems = getMenuItems(permissions)\n\n  // إغلاق القائمة عند تغيير الصفحة\n  useEffect(() => {\n    if (onClose) {\n      onClose()\n    }\n  }, [pathname, onClose])\n\n  // إغلاق القائمة عند النقر خارجها\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      const sidebar = document.getElementById('mobile-sidebar')\n      const menuButton = document.getElementById('mobile-menu-button')\n\n      if (sidebar && !sidebar.contains(event.target as Node) &&\n          menuButton && !menuButton.contains(event.target as Node)) {\n        if (onClose) {\n          onClose()\n        }\n      }\n    }\n\n    if (isOpen) {\n      document.addEventListener('mousedown', handleClickOutside)\n      document.body.style.overflow = 'hidden'\n    } else {\n      document.body.style.overflow = 'unset'\n    }\n\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside)\n      document.body.style.overflow = 'unset'\n    }\n  }, [isOpen, onClose])\n\n  // تصفية العناصر بناءً على الصلاحيات\n  const visibleMenuItems = menuItems.filter(item => {\n    if (!item.permission) return true // العناصر بدون صلاحيات مطلوبة (مثل الرئيسية)\n    return hasPermission(item.permission as any)\n  })\n\n  return (\n    <>\n      {/* Mobile Overlay */}\n      {isOpen && (\n        <div\n          className=\"fixed inset-0 bg-black bg-opacity-50 z-30 md:hidden\"\n          onClick={onClose}\n        />\n      )}\n\n      {/* Sidebar */}\n      <div\n        id=\"mobile-sidebar\"\n        className={`bg-gradient-to-b from-white to-gray-50 shadow-2xl h-screen w-64 fixed right-0 top-0 z-40 border-l border-gray-200 transform transition-transform duration-300 ease-in-out ${\n          isOpen ? 'translate-x-0' : 'translate-x-full md:translate-x-0'\n        }`}\n      >\n        <div className=\"p-6\">\n        <div className=\"flex items-center gap-3 mb-8 p-4 bg-gradient-to-r from-blue-600 to-indigo-700 rounded-xl text-white shadow-lg\">\n          <div className=\"bg-white bg-opacity-20 p-2 rounded-lg\">\n            <Pill className=\"h-6 w-6 text-white\" />\n          </div>\n          <div>\n            <h1 className=\"text-lg font-bold\">نظام الصيدلية</h1>\n            <p className=\"text-sm text-blue-100\">مكتب لارين العلمي</p>\n          </div>\n        </div>\n\n        <nav className=\"space-y-2\">\n          {visibleMenuItems.map((item) => {\n            const Icon = item.icon\n            const isActive = pathname === item.href\n\n            return (\n              <Link\n                key={item.href}\n                href={item.href}\n                className={`flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300 group hover-lift ${\n                  isActive\n                    ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg transform scale-105'\n                    : 'text-gray-600 hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-100 hover:text-gray-800 hover:shadow-md'\n                }`}\n              >\n                <Icon className={`h-5 w-5 transition-transform duration-300 ${isActive ? 'text-white' : 'group-hover:scale-110'}`} />\n                <span className=\"font-medium\">{item.title}</span>\n                {isActive && (\n                  <div className=\"mr-auto w-2 h-2 bg-white rounded-full animate-pulse\"></div>\n                )}\n              </Link>\n            )\n          })}\n        </nav>\n\n        {/* Footer */}\n        <div className=\"absolute bottom-4 left-4 right-4\">\n          <div className=\"bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl p-4 text-center border border-gray-200 shadow-sm\">\n            <div className=\"flex items-center justify-center gap-2 mb-2\">\n              <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n              <p className=\"text-xs text-gray-600 font-medium\">\n                متصل\n              </p>\n            </div>\n            <p className=\"text-xs text-gray-500\">\n              © 2024 مكتب لارين العلمي\n            </p>\n            <p className=\"text-xs text-gray-400 mt-1\">\n              الإصدار 1.0.0\n            </p>\n          </div>\n        </div>\n      </div>\n      </div>\n    </>\n  )\n}\n\n// تصدير دالة للتحكم في القائمة من مكونات أخرى\nexport const useSidebar = () => {\n  const [isOpen, setIsOpen] = useState(false)\n  return { isOpen, setIsOpen, toggleSidebar: () => setIsOpen(!isOpen) }\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AANA;;;;;;AAkCA,MAAM,eAAe,CAAC,cAAiC;QACrD;YACE,OAAO;YACP,MAAM;YACN,MAAM,sMAAA,CAAA,OAAI;QACZ;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,2MAAA,CAAA,UAAO;YACb,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,yNAAA,CAAA,eAAY;YAClB,YAAY;QACd;QAEA;YACE,OAAO;YACP,MAAM;YACN,MAAM,qMAAA,CAAA,OAAI;YACV,YAAY;QACd;QAEA;YACE,OAAO;YACP,MAAM;YACN,MAAM,uMAAA,CAAA,QAAK;YACX,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,mNAAA,CAAA,YAAS;YACf,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,mNAAA,CAAA,YAAS;YACf,YAAY;QACd;QAEA;YACE,OAAO;YACP,MAAM;YACN,MAAM,yMAAA,CAAA,SAAM;YACZ,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,qNAAA,CAAA,YAAS;YACf,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,yMAAA,CAAA,SAAM;YACZ,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,6MAAA,CAAA,WAAQ;YACd,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,qMAAA,CAAA,OAAI;QACZ;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,6MAAA,CAAA,WAAQ;YACd,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,yMAAA,CAAA,SAAM;YACZ,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,2MAAA,CAAA,UAAO;YACb,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,mMAAA,CAAA,MAAG;YACT,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,yNAAA,CAAA,eAAY;YAClB,YAAY;QACd;KACD;AAOc,SAAS;QAAQ,EAAE,SAAS,KAAK,EAAE,OAAO,EAAgB,GAAzC,iEAA4C,CAAC;;IAC3E,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD;IAEpD,MAAM,YAAY,aAAa;IAE/B,iCAAiC;IACjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,IAAI,SAAS;gBACX;YACF;QACF;4BAAG;QAAC;QAAU;KAAQ;IAEtB,iCAAiC;IACjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,MAAM;wDAAqB,CAAC;oBAC1B,MAAM,UAAU,SAAS,cAAc,CAAC;oBACxC,MAAM,aAAa,SAAS,cAAc,CAAC;oBAE3C,IAAI,WAAW,CAAC,QAAQ,QAAQ,CAAC,MAAM,MAAM,KACzC,cAAc,CAAC,WAAW,QAAQ,CAAC,MAAM,MAAM,GAAW;wBAC5D,IAAI,SAAS;4BACX;wBACF;oBACF;gBACF;;YAEA,IAAI,QAAQ;gBACV,SAAS,gBAAgB,CAAC,aAAa;gBACvC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC,OAAO;gBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC;YAEA;qCAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;oBAC1C,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBACjC;;QACF;4BAAG;QAAC;QAAQ;KAAQ;IAEpB,oCAAoC;IACpC,MAAM,mBAAmB,UAAU,MAAM,CAAC,CAAA;QACxC,IAAI,CAAC,KAAK,UAAU,EAAE,OAAO,KAAK,6CAA6C;;QAC/E,OAAO,cAAc,KAAK,UAAU;IACtC;IAEA,qBACE;;YAEG,wBACC,6LAAC;gBACC,WAAU;gBACV,SAAS;;;;;;0BAKb,6LAAC;gBACC,IAAG;gBACH,WAAW,AAAC,6KAEX,OADC,SAAS,kBAAkB;0BAG7B,cAAA,6LAAC;oBAAI,WAAU;;sCACf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAElB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAoB;;;;;;sDAClC,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;sCAIzC,6LAAC;4BAAI,WAAU;sCACZ,iBAAiB,GAAG,CAAC,CAAC;gCACrB,MAAM,OAAO,KAAK,IAAI;gCACtB,MAAM,WAAW,aAAa,KAAK,IAAI;gCAEvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,AAAC,6FAIX,OAHC,WACI,0FACA;;sDAGN,6LAAC;4CAAK,WAAW,AAAC,6CAA8F,OAAlD,WAAW,eAAe;;;;;;sDACxF,6LAAC;4CAAK,WAAU;sDAAe,KAAK,KAAK;;;;;;wCACxC,0BACC,6LAAC;4CAAI,WAAU;;;;;;;mCAXZ,KAAK,IAAI;;;;;4BAepB;;;;;;sCAIF,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;;;;;;;kDAInD,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;kDAGrC,6LAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStD;GAxHwB;;QACL,qIAAA,CAAA,cAAW;QACW,kIAAA,CAAA,iBAAc;;;KAF/B;AA2HjB,MAAM,aAAa;;IACxB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,OAAO;QAAE;QAAQ;QAAW,eAAe,IAAM,UAAU,CAAC;IAAQ;AACtE;IAHa", "debugId": null}}, {"offset": {"line": 400, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%287%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/components/NotificationDropdown.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef, useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useNotifications } from '@/contexts/NotificationContext'\nimport {\n  Bell,\n  X,\n  Check,\n  CheckCheck,\n  Trash2,\n  ExternalLink,\n  AlertTriangle,\n  Info,\n  CheckCircle,\n  XCircle,\n  Clock,\n  Package,\n  ShoppingCart,\n  Settings,\n  Users,\n  DollarSign\n} from 'lucide-react'\n\nexport default function NotificationDropdown() {\n  const [isOpen, setIsOpen] = useState(false)\n  const [activeTab, setActiveTab] = useState<'all' | 'unread'>('all')\n  const dropdownRef = useRef<HTMLDivElement>(null)\n  const router = useRouter()\n  \n  const {\n    notifications,\n    unreadCount,\n    markAsRead,\n    markAllAsRead,\n    removeNotification,\n    clearAll\n  } = useNotifications()\n\n  // إغلاق القائمة عند النقر خارجها\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setIsOpen(false)\n      }\n    }\n\n    document.addEventListener('mousedown', handleClickOutside)\n    return () => document.removeEventListener('mousedown', handleClickOutside)\n  }, [])\n\n  const filteredNotifications = activeTab === 'unread' \n    ? notifications.filter(n => !n.isRead)\n    : notifications\n\n  const getNotificationIcon = (type: string, category: string) => {\n    if (type === 'error') return <XCircle className=\"h-4 w-4 text-red-500\" />\n    if (type === 'warning') return <AlertTriangle className=\"h-4 w-4 text-yellow-500\" />\n    if (type === 'success') return <CheckCircle className=\"h-4 w-4 text-green-500\" />\n    if (type === 'info') return <Info className=\"h-4 w-4 text-blue-500\" />\n    \n    // أيقونات حسب الفئة\n    if (category === 'inventory') return <Package className=\"h-4 w-4 text-purple-500\" />\n    if (category === 'sales') return <ShoppingCart className=\"h-4 w-4 text-green-500\" />\n    if (category === 'financial') return <DollarSign className=\"h-4 w-4 text-yellow-500\" />\n    if (category === 'user') return <Users className=\"h-4 w-4 text-blue-500\" />\n    if (category === 'system') return <Settings className=\"h-4 w-4 text-gray-500\" />\n    \n    return <Bell className=\"h-4 w-4 text-gray-500\" />\n  }\n\n  const getPriorityColor = (priority: string) => {\n    switch (priority) {\n      case 'critical': return 'border-r-4 border-red-500 bg-red-50'\n      case 'high': return 'border-r-4 border-orange-500 bg-orange-50'\n      case 'medium': return 'border-r-4 border-yellow-500 bg-yellow-50'\n      case 'low': return 'border-r-4 border-blue-500 bg-blue-50'\n      default: return 'border-r-4 border-gray-500 bg-gray-50'\n    }\n  }\n\n  const formatTimeAgo = (dateString: string) => {\n    const now = new Date()\n    const date = new Date(dateString)\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))\n    \n    if (diffInMinutes < 1) return 'الآن'\n    if (diffInMinutes < 60) return `منذ ${diffInMinutes} دقيقة`\n    \n    const diffInHours = Math.floor(diffInMinutes / 60)\n    if (diffInHours < 24) return `منذ ${diffInHours} ساعة`\n    \n    const diffInDays = Math.floor(diffInHours / 24)\n    return `منذ ${diffInDays} يوم`\n  }\n\n  const handleNotificationClick = (notification: any) => {\n    if (!notification.isRead) {\n      markAsRead(notification.id)\n    }\n    \n    if (notification.actionUrl) {\n      router.push(notification.actionUrl)\n      setIsOpen(false)\n    }\n  }\n\n  return (\n    <div className=\"relative\" ref={dropdownRef}>\n      {/* Bell Button */}\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"relative p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors\"\n      >\n        <Bell className=\"h-5 w-5\" />\n        {unreadCount > 0 && (\n          <span className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center animate-pulse\">\n            {unreadCount > 99 ? '99+' : unreadCount}\n          </span>\n        )}\n      </button>\n\n      {/* Dropdown */}\n      {isOpen && (\n        <div className=\"absolute left-0 mt-2 w-96 bg-white rounded-lg shadow-xl border border-gray-200 z-50 max-h-[600px] overflow-hidden\">\n          {/* Header */}\n          <div className=\"p-4 border-b border-gray-200\">\n            <div className=\"flex items-center justify-between mb-3\">\n              <h3 className=\"text-lg font-semibold text-gray-900\">التنبيهات</h3>\n              <button\n                onClick={() => setIsOpen(false)}\n                className=\"text-gray-400 hover:text-gray-600\"\n              >\n                <X className=\"h-5 w-5\" />\n              </button>\n            </div>\n            \n            {/* Tabs */}\n            <div className=\"flex space-x-2 space-x-reverse\">\n              <button\n                onClick={() => setActiveTab('all')}\n                className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${\n                  activeTab === 'all'\n                    ? 'bg-blue-100 text-blue-700'\n                    : 'text-gray-600 hover:text-gray-800'\n                }`}\n              >\n                الكل ({notifications.length})\n              </button>\n              <button\n                onClick={() => setActiveTab('unread')}\n                className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${\n                  activeTab === 'unread'\n                    ? 'bg-blue-100 text-blue-700'\n                    : 'text-gray-600 hover:text-gray-800'\n                }`}\n              >\n                غير مقروءة ({unreadCount})\n              </button>\n            </div>\n          </div>\n\n          {/* Actions */}\n          {notifications.length > 0 && (\n            <div className=\"p-3 border-b border-gray-100 bg-gray-50\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex space-x-2 space-x-reverse\">\n                  {unreadCount > 0 && (\n                    <button\n                      onClick={markAllAsRead}\n                      className=\"flex items-center gap-1 text-xs text-blue-600 hover:text-blue-800\"\n                    >\n                      <CheckCheck className=\"h-3 w-3\" />\n                      تحديد الكل كمقروء\n                    </button>\n                  )}\n                </div>\n                <button\n                  onClick={clearAll}\n                  className=\"flex items-center gap-1 text-xs text-red-600 hover:text-red-800\"\n                >\n                  <Trash2 className=\"h-3 w-3\" />\n                  مسح الكل\n                </button>\n              </div>\n            </div>\n          )}\n\n          {/* Notifications List */}\n          <div className=\"max-h-96 overflow-y-auto\">\n            {filteredNotifications.length === 0 ? (\n              <div className=\"p-8 text-center\">\n                <Bell className=\"h-12 w-12 text-gray-300 mx-auto mb-3\" />\n                <p className=\"text-gray-500\">\n                  {activeTab === 'unread' ? 'لا توجد تنبيهات غير مقروءة' : 'لا توجد تنبيهات'}\n                </p>\n              </div>\n            ) : (\n              <div className=\"divide-y divide-gray-100\">\n                {filteredNotifications.map((notification) => (\n                  <div\n                    key={notification.id}\n                    className={`p-4 hover:bg-gray-50 transition-colors cursor-pointer ${\n                      !notification.isRead ? getPriorityColor(notification.priority) : ''\n                    }`}\n                    onClick={() => handleNotificationClick(notification)}\n                  >\n                    <div className=\"flex items-start gap-3\">\n                      {/* Icon */}\n                      <div className=\"flex-shrink-0 mt-1\">\n                        {getNotificationIcon(notification.type, notification.category)}\n                      </div>\n                      \n                      {/* Content */}\n                      <div className=\"flex-1 min-w-0\">\n                        <div className=\"flex items-start justify-between\">\n                          <div className=\"flex-1\">\n                            <h4 className={`text-sm font-medium ${\n                              !notification.isRead ? 'text-gray-900' : 'text-gray-700'\n                            }`}>\n                              {notification.title}\n                            </h4>\n                            <p className=\"text-sm text-gray-600 mt-1 line-clamp-2\">\n                              {notification.message}\n                            </p>\n                            \n                            {/* Action Button */}\n                            {notification.actionUrl && (\n                              <button className=\"inline-flex items-center gap-1 text-xs text-blue-600 hover:text-blue-800 mt-2\">\n                                <ExternalLink className=\"h-3 w-3\" />\n                                {notification.actionLabel || 'عرض التفاصيل'}\n                              </button>\n                            )}\n                          </div>\n                          \n                          {/* Actions */}\n                          <div className=\"flex items-center gap-1 mr-2\">\n                            {!notification.isRead && (\n                              <button\n                                onClick={(e) => {\n                                  e.stopPropagation()\n                                  markAsRead(notification.id)\n                                }}\n                                className=\"p-1 text-gray-400 hover:text-blue-600\"\n                                title=\"تحديد كمقروء\"\n                              >\n                                <Check className=\"h-3 w-3\" />\n                              </button>\n                            )}\n                            <button\n                              onClick={(e) => {\n                                e.stopPropagation()\n                                removeNotification(notification.id)\n                              }}\n                              className=\"p-1 text-gray-400 hover:text-red-600\"\n                              title=\"حذف\"\n                            >\n                              <X className=\"h-3 w-3\" />\n                            </button>\n                          </div>\n                        </div>\n                        \n                        {/* Time */}\n                        <div className=\"flex items-center gap-1 mt-2 text-xs text-gray-500\">\n                          <Clock className=\"h-3 w-3\" />\n                          {formatTimeAgo(notification.createdAt)}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n\n          {/* Footer */}\n          {notifications.length > 0 && (\n            <div className=\"p-3 border-t border-gray-200 bg-gray-50\">\n              <button\n                onClick={() => {\n                  router.push('/notifications')\n                  setIsOpen(false)\n                }}\n                className=\"w-full text-center text-sm text-blue-600 hover:text-blue-800 font-medium\"\n              >\n                عرض جميع التنبيهات\n              </button>\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;;AAwBe,SAAS;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IAC7D,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,EACJ,aAAa,EACb,WAAW,EACX,UAAU,EACV,aAAa,EACb,kBAAkB,EAClB,QAAQ,EACT,GAAG,CAAA,GAAA,0IAAA,CAAA,mBAAgB,AAAD;IAEnB,iCAAiC;IACjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,MAAM;qEAAqB,CAAC;oBAC1B,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;wBAC9E,UAAU;oBACZ;gBACF;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;kDAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;;QACzD;yCAAG,EAAE;IAEL,MAAM,wBAAwB,cAAc,WACxC,cAAc,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,MAAM,IACnC;IAEJ,MAAM,sBAAsB,CAAC,MAAc;QACzC,IAAI,SAAS,SAAS,qBAAO,6LAAC,+MAAA,CAAA,UAAO;YAAC,WAAU;;;;;;QAChD,IAAI,SAAS,WAAW,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;QACxD,IAAI,SAAS,WAAW,qBAAO,6LAAC,8NAAA,CAAA,cAAW;YAAC,WAAU;;;;;;QACtD,IAAI,SAAS,QAAQ,qBAAO,6LAAC,qMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;QAE5C,oBAAoB;QACpB,IAAI,aAAa,aAAa,qBAAO,6LAAC,2MAAA,CAAA,UAAO;YAAC,WAAU;;;;;;QACxD,IAAI,aAAa,SAAS,qBAAO,6LAAC,yNAAA,CAAA,eAAY;YAAC,WAAU;;;;;;QACzD,IAAI,aAAa,aAAa,qBAAO,6LAAC,qNAAA,CAAA,aAAU;YAAC,WAAU;;;;;;QAC3D,IAAI,aAAa,QAAQ,qBAAO,6LAAC,uMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QACjD,IAAI,aAAa,UAAU,qBAAO,6LAAC,6MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;QAEtD,qBAAO,6LAAC,qMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;IACzB;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAO,OAAO;YACnB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,MAAM,IAAI;QAChB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE;QAE9E,IAAI,gBAAgB,GAAG,OAAO;QAC9B,IAAI,gBAAgB,IAAI,OAAO,AAAC,OAAoB,OAAd,eAAc;QAEpD,MAAM,cAAc,KAAK,KAAK,CAAC,gBAAgB;QAC/C,IAAI,cAAc,IAAI,OAAO,AAAC,OAAkB,OAAZ,aAAY;QAEhD,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;QAC5C,OAAO,AAAC,OAAiB,OAAX,YAAW;IAC3B;IAEA,MAAM,0BAA0B,CAAC;QAC/B,IAAI,CAAC,aAAa,MAAM,EAAE;YACxB,WAAW,aAAa,EAAE;QAC5B;QAEA,IAAI,aAAa,SAAS,EAAE;YAC1B,OAAO,IAAI,CAAC,aAAa,SAAS;YAClC,UAAU;QACZ;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;QAAW,KAAK;;0BAE7B,6LAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;;kCAEV,6LAAC,qMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;oBACf,cAAc,mBACb,6LAAC;wBAAK,WAAU;kCACb,cAAc,KAAK,QAAQ;;;;;;;;;;;;YAMjC,wBACC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAsC;;;;;;kDACpD,6LAAC;wCACC,SAAS,IAAM,UAAU;wCACzB,WAAU;kDAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAKjB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,AAAC,gEAIX,OAHC,cAAc,QACV,8BACA;;4CAEP;4CACQ,cAAc,MAAM;4CAAC;;;;;;;kDAE9B,6LAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,AAAC,gEAIX,OAHC,cAAc,WACV,8BACA;;4CAEP;4CACc;4CAAY;;;;;;;;;;;;;;;;;;;oBAM9B,cAAc,MAAM,GAAG,mBACtB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACZ,cAAc,mBACb,6LAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;8CAKxC,6LAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;;;;;;kCAQtC,6LAAC;wBAAI,WAAU;kCACZ,sBAAsB,MAAM,KAAK,kBAChC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC;oCAAE,WAAU;8CACV,cAAc,WAAW,+BAA+B;;;;;;;;;;;iDAI7D,6LAAC;4BAAI,WAAU;sCACZ,sBAAsB,GAAG,CAAC,CAAC,6BAC1B,6LAAC;oCAEC,WAAW,AAAC,yDAEX,OADC,CAAC,aAAa,MAAM,GAAG,iBAAiB,aAAa,QAAQ,IAAI;oCAEnE,SAAS,IAAM,wBAAwB;8CAEvC,cAAA,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;0DACZ,oBAAoB,aAAa,IAAI,EAAE,aAAa,QAAQ;;;;;;0DAI/D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAG,WAAW,AAAC,uBAEf,OADC,CAAC,aAAa,MAAM,GAAG,kBAAkB;kFAExC,aAAa,KAAK;;;;;;kFAErB,6LAAC;wEAAE,WAAU;kFACV,aAAa,OAAO;;;;;;oEAItB,aAAa,SAAS,kBACrB,6LAAC;wEAAO,WAAU;;0FAChB,6LAAC,yNAAA,CAAA,eAAY;gFAAC,WAAU;;;;;;4EACvB,aAAa,WAAW,IAAI;;;;;;;;;;;;;0EAMnC,6LAAC;gEAAI,WAAU;;oEACZ,CAAC,aAAa,MAAM,kBACnB,6LAAC;wEACC,SAAS,CAAC;4EACR,EAAE,eAAe;4EACjB,WAAW,aAAa,EAAE;wEAC5B;wEACA,WAAU;wEACV,OAAM;kFAEN,cAAA,6LAAC,uMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;;;;;;kFAGrB,6LAAC;wEACC,SAAS,CAAC;4EACR,EAAE,eAAe;4EACjB,mBAAmB,aAAa,EAAE;wEACpC;wEACA,WAAU;wEACV,OAAM;kFAEN,cAAA,6LAAC,+LAAA,CAAA,IAAC;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kEAMnB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAChB,cAAc,aAAa,SAAS;;;;;;;;;;;;;;;;;;;mCAhEtC,aAAa,EAAE;;;;;;;;;;;;;;;oBA2E7B,cAAc,MAAM,GAAG,mBACtB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,SAAS;gCACP,OAAO,IAAI,CAAC;gCACZ,UAAU;4BACZ;4BACA,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AASf;GA7QwB;;QAIP,qIAAA,CAAA,YAAS;QASpB,0IAAA,CAAA,mBAAgB;;;KAbE", "debugId": null}}, {"offset": {"line": 964, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%287%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/components/MobileMenuButton.tsx"], "sourcesContent": ["'use client'\n\nimport { Menu, X } from 'lucide-react'\n\ninterface MobileMenuButtonProps {\n  isOpen: boolean\n  onClick: () => void\n}\n\nexport default function MobileMenuButton({ isOpen, onClick }: MobileMenuButtonProps) {\n  return (\n    <button\n      id=\"mobile-menu-button\"\n      onClick={onClick}\n      className=\"md:hidden p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors\"\n      aria-label={isOpen ? 'إغلاق القائمة' : 'فتح القائمة'}\n    >\n      {isOpen ? (\n        <X className=\"h-6 w-6\" />\n      ) : (\n        <Menu className=\"h-6 w-6\" />\n      )}\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAFA;;;AASe,SAAS,iBAAiB,KAA0C;QAA1C,EAAE,MAAM,EAAE,OAAO,EAAyB,GAA1C;IACvC,qBACE,6LAAC;QACC,IAAG;QACH,SAAS;QACT,WAAU;QACV,cAAY,SAAS,kBAAkB;kBAEtC,uBACC,6LAAC,+LAAA,CAAA,IAAC;YAAC,WAAU;;;;;iCAEb,6LAAC,qMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;;;;;;AAIxB;KAfwB", "debugId": null}}, {"offset": {"line": 1011, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%287%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/components/Header.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/contexts/AuthContext'\nimport NotificationDropdown from './NotificationDropdown'\nimport MobileMenuButton from './MobileMenuButton'\nimport {\n  Search,\n  User,\n  LogOut,\n  Settings,\n  Shield,\n  ChevronDown,\n  Activity\n} from 'lucide-react'\n\ninterface HeaderProps {\n  onMobileMenuToggle?: () => void\n  isMobileMenuOpen?: boolean\n}\n\nexport default function Header({ onMobileMenuToggle, isMobileMenuOpen = false }: HeaderProps = {}) {\n  const [showUserMenu, setShowUserMenu] = useState(false)\n  const { user, logout } = useAuth()\n  const router = useRouter()\n\n  const handleLogout = async () => {\n    await logout()\n    router.push('/login')\n  }\n\n  const getRoleDisplayName = (role: string) => {\n    const roleNames: Record<string, string> = {\n      admin: 'مدير النظام',\n      manager: 'مدير',\n      pharmacist: 'صيدلي',\n      cashier: 'كاشير',\n      viewer: 'مشاهد'\n    }\n    return roleNames[role] || role\n  }\n\n  const getRoleColor = (role: string) => {\n    const colors: Record<string, string> = {\n      admin: 'bg-red-600',\n      manager: 'bg-purple-600',\n      pharmacist: 'bg-blue-600',\n      cashier: 'bg-green-600',\n      viewer: 'bg-gray-600'\n    }\n    return colors[role] || 'bg-gray-600'\n  }\n\n  return (\n    <header className=\"bg-white shadow-sm border-b border-gray-200 h-16 fixed top-0 left-0 right-0 md:right-64 z-30\">\n      <div className=\"flex items-center justify-between h-full px-2 md:px-6\">\n        <div className=\"flex items-center gap-2 md:gap-4\">\n          {/* Mobile Menu Button */}\n          {onMobileMenuToggle && (\n            <MobileMenuButton\n              isOpen={isMobileMenuOpen}\n              onClick={onMobileMenuToggle}\n            />\n          )}\n\n          <div className=\"relative hidden md:block\">\n            <Search className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n            <input\n              type=\"text\"\n              placeholder=\"البحث...\"\n              className=\"pl-4 pr-10 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent w-60 lg:w-80\"\n            />\n          </div>\n        </div>\n\n        <div className=\"flex items-center gap-4\">\n          {/* Notifications */}\n          <NotificationDropdown />\n\n          {/* User Menu */}\n          <div className=\"relative\">\n            <button\n              onClick={() => setShowUserMenu(!showUserMenu)}\n              className=\"flex items-center gap-3 border-r border-gray-200 pr-4 hover:bg-gray-50 rounded-lg p-2 transition-colors\"\n            >\n              <div className=\"text-right\">\n                <p className=\"text-sm font-medium text-gray-800\">{user?.full_name || 'مستخدم'}</p>\n                <p className=\"text-xs text-gray-500\">{user ? getRoleDisplayName(user.role) : ''}</p>\n              </div>\n              <div className={`${user ? getRoleColor(user.role) : 'bg-gray-600'} p-2 rounded-full`}>\n                <User className=\"h-4 w-4 text-white\" />\n              </div>\n              <ChevronDown className=\"h-4 w-4 text-gray-400\" />\n            </button>\n\n            {/* Dropdown Menu */}\n            {showUserMenu && (\n              <div className=\"absolute left-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50\">\n                {/* User Info */}\n                <div className=\"px-4 py-3 border-b border-gray-100\">\n                  <div className=\"flex items-center gap-3\">\n                    <div className={`${user ? getRoleColor(user.role) : 'bg-gray-600'} p-2 rounded-full`}>\n                      <User className=\"h-5 w-5 text-white\" />\n                    </div>\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-900\">{user?.full_name}</p>\n                      <p className=\"text-xs text-gray-500\">@{user?.username}</p>\n                      <p className=\"text-xs text-gray-500\">{user?.email}</p>\n                    </div>\n                  </div>\n                  <div className=\"mt-2\">\n                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${\n                      user?.role === 'admin' ? 'bg-red-100 text-red-800' :\n                      user?.role === 'manager' ? 'bg-purple-100 text-purple-800' :\n                      user?.role === 'pharmacist' ? 'bg-blue-100 text-blue-800' :\n                      user?.role === 'cashier' ? 'bg-green-100 text-green-800' :\n                      'bg-gray-100 text-gray-800'\n                    }`}>\n                      <Shield className=\"h-3 w-3 ml-1\" />\n                      {user ? getRoleDisplayName(user.role) : ''}\n                    </span>\n                  </div>\n                </div>\n\n                {/* Menu Items */}\n                <div className=\"py-1\">\n                  <button\n                    onClick={() => {\n                      setShowUserMenu(false)\n                      router.push('/profile')\n                    }}\n                    className=\"flex items-center gap-3 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                  >\n                    <User className=\"h-4 w-4\" />\n                    الملف الشخصي\n                  </button>\n\n                  <button\n                    onClick={() => {\n                      setShowUserMenu(false)\n                      router.push('/activity-log')\n                    }}\n                    className=\"flex items-center gap-3 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                  >\n                    <Activity className=\"h-4 w-4\" />\n                    سجل النشاطات\n                  </button>\n\n                  <button\n                    onClick={() => {\n                      setShowUserMenu(false)\n                      router.push('/settings')\n                    }}\n                    className=\"flex items-center gap-3 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                  >\n                    <Settings className=\"h-4 w-4\" />\n                    الإعدادات\n                  </button>\n                </div>\n\n                {/* Logout */}\n                <div className=\"border-t border-gray-100 pt-1\">\n                  <button\n                    onClick={() => {\n                      setShowUserMenu(false)\n                      handleLogout()\n                    }}\n                    className=\"flex items-center gap-3 w-full px-4 py-2 text-sm text-red-700 hover:bg-red-50\"\n                  >\n                    <LogOut className=\"h-4 w-4\" />\n                    تسجيل الخروج\n                  </button>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Click outside to close menu */}\n      {showUserMenu && (\n        <div\n          className=\"fixed inset-0 z-40\"\n          onClick={() => setShowUserMenu(false)}\n        />\n      )}\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAPA;;;;;;;AAsBe,SAAS;QAAO,EAAE,kBAAkB,EAAE,mBAAmB,KAAK,EAAe,GAA7D,iEAAgE,CAAC;;IAC9F,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe;QACnB,MAAM;QACN,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,YAAoC;YACxC,OAAO;YACP,SAAS;YACT,YAAY;YACZ,SAAS;YACT,QAAQ;QACV;QACA,OAAO,SAAS,CAAC,KAAK,IAAI;IAC5B;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,SAAiC;YACrC,OAAO;YACP,SAAS;YACT,YAAY;YACZ,SAAS;YACT,QAAQ;QACV;QACA,OAAO,MAAM,CAAC,KAAK,IAAI;IACzB;IAEA,qBACE,6LAAC;QAAO,WAAU;;0BAChB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;4BAEZ,oCACC,6LAAC,yIAAA,CAAA,UAAgB;gCACf,QAAQ;gCACR,SAAS;;;;;;0CAIb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;;;;;;;;;;;;;kCAKhB,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,6IAAA,CAAA,UAAoB;;;;;0CAGrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,gBAAgB,CAAC;wCAChC,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAqC,CAAA,iBAAA,2BAAA,KAAM,SAAS,KAAI;;;;;;kEACrE,6LAAC;wDAAE,WAAU;kEAAyB,OAAO,mBAAmB,KAAK,IAAI,IAAI;;;;;;;;;;;;0DAE/E,6LAAC;gDAAI,WAAW,AAAC,GAAiD,OAA/C,OAAO,aAAa,KAAK,IAAI,IAAI,eAAc;0DAChE,cAAA,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAElB,6LAAC,uNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;oCAIxB,8BACC,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAW,AAAC,GAAiD,OAA/C,OAAO,aAAa,KAAK,IAAI,IAAI,eAAc;0EAChE,cAAA,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;;;;;;0EAElB,6LAAC;;kFACC,6LAAC;wEAAE,WAAU;kFAAqC,iBAAA,2BAAA,KAAM,SAAS;;;;;;kFACjE,6LAAC;wEAAE,WAAU;;4EAAwB;4EAAE,iBAAA,2BAAA,KAAM,QAAQ;;;;;;;kFACrD,6LAAC;wEAAE,WAAU;kFAAyB,iBAAA,2BAAA,KAAM,KAAK;;;;;;;;;;;;;;;;;;kEAGrD,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAK,WAAW,AAAC,uEAMjB,OALC,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK,UAAU,4BACzB,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK,YAAY,kCAC3B,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK,eAAe,8BAC9B,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK,YAAY,gCAC3B;;8EAEA,6LAAC,yMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEACjB,OAAO,mBAAmB,KAAK,IAAI,IAAI;;;;;;;;;;;;;;;;;;0DAM9C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,SAAS;4DACP,gBAAgB;4DAChB,OAAO,IAAI,CAAC;wDACd;wDACA,WAAU;;0EAEV,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAY;;;;;;;kEAI9B,6LAAC;wDACC,SAAS;4DACP,gBAAgB;4DAChB,OAAO,IAAI,CAAC;wDACd;wDACA,WAAU;;0EAEV,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAY;;;;;;;kEAIlC,6LAAC;wDACC,SAAS;4DACP,gBAAgB;4DAChB,OAAO,IAAI,CAAC;wDACd;wDACA,WAAU;;0EAEV,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAY;;;;;;;;;;;;;0DAMpC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,SAAS;wDACP,gBAAgB;wDAChB;oDACF;oDACA,WAAU;;sEAEV,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAW3C,8BACC,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,gBAAgB;;;;;;;;;;;;AAKzC;GAvKwB;;QAEG,kIAAA,CAAA,UAAO;QACjB,qIAAA,CAAA,YAAS;;;KAHF", "debugId": null}}, {"offset": {"line": 1432, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%287%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/components/ErrorBoundary.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { AlertTriangle, RefreshCw } from 'lucide-react'\n\ninterface ErrorBoundaryState {\n  hasError: boolean\n  error?: Error\n  errorInfo?: React.ErrorInfo\n}\n\ninterface ErrorBoundaryProps {\n  children: React.ReactNode\n  fallback?: React.ComponentType<{ error: Error; retry: () => void }>\n}\n\nclass ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {\n  constructor(props: ErrorBoundaryProps) {\n    super(props)\n    this.state = { hasError: false }\n  }\n\n  static getDerivedStateFromError(error: Error): ErrorBoundaryState {\n    return {\n      hasError: true,\n      error\n    }\n  }\n\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\n    console.error('ErrorBoundary caught an error:', error, errorInfo)\n    this.setState({\n      error,\n      errorInfo\n    })\n  }\n\n  retry = () => {\n    this.setState({ hasError: false, error: undefined, errorInfo: undefined })\n  }\n\n  render() {\n    if (this.state.hasError) {\n      if (this.props.fallback) {\n        const FallbackComponent = this.props.fallback\n        return <FallbackComponent error={this.state.error!} retry={this.retry} />\n      }\n\n      return (\n        <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n          <div className=\"max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center\">\n            <div className=\"flex justify-center mb-4\">\n              <AlertTriangle className=\"h-12 w-12 text-red-500\" />\n            </div>\n            \n            <h1 className=\"text-xl font-bold text-gray-900 mb-2\">حدث خطأ غير متوقع</h1>\n            \n            <p className=\"text-gray-600 mb-4\">\n              عذراً، حدث خطأ أثناء تحميل هذه الصفحة. يرجى المحاولة مرة أخرى.\n            </p>\n            \n            {this.state.error && (\n              <div className=\"bg-red-50 border border-red-200 rounded-lg p-3 mb-4 text-left\">\n                <p className=\"text-sm text-red-800 font-mono\">\n                  {this.state.error.message}\n                </p>\n              </div>\n            )}\n            \n            <div className=\"flex gap-3 justify-center\">\n              <button\n                onClick={this.retry}\n                className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2\"\n              >\n                <RefreshCw className=\"h-4 w-4\" />\n                إعادة المحاولة\n              </button>\n              \n              <button\n                onClick={() => window.location.reload()}\n                className=\"bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700\"\n              >\n                إعادة تحميل الصفحة\n              </button>\n            </div>\n            \n            {process.env.NODE_ENV === 'development' && this.state.errorInfo && (\n              <details className=\"mt-4 text-left\">\n                <summary className=\"cursor-pointer text-sm text-gray-500 hover:text-gray-700\">\n                  تفاصيل الخطأ (للمطورين)\n                </summary>\n                <pre className=\"mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto max-h-40\">\n                  {this.state.error?.stack}\n                  {'\\n\\n'}\n                  {this.state.errorInfo?.componentStack}\n                </pre>\n              </details>\n            )}\n          </div>\n        </div>\n      )\n    }\n\n    return this.props.children\n  }\n}\n\n// Hook for functional components\nexport function useErrorHandler() {\n  const [error, setError] = React.useState<Error | null>(null)\n\n  const resetError = React.useCallback(() => {\n    setError(null)\n  }, [])\n\n  const handleError = React.useCallback((error: Error) => {\n    console.error('Error caught by useErrorHandler:', error)\n    setError(error)\n  }, [])\n\n  React.useEffect(() => {\n    if (error) {\n      throw error\n    }\n  }, [error])\n\n  return { handleError, resetError }\n}\n\n// Safe component wrapper\nexport function withErrorBoundary<P extends object>(\n  Component: React.ComponentType<P>,\n  fallback?: React.ComponentType<{ error: Error; retry: () => void }>\n) {\n  const WrappedComponent = (props: P) => (\n    <ErrorBoundary fallback={fallback}>\n      <Component {...props} />\n    </ErrorBoundary>\n  )\n\n  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`\n  \n  return WrappedComponent\n}\n\nexport default ErrorBoundary\n"], "names": [], "mappings": ";;;;;AAsFa;;;AApFb;AACA;AAAA;;;;AAHA;;;AAgBA,MAAM,sBAAsB,6JAAA,CAAA,UAAK,CAAC,SAAS;IAMzC,OAAO,yBAAyB,KAAY,EAAsB;QAChE,OAAO;YACL,UAAU;YACV;QACF;IACF;IAEA,kBAAkB,KAAY,EAAE,SAA0B,EAAE;QAC1D,QAAQ,KAAK,CAAC,kCAAkC,OAAO;QACvD,IAAI,CAAC,QAAQ,CAAC;YACZ;YACA;QACF;IACF;IAMA,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;gBAkDV,mBAEA;YAnDb,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;gBACvB,MAAM,oBAAoB,IAAI,CAAC,KAAK,CAAC,QAAQ;gBAC7C,qBAAO,6LAAC;oBAAkB,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;oBAAG,OAAO,IAAI,CAAC,KAAK;;;;;;YACvE;YAEA,qBACE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;;;;;;sCAG3B,6LAAC;4BAAG,WAAU;sCAAuC;;;;;;sCAErD,6LAAC;4BAAE,WAAU;sCAAqB;;;;;;wBAIjC,IAAI,CAAC,KAAK,CAAC,KAAK,kBACf,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CACV,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO;;;;;;;;;;;sCAK/B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAI,CAAC,KAAK;oCACnB,WAAU;;sDAEV,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAInC,6LAAC;oCACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;oCACrC,WAAU;8CACX;;;;;;;;;;;;wBAKF,oDAAyB,iBAAiB,IAAI,CAAC,KAAK,CAAC,SAAS,kBAC7D,6LAAC;4BAAQ,WAAU;;8CACjB,6LAAC;oCAAQ,WAAU;8CAA2D;;;;;;8CAG9E,6LAAC;oCAAI,WAAU;;yCACZ,oBAAA,IAAI,CAAC,KAAK,CAAC,KAAK,cAAhB,wCAAA,kBAAkB,KAAK;wCACvB;yCACA,wBAAA,IAAI,CAAC,KAAK,CAAC,SAAS,cAApB,4CAAA,sBAAsB,cAAc;;;;;;;;;;;;;;;;;;;;;;;;QAOnD;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;IAvFA,YAAY,KAAyB,CAAE;QACrC,KAAK,CAAC,QAmBR,+KAAA,SAAQ;YACN,IAAI,CAAC,QAAQ,CAAC;gBAAE,UAAU;gBAAO,OAAO;gBAAW,WAAW;YAAU;QAC1E;QApBE,IAAI,CAAC,KAAK,GAAG;YAAE,UAAU;QAAM;IACjC;AAqFF;AAGO,SAAS;;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAe;IAEvD,MAAM,aAAa,6JAAA,CAAA,UAAK,CAAC,WAAW;mDAAC;YACnC,SAAS;QACX;kDAAG,EAAE;IAEL,MAAM,cAAc,6JAAA,CAAA,UAAK,CAAC,WAAW;oDAAC,CAAC;YACrC,QAAQ,KAAK,CAAC,oCAAoC;YAClD,SAAS;QACX;mDAAG,EAAE;IAEL,6JAAA,CAAA,UAAK,CAAC,SAAS;qCAAC;YACd,IAAI,OAAO;gBACT,MAAM;YACR;QACF;oCAAG;QAAC;KAAM;IAEV,OAAO;QAAE;QAAa;IAAW;AACnC;GAnBgB;AAsBT,SAAS,kBACd,SAAiC,EACjC,QAAmE;IAEnE,MAAM,mBAAmB,CAAC,sBACxB,6LAAC;YAAc,UAAU;sBACvB,cAAA,6LAAC;gBAAW,GAAG,KAAK;;;;;;;;;;;IAIxB,iBAAiB,WAAW,GAAG,AAAC,qBAA4D,OAAxC,UAAU,WAAW,IAAI,UAAU,IAAI,EAAC;IAE5F,OAAO;AACT;uCAEe", "debugId": null}}, {"offset": {"line": 1676, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%287%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/components/ToastNotifications.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useNotifications } from '@/contexts/NotificationContext'\nimport {\n  X,\n  CheckCircle,\n  AlertTriangle,\n  Info,\n  XCircle,\n  ExternalLink\n} from 'lucide-react'\n\ninterface ToastNotification {\n  id: string\n  type: 'success' | 'warning' | 'error' | 'info'\n  title: string\n  message: string\n  actionUrl?: string\n  actionLabel?: string\n  duration?: number\n}\n\nexport default function ToastNotifications() {\n  const [toasts, setToasts] = useState<ToastNotification[]>([])\n  const { notifications } = useNotifications()\n\n  // مراقبة التنبيهات الجديدة وعرضها كـ Toast\n  useEffect(() => {\n    const latestNotification = notifications[0]\n    if (latestNotification && !latestNotification.isRead) {\n      // عرض التنبيه كـ Toast فقط إذا كان حديث (أقل من دقيقة)\n      const notificationTime = new Date(latestNotification.createdAt).getTime()\n      const now = new Date().getTime()\n      const diffInMinutes = (now - notificationTime) / (1000 * 60)\n      \n      if (diffInMinutes < 1) {\n        showToast({\n          id: latestNotification.id,\n          type: latestNotification.type,\n          title: latestNotification.title,\n          message: latestNotification.message,\n          actionUrl: latestNotification.actionUrl,\n          actionLabel: latestNotification.actionLabel,\n          duration: getDurationByPriority(latestNotification.priority)\n        })\n      }\n    }\n  }, [notifications])\n\n  const getDurationByPriority = (priority: string): number => {\n    switch (priority) {\n      case 'critical': return 10000 // 10 ثواني\n      case 'high': return 7000     // 7 ثواني\n      case 'medium': return 5000   // 5 ثواني\n      case 'low': return 3000      // 3 ثواني\n      default: return 5000\n    }\n  }\n\n  const showToast = (toast: ToastNotification) => {\n    // تجنب التكرار\n    if (toasts.find(t => t.id === toast.id)) return\n\n    setToasts(prev => [...prev, toast])\n\n    // إزالة التنبيه تلقائياً بعد المدة المحددة\n    setTimeout(() => {\n      removeToast(toast.id)\n    }, toast.duration || 5000)\n  }\n\n  const removeToast = (id: string) => {\n    setToasts(prev => prev.filter(toast => toast.id !== id))\n  }\n\n  const getToastIcon = (type: string) => {\n    switch (type) {\n      case 'success':\n        return <CheckCircle className=\"h-5 w-5 text-green-500\" />\n      case 'warning':\n        return <AlertTriangle className=\"h-5 w-5 text-yellow-500\" />\n      case 'error':\n        return <XCircle className=\"h-5 w-5 text-red-500\" />\n      case 'info':\n        return <Info className=\"h-5 w-5 text-blue-500\" />\n      default:\n        return <Info className=\"h-5 w-5 text-gray-500\" />\n    }\n  }\n\n  const getToastStyles = (type: string) => {\n    switch (type) {\n      case 'success':\n        return 'bg-green-50 border-green-200 text-green-800'\n      case 'warning':\n        return 'bg-yellow-50 border-yellow-200 text-yellow-800'\n      case 'error':\n        return 'bg-red-50 border-red-200 text-red-800'\n      case 'info':\n        return 'bg-blue-50 border-blue-200 text-blue-800'\n      default:\n        return 'bg-gray-50 border-gray-200 text-gray-800'\n    }\n  }\n\n  if (toasts.length === 0) return null\n\n  return (\n    <div className=\"fixed top-20 left-4 z-50 space-y-3\">\n      {toasts.map((toast) => (\n        <div\n          key={toast.id}\n          className={`max-w-sm w-full shadow-lg rounded-lg border p-4 transition-all duration-300 transform animate-slide-in-left ${getToastStyles(toast.type)}`}\n        >\n          <div className=\"flex items-start gap-3\">\n            {/* Icon */}\n            <div className=\"flex-shrink-0 mt-0.5\">\n              {getToastIcon(toast.type)}\n            </div>\n            \n            {/* Content */}\n            <div className=\"flex-1 min-w-0\">\n              <h4 className=\"text-sm font-medium mb-1\">\n                {toast.title}\n              </h4>\n              <p className=\"text-sm opacity-90 line-clamp-2\">\n                {toast.message}\n              </p>\n              \n              {/* Action Button */}\n              {toast.actionUrl && (\n                <a\n                  href={toast.actionUrl}\n                  className=\"inline-flex items-center gap-1 text-xs font-medium mt-2 hover:underline\"\n                >\n                  <ExternalLink className=\"h-3 w-3\" />\n                  {toast.actionLabel || 'عرض التفاصيل'}\n                </a>\n              )}\n            </div>\n            \n            {/* Close Button */}\n            <button\n              onClick={() => removeToast(toast.id)}\n              className=\"flex-shrink-0 p-1 hover:bg-black hover:bg-opacity-10 rounded\"\n            >\n              <X className=\"h-4 w-4\" />\n            </button>\n          </div>\n        </div>\n      ))}\n    </div>\n  )\n}\n\n// إضافة الأنيميشن إلى CSS\nconst toastStyles = `\n  @keyframes slide-in-left {\n    from {\n      transform: translateX(-100%);\n      opacity: 0;\n    }\n    to {\n      transform: translateX(0);\n      opacity: 1;\n    }\n  }\n  \n  .animate-slide-in-left {\n    animation: slide-in-left 0.3s ease-out;\n  }\n  \n  .line-clamp-2 {\n    display: -webkit-box;\n    -webkit-line-clamp: 2;\n    -webkit-box-orient: vertical;\n    overflow: hidden;\n  }\n`\n\n// إضافة الأنيميشن إلى الصفحة\nif (typeof document !== 'undefined') {\n  const styleElement = document.createElement('style')\n  styleElement.textContent = toastStyles\n  document.head.appendChild(styleElement)\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AAuBe,SAAS;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB,EAAE;IAC5D,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,mBAAgB,AAAD;IAEzC,2CAA2C;IAC3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,MAAM,qBAAqB,aAAa,CAAC,EAAE;YAC3C,IAAI,sBAAsB,CAAC,mBAAmB,MAAM,EAAE;gBACpD,uDAAuD;gBACvD,MAAM,mBAAmB,IAAI,KAAK,mBAAmB,SAAS,EAAE,OAAO;gBACvE,MAAM,MAAM,IAAI,OAAO,OAAO;gBAC9B,MAAM,gBAAgB,CAAC,MAAM,gBAAgB,IAAI,CAAC,OAAO,EAAE;gBAE3D,IAAI,gBAAgB,GAAG;oBACrB,UAAU;wBACR,IAAI,mBAAmB,EAAE;wBACzB,MAAM,mBAAmB,IAAI;wBAC7B,OAAO,mBAAmB,KAAK;wBAC/B,SAAS,mBAAmB,OAAO;wBACnC,WAAW,mBAAmB,SAAS;wBACvC,aAAa,mBAAmB,WAAW;wBAC3C,UAAU,sBAAsB,mBAAmB,QAAQ;oBAC7D;gBACF;YACF;QACF;uCAAG;QAAC;KAAc;IAElB,MAAM,wBAAwB,CAAC;QAC7B,OAAQ;YACN,KAAK;gBAAY,OAAO,MAAM,WAAW;;YACzC,KAAK;gBAAQ,OAAO,KAAS,UAAU;;YACvC,KAAK;gBAAU,OAAO,KAAO,UAAU;;YACvC,KAAK;gBAAO,OAAO,KAAU,UAAU;;YACvC;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,YAAY,CAAC;QACjB,eAAe;QACf,IAAI,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,MAAM,EAAE,GAAG;QAEzC,UAAU,CAAA,OAAQ;mBAAI;gBAAM;aAAM;QAElC,2CAA2C;QAC3C,WAAW;YACT,YAAY,MAAM,EAAE;QACtB,GAAG,MAAM,QAAQ,IAAI;IACvB;IAEA,MAAM,cAAc,CAAC;QACnB,UAAU,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IACtD;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC,KAAK;gBACH,qBAAO,6LAAC,+MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B,KAAK;gBACH,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB;gBACE,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,OAAO,MAAM,KAAK,GAAG,OAAO;IAEhC,qBACE,6LAAC;QAAI,WAAU;kBACZ,OAAO,GAAG,CAAC,CAAC,sBACX,6LAAC;gBAEC,WAAW,AAAC,+GAAyI,OAA3B,eAAe,MAAM,IAAI;0BAEnJ,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACZ,aAAa,MAAM,IAAI;;;;;;sCAI1B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CACX,MAAM,KAAK;;;;;;8CAEd,6LAAC;oCAAE,WAAU;8CACV,MAAM,OAAO;;;;;;gCAIf,MAAM,SAAS,kBACd,6LAAC;oCACC,MAAM,MAAM,SAAS;oCACrB,WAAU;;sDAEV,6LAAC,yNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCACvB,MAAM,WAAW,IAAI;;;;;;;;;;;;;sCAM5B,6LAAC;4BACC,SAAS,IAAM,YAAY,MAAM,EAAE;4BACnC,WAAU;sCAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;eAnCZ,MAAM,EAAE;;;;;;;;;;AA0CvB;GAnIwB;;QAEI,0IAAA,CAAA,mBAAgB;;;KAFpB;AAqIxB,0BAA0B;AAC1B,MAAM,cAAe;AAwBrB,6BAA6B;AAC7B,IAAI,OAAO,aAAa,aAAa;IACnC,MAAM,eAAe,SAAS,aAAa,CAAC;IAC5C,aAAa,WAAW,GAAG;IAC3B,SAAS,IAAI,CAAC,WAAW,CAAC;AAC5B", "debugId": null}}, {"offset": {"line": 1930, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%287%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/components/AppLayout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Sidebar from './Sidebar'\nimport Header from './Header'\nimport ErrorBoundary from './ErrorBoundary'\nimport ToastNotifications from './ToastNotifications'\n\ninterface AppLayoutProps {\n  children: React.ReactNode\n}\n\nexport default function AppLayout({ children }: AppLayoutProps) {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)\n\n  const toggleMobileMenu = () => {\n    setIsMobileMenuOpen(!isMobileMenuOpen)\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Sidebar isOpen={isMobileMenuOpen} onClose={() => setIsMobileMenuOpen(false)} />\n      <Header\n        onMobileMenuToggle={toggleMobileMenu}\n        isMobileMenuOpen={isMobileMenuOpen}\n      />\n      <main className=\"mr-0 md:mr-64 mt-16 p-3 md:p-6\">\n        <ErrorBoundary>\n          <div className=\"max-w-full overflow-x-auto\">\n            {children}\n          </div>\n        </ErrorBoundary>\n      </main>\n      <ToastNotifications />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAYe,SAAS,UAAU,KAA4B;QAA5B,EAAE,QAAQ,EAAkB,GAA5B;;IAChC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,mBAAmB;QACvB,oBAAoB,CAAC;IACvB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,gIAAA,CAAA,UAAO;gBAAC,QAAQ;gBAAkB,SAAS,IAAM,oBAAoB;;;;;;0BACtE,6LAAC,+HAAA,CAAA,UAAM;gBACL,oBAAoB;gBACpB,kBAAkB;;;;;;0BAEpB,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,sIAAA,CAAA,UAAa;8BACZ,cAAA,6LAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;0BAIP,6LAAC,2IAAA,CAAA,UAAkB;;;;;;;;;;;AAGzB;GAxBwB;KAAA", "debugId": null}}, {"offset": {"line": 2019, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%287%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// Database Types\nexport interface Medicine {\n  id: string\n  name: string\n  category: string\n  manufacturer: string\n  active_ingredient: string\n  strength: string\n  form: string // tablet, capsule, syrup, etc.\n  unit_price: number\n  selling_price: number\n  created_at: string\n  updated_at: string\n}\n\nexport interface MedicineBatch {\n  id: string\n  medicine_id: string\n  batch_code: string\n  expiry_date: string\n  quantity: number\n  cost_price: number\n  selling_price: number\n  supplier_id: string\n  received_date: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface Customer {\n  id: string\n  name: string\n  phone?: string\n  email?: string\n  address?: string\n  notes?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface Supplier {\n  id: string\n  name: string\n  contact_person?: string\n  phone?: string\n  email?: string\n  address?: string\n  notes?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface SalesInvoice {\n  id: string\n  invoice_number: string\n  customer_id?: string\n  customer_name?: string\n  total_amount: number\n  discount_amount: number\n  final_amount: number\n  payment_method: string\n  notes?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface SalesInvoiceItem {\n  id: string\n  invoice_id: string\n  medicine_batch_id: string\n  quantity: number\n  unit_price: number\n  total_price: number\n  is_gift: boolean\n  created_at: string\n}\n\nexport interface PurchaseInvoice {\n  id: string\n  invoice_number: string\n  supplier_id: string\n  total_amount: number\n  discount_amount: number\n  final_amount: number\n  payment_method: string\n  notes?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface PurchaseInvoiceItem {\n  id: string\n  invoice_id: string\n  medicine_id: string\n  batch_code: string\n  quantity: number\n  unit_cost: number\n  total_cost: number\n  expiry_date: string\n  created_at: string\n}\n\nexport interface SalesReturn {\n  id: string\n  original_invoice_id: string\n  return_number: string\n  total_amount: number\n  reason: string\n  notes?: string\n  created_at: string\n}\n\nexport interface PurchaseReturn {\n  id: string\n  original_invoice_id: string\n  return_number: string\n  total_amount: number\n  reason: string\n  notes?: string\n  created_at: string\n}\n\nexport interface InventoryMovement {\n  id: string\n  medicine_batch_id: string\n  movement_type: 'in' | 'out' | 'adjustment'\n  quantity: number\n  reference_type: 'sale' | 'purchase' | 'return' | 'adjustment'\n  reference_id?: string\n  notes?: string\n  created_at: string\n}\n"], "names": [], "mappings": ";;;AAEoB;AAFpB;;AAEA,MAAM;AACN,MAAM;AAEC,MAAM,WAAW,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAa", "debugId": null}}, {"offset": {"line": 2037, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%287%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/lib/database.ts"], "sourcesContent": ["import { supabase } from './supabase'\n\n// Medicine operations\nexport const addMedicine = async (medicineData: {\n  name: string\n  category: string\n  manufacturer?: string\n  active_ingredient?: string\n  strength?: string\n  form: string\n  unit_price: number\n  selling_price: number\n}) => {\n  try {\n    const { data, error } = await supabase\n      .from('medicines')\n      .insert([{\n        name: medicineData.name,\n        category: medicineData.category,\n        manufacturer: medicineData.manufacturer || '',\n        active_ingredient: medicineData.active_ingredient || '',\n        strength: medicineData.strength || '',\n        form: medicineData.form,\n        unit_price: medicineData.unit_price,\n        selling_price: medicineData.selling_price\n      }])\n      .select()\n      .single()\n\n    if (error) throw error\n    return { success: true, data }\n  } catch (error) {\n    console.error('Error adding medicine:', error)\n    return { success: false, error }\n  }\n}\n\nexport const getMedicines = async () => {\n  try {\n    const { data, error } = await supabase\n      .from('medicines')\n      .select(`\n        *,\n        medicine_batches (\n          id,\n          batch_code,\n          expiry_date,\n          quantity,\n          cost_price,\n          selling_price,\n          supplier_id,\n          received_date\n        )\n      `)\n      .order('name')\n\n    if (error) {\n      console.warn('Supabase error fetching medicines, using localStorage:', error)\n      // Fallback to localStorage\n      return getMedicinesFromLocalStorage()\n    }\n\n    return { success: true, data }\n  } catch (error) {\n    console.error('Error fetching medicines:', error)\n    // Final fallback to localStorage\n    return getMedicinesFromLocalStorage()\n  }\n}\n\n// Helper function to get medicines from localStorage\nconst getMedicinesFromLocalStorage = () => {\n  try {\n    const medicines = JSON.parse(localStorage.getItem('medicines') || '[]')\n    const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')\n\n    // If no medicines in localStorage, create sample data\n    if (medicines.length === 0) {\n      console.log('🔄 لا توجد أدوية في localStorage، إنشاء بيانات تجريبية...')\n      return createSampleMedicinesData()\n    }\n\n    // Combine medicines with their batches\n    const medicinesWithBatches = medicines.map((medicine: any) => ({\n      ...medicine,\n      medicine_batches: batches.filter((batch: any) => batch.medicine_id === medicine.id),\n      batches: batches.filter((batch: any) => batch.medicine_id === medicine.id)\n    }))\n\n    console.log(`✅ تم تحميل ${medicinesWithBatches.length} دواء من localStorage`)\n    return { success: true, data: medicinesWithBatches }\n  } catch (error) {\n    console.error('Error loading medicines from localStorage:', error)\n    return { success: false, error }\n  }\n}\n\n// Helper function to create sample medicines data\nconst createSampleMedicinesData = () => {\n  try {\n    const sampleMedicines = [\n      {\n        id: 'med_1',\n        name: 'باراسيتامول 500 مجم',\n        category: 'مسكنات',\n        manufacturer: 'شركة الأدوية العراقية',\n        strength: '500mg',\n        form: 'أقراص',\n        created_at: new Date().toISOString()\n      },\n      {\n        id: 'med_2',\n        name: 'أموكسيسيلين 250 مجم',\n        category: 'مضادات حيوية',\n        manufacturer: 'شركة بغداد للأدوية',\n        strength: '250mg',\n        form: 'كبسولات',\n        created_at: new Date().toISOString()\n      },\n      {\n        id: 'med_3',\n        name: 'أسبرين 100 مجم',\n        category: 'مسكنات',\n        manufacturer: 'شركة النهرين',\n        strength: '100mg',\n        form: 'أقراص',\n        created_at: new Date().toISOString()\n      },\n      {\n        id: 'med_4',\n        name: 'إيبوبروفين 400 مجم',\n        category: 'مسكنات',\n        manufacturer: 'شركة الرافدين',\n        strength: '400mg',\n        form: 'أقراص',\n        created_at: new Date().toISOString()\n      },\n      {\n        id: 'med_5',\n        name: 'أوميبرازول 20 مجم',\n        category: 'أدوية المعدة',\n        manufacturer: 'شركة دجلة',\n        strength: '20mg',\n        form: 'كبسولات',\n        created_at: new Date().toISOString()\n      }\n    ]\n\n    const sampleBatches = [\n      {\n        id: 'batch_1',\n        medicine_id: 'med_1',\n        batch_code: 'PAR001',\n        expiry_date: '2025-12-31',\n        quantity: 100,\n        cost_price: 500,\n        selling_price: 750,\n        received_date: '2024-01-01',\n        created_at: new Date().toISOString()\n      },\n      {\n        id: 'batch_2',\n        medicine_id: 'med_2',\n        batch_code: 'AMX001',\n        expiry_date: '2025-06-30',\n        quantity: 50,\n        cost_price: 1000,\n        selling_price: 1500,\n        received_date: '2024-01-01',\n        created_at: new Date().toISOString()\n      },\n      {\n        id: 'batch_3',\n        medicine_id: 'med_3',\n        batch_code: 'ASP001',\n        expiry_date: '2026-03-31',\n        quantity: 200,\n        cost_price: 300,\n        selling_price: 500,\n        received_date: '2024-01-01',\n        created_at: new Date().toISOString()\n      },\n      {\n        id: 'batch_4',\n        medicine_id: 'med_4',\n        batch_code: 'IBU001',\n        expiry_date: '2025-09-30',\n        quantity: 75,\n        cost_price: 800,\n        selling_price: 1200,\n        received_date: '2024-01-01',\n        created_at: new Date().toISOString()\n      },\n      {\n        id: 'batch_5',\n        medicine_id: 'med_5',\n        batch_code: 'OME001',\n        expiry_date: '2025-11-30',\n        quantity: 30,\n        cost_price: 1500,\n        selling_price: 2000,\n        received_date: '2024-01-01',\n        created_at: new Date().toISOString()\n      }\n    ]\n\n    // Create sample customers\n    const sampleCustomers = [\n      {\n        id: 'cust_1',\n        name: 'أحمد محمد علي',\n        phone: '07701234567',\n        address: 'بغداد - الكرادة',\n        created_at: new Date().toISOString()\n      },\n      {\n        id: 'cust_2',\n        name: 'فاطمة حسن',\n        phone: '07809876543',\n        address: 'بغداد - الجادرية',\n        created_at: new Date().toISOString()\n      }\n    ]\n\n    // Save to localStorage\n    localStorage.setItem('medicines', JSON.stringify(sampleMedicines))\n    localStorage.setItem('medicine_batches', JSON.stringify(sampleBatches))\n    localStorage.setItem('customers', JSON.stringify(sampleCustomers))\n\n    // Initialize empty arrays for invoices\n    localStorage.setItem('sales_invoices', JSON.stringify([]))\n    localStorage.setItem('sales_invoice_items', JSON.stringify([]))\n\n    // Combine medicines with their batches\n    const medicinesWithBatches = sampleMedicines.map((medicine: any) => ({\n      ...medicine,\n      medicine_batches: sampleBatches.filter((batch: any) => batch.medicine_id === medicine.id),\n      batches: sampleBatches.filter((batch: any) => batch.medicine_id === medicine.id)\n    }))\n\n    console.log(`✅ تم إنشاء ${medicinesWithBatches.length} دواء تجريبي`)\n    console.log(`✅ تم إنشاء ${sampleBatches.length} دفعة تجريبية`)\n    console.log(`✅ تم إنشاء ${sampleCustomers.length} عميل تجريبي`)\n    return { success: true, data: medicinesWithBatches }\n  } catch (error) {\n    console.error('Error creating sample medicines:', error)\n    return { success: false, error }\n  }\n}\n\n// Function to initialize system data\nexport const initializeSystemData = async () => {\n  try {\n    console.log('🔄 تهيئة بيانات النظام...')\n\n    // Check if we have basic data\n    const medicines = JSON.parse(localStorage.getItem('medicines') || '[]')\n    const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')\n\n    if (medicines.length === 0 || batches.length === 0) {\n      console.log('📦 إنشاء البيانات الأساسية...')\n      return createSampleMedicinesData()\n    }\n\n    console.log(`✅ البيانات الأساسية موجودة: ${medicines.length} دواء، ${batches.length} دفعة`)\n    return { success: true, data: medicines }\n  } catch (error) {\n    console.error('Error initializing system data:', error)\n    return { success: false, error }\n  }\n}\n\n// Medicine batch operations\nexport const addMedicineBatch = async (batchData: {\n  medicine_id: string\n  batch_code: string\n  expiry_date: string\n  quantity: number\n  cost_price: number\n  selling_price: number\n  supplier_id?: string\n}) => {\n  try {\n    const { data, error } = await supabase\n      .from('medicine_batches')\n      .insert([batchData])\n      .select()\n      .single()\n\n    if (error) throw error\n    return { success: true, data }\n  } catch (error) {\n    console.error('Error adding medicine batch:', error)\n    return { success: false, error }\n  }\n}\n\nexport const updateBatchQuantity = async (batchId: string, newQuantity: number) => {\n  try {\n    const { data, error } = await supabase\n      .from('medicine_batches')\n      .update({ quantity: newQuantity })\n      .eq('id', batchId)\n      .select()\n      .single()\n\n    if (error) {\n      console.warn('Supabase error updating batch quantity, using localStorage:', error)\n      // Fallback to localStorage\n      const existingBatches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')\n      const batchIndex = existingBatches.findIndex((batch: any) => batch.id === batchId)\n\n      if (batchIndex !== -1) {\n        existingBatches[batchIndex].quantity = newQuantity\n        localStorage.setItem('medicine_batches', JSON.stringify(existingBatches))\n        return { success: true, data: existingBatches[batchIndex] }\n      }\n\n      return { success: false, error: 'Batch not found in localStorage' }\n    }\n\n    return { success: true, data }\n  } catch (error) {\n    console.error('Error updating batch quantity:', error)\n\n    // Final fallback to localStorage\n    try {\n      const existingBatches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')\n      const batchIndex = existingBatches.findIndex((batch: any) => batch.id === batchId)\n\n      if (batchIndex !== -1) {\n        existingBatches[batchIndex].quantity = newQuantity\n        localStorage.setItem('medicine_batches', JSON.stringify(existingBatches))\n        return { success: true, data: existingBatches[batchIndex] }\n      }\n\n      return { success: false, error: 'Batch not found' }\n    } catch (localError) {\n      console.error('LocalStorage fallback failed for batch update:', localError)\n      return { success: false, error: localError }\n    }\n  }\n}\n\n// Sales operations\nexport const createSalesInvoice = async (invoiceData: {\n  invoice_number: string\n  customer_id?: string\n  customer_name?: string\n  total_amount: number\n  discount_amount: number\n  final_amount: number\n  payment_method: string\n  payment_status: string\n  notes?: string\n  private_notes?: string\n}) => {\n  try {\n    // Try Supabase first\n    const { data, error } = await supabase\n      .from('sales_invoices')\n      .insert([invoiceData])\n      .select()\n      .single()\n\n    if (error) {\n      console.warn('Supabase error, using localStorage:', error)\n      // Fallback to localStorage\n      const invoiceId = `invoice_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n      const invoice = {\n        id: invoiceId,\n        ...invoiceData,\n        created_at: new Date().toISOString()\n      }\n\n      // Save to localStorage\n      const existingInvoices = JSON.parse(localStorage.getItem('sales_invoices') || '[]')\n      existingInvoices.push(invoice)\n      localStorage.setItem('sales_invoices', JSON.stringify(existingInvoices))\n\n      return { success: true, data: invoice }\n    }\n\n    return { success: true, data }\n  } catch (error) {\n    console.error('Error creating sales invoice:', error)\n\n    // Final fallback to localStorage\n    try {\n      const invoiceId = `invoice_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n      const invoice = {\n        id: invoiceId,\n        ...invoiceData,\n        created_at: new Date().toISOString()\n      }\n\n      const existingInvoices = JSON.parse(localStorage.getItem('sales_invoices') || '[]')\n      existingInvoices.push(invoice)\n      localStorage.setItem('sales_invoices', JSON.stringify(existingInvoices))\n\n      return { success: true, data: invoice }\n    } catch (localError) {\n      console.error('LocalStorage fallback failed:', localError)\n      return { success: false, error: localError }\n    }\n  }\n}\n\nexport const addSalesInvoiceItems = async (items: Array<{\n  invoice_id: string\n  medicine_batch_id: string\n  quantity: number\n  unit_price: number\n  total_price: number\n  is_gift: boolean\n  medicine_name?: string\n}>) => {\n  try {\n    const { data, error } = await supabase\n      .from('sales_invoice_items')\n      .insert(items)\n      .select()\n\n    if (error) {\n      console.warn('Supabase error for invoice items, using localStorage:', error)\n      // Fallback to localStorage - preserve existing medicine names\n      console.log('📦 العناصر الواردة للحفظ:', items)\n\n      const enhancedItems = items.map(item => {\n        // Use existing medicine name if available, otherwise enhance\n        const medicineName = item.medicine_name || item.medicineName\n\n        if (medicineName && medicineName !== 'غير محدد') {\n          console.log(`✅ استخدام اسم الدواء الموجود: ${medicineName}`)\n          return {\n            id: `item_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n            ...item,\n            medicine_name: medicineName,\n            medicineName: medicineName,\n            medicine_batches: {\n              batch_code: '',\n              expiry_date: '',\n              medicines: {\n                name: medicineName,\n                category: '',\n                manufacturer: '',\n                strength: '',\n                form: ''\n              }\n            },\n            created_at: new Date().toISOString()\n          }\n        } else {\n          console.log(`⚠️ لا يوجد اسم دواء، سيتم البحث عنه...`)\n          // Only enhance if no medicine name is available\n          return item\n        }\n      })\n\n      // Enhance items that still need medicine names\n      const itemsNeedingEnhancement = enhancedItems.filter(item =>\n        !item.medicine_name || item.medicine_name === 'غير محدد'\n      )\n\n      let finalItems = enhancedItems\n      if (itemsNeedingEnhancement.length > 0) {\n        console.log(`🔍 تحسين ${itemsNeedingEnhancement.length} عنصر يحتاج أسماء أدوية`)\n        const enhancedNeeded = await enhanceItemsWithMedicineNames(itemsNeedingEnhancement)\n\n        // Replace items that needed enhancement\n        finalItems = enhancedItems.map(item => {\n          if (!item.medicine_name || item.medicine_name === 'غير محدد') {\n            const enhanced = enhancedNeeded.find(e => e.medicine_batch_id === item.medicine_batch_id)\n            return enhanced || item\n          }\n          return item\n        })\n      }\n\n      const existingItems = JSON.parse(localStorage.getItem('sales_invoice_items') || '[]')\n      existingItems.push(...finalItems)\n      localStorage.setItem('sales_invoice_items', JSON.stringify(existingItems))\n\n      console.log('✅ تم حفظ العناصر في localStorage:', finalItems)\n      return { success: true, data: finalItems }\n    }\n\n    return { success: true, data }\n  } catch (error) {\n    console.error('Error adding sales invoice items:', error)\n\n    // Final fallback to localStorage\n    try {\n      console.log('🔄 Final fallback - حفظ العناصر مع الأسماء الموجودة')\n\n      const enhancedItems = items.map(item => {\n        const medicineName = item.medicine_name || item.medicineName || 'غير محدد'\n\n        return {\n          id: `item_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n          ...item,\n          medicine_name: medicineName,\n          medicineName: medicineName,\n          medicine_batches: {\n            batch_code: '',\n            expiry_date: '',\n            medicines: {\n              name: medicineName,\n              category: '',\n              manufacturer: '',\n              strength: '',\n              form: ''\n            }\n          },\n          created_at: new Date().toISOString()\n        }\n      })\n\n      const existingItems = JSON.parse(localStorage.getItem('sales_invoice_items') || '[]')\n      existingItems.push(...enhancedItems)\n      localStorage.setItem('sales_invoice_items', JSON.stringify(existingItems))\n\n      console.log('✅ Final fallback - تم حفظ العناصر:', enhancedItems)\n      return { success: true, data: enhancedItems }\n    } catch (localError) {\n      console.error('LocalStorage fallback failed for items:', localError)\n      return { success: false, error: localError }\n    }\n  }\n}\n\n// Helper function to enhance items with medicine names\nconst enhanceItemsWithMedicineNames = async (items: any[]) => {\n  try {\n    // Get medicine data for names\n    const medicines = JSON.parse(localStorage.getItem('medicines') || '[]')\n    const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')\n\n    return items.map(item => {\n      // Use existing medicine name if available, otherwise find from batch\n      let medicineName = item.medicine_name || item.medicineName\n\n      if (!medicineName || medicineName === 'غير محدد') {\n        // Find medicine name from batch\n        const batch = batches.find((b: any) => b.id === item.medicine_batch_id)\n        const medicine = medicines.find((m: any) => m.id === batch?.medicine_id)\n        medicineName = medicine?.name || 'غير محدد'\n      }\n\n      const batch = batches.find((b: any) => b.id === item.medicine_batch_id)\n      const medicine = medicines.find((m: any) => m.id === batch?.medicine_id)\n\n      return {\n        id: `item_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n        ...item,\n        medicine_name: medicineName,\n        medicineName: medicineName, // Add both for compatibility\n        medicine_batches: {\n          batch_code: batch?.batch_code || '',\n          expiry_date: batch?.expiry_date || '',\n          medicines: {\n            name: medicineName,\n            category: medicine?.category || '',\n            manufacturer: medicine?.manufacturer || '',\n            strength: medicine?.strength || '',\n            form: medicine?.form || ''\n          }\n        },\n        created_at: new Date().toISOString()\n      }\n    })\n  } catch (error) {\n    console.error('خطأ في تحسين العناصر بأسماء الأدوية:', error)\n    // Return items with basic structure if enhancement fails\n    return items.map(item => ({\n      id: `item_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n      ...item,\n      medicine_name: 'غير محدد',\n      created_at: new Date().toISOString()\n    }))\n  }\n}\n\n// Function to fix existing localStorage data with medicine names\nexport const fixLocalStorageInvoiceItems = () => {\n  try {\n    console.log('🔧 بدء إصلاح بيانات الفواتير في localStorage...')\n\n    const salesItems = JSON.parse(localStorage.getItem('sales_invoice_items') || '[]')\n    const medicines = JSON.parse(localStorage.getItem('medicines') || '[]')\n    const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')\n\n    console.log(`📦 عدد عناصر الفواتير: ${salesItems.length}`)\n    console.log(`💊 عدد الأدوية: ${medicines.length}`)\n    console.log(`📋 عدد الدفعات: ${batches.length}`)\n\n    let fixedCount = 0\n    let notFoundCount = 0\n\n    const fixedItems = salesItems.map((item: any) => {\n      // Skip if already has proper medicine name structure\n      if (item.medicine_batches?.medicines?.name && item.medicine_batches.medicines.name !== 'غير محدد') {\n        return item\n      }\n\n      // Find medicine name from batch\n      const batch = batches.find((b: any) => b.id === item.medicine_batch_id)\n      const medicine = medicines.find((m: any) => m.id === batch?.medicine_id)\n\n      if (medicine?.name) {\n        fixedCount++\n        console.log(`✅ إصلاح العنصر: ${medicine.name} (Batch: ${batch?.batch_code})`)\n\n        return {\n          ...item,\n          medicine_name: medicine.name,\n          medicineName: medicine.name, // Add both for compatibility\n          medicine_batches: {\n            batch_code: batch?.batch_code || item.batch_code || '',\n            expiry_date: batch?.expiry_date || item.expiry_date || '',\n            medicines: {\n              name: medicine.name,\n              category: medicine.category || '',\n              manufacturer: medicine.manufacturer || '',\n              strength: medicine.strength || '',\n              form: medicine.form || ''\n            }\n          }\n        }\n      } else {\n        notFoundCount++\n        console.log(`⚠️ لم يتم العثور على الدواء للعنصر: ${item.medicine_batch_id}`)\n\n        // Try to preserve any existing name\n        const existingName = item.medicine_name || item.medicineName || 'غير محدد'\n        return {\n          ...item,\n          medicine_name: existingName,\n          medicineName: existingName,\n          medicine_batches: {\n            batch_code: batch?.batch_code || item.batch_code || '',\n            expiry_date: batch?.expiry_date || item.expiry_date || '',\n            medicines: {\n              name: existingName,\n              category: '',\n              manufacturer: '',\n              strength: '',\n              form: ''\n            }\n          }\n        }\n      }\n    })\n\n    // Save fixed data back to localStorage\n    localStorage.setItem('sales_invoice_items', JSON.stringify(fixedItems))\n\n    console.log(`✅ تم إصلاح ${fixedCount} عنصر من أصل ${salesItems.length}`)\n    console.log(`⚠️ لم يتم العثور على ${notFoundCount} عنصر`)\n\n    return {\n      success: true,\n      fixedCount,\n      notFoundCount,\n      totalCount: salesItems.length\n    }\n\n  } catch (error) {\n    console.error('❌ خطأ في إصلاح بيانات localStorage:', error)\n    return { success: false, error }\n  }\n}\n\n// Helper function to get medicine name from batch ID\nexport const getMedicineNameFromBatch = (batchId: string): string => {\n  try {\n    const medicines = JSON.parse(localStorage.getItem('medicines') || '[]')\n    const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')\n\n    const batch = batches.find((b: any) => b.id === batchId)\n    const medicine = medicines.find((m: any) => m.id === batch?.medicine_id)\n\n    return medicine?.name || 'غير محدد'\n  } catch (error) {\n    console.error('خطأ في الحصول على اسم الدواء:', error)\n    return 'غير محدد'\n  }\n}\n\n// Purchase operations\nexport const createPurchaseInvoice = async (invoiceData: {\n  invoice_number: string\n  supplier_id: string\n  total_amount: number\n  discount_amount: number\n  final_amount: number\n  payment_method: string\n  payment_status: string\n  notes?: string\n  private_notes?: string\n}) => {\n  try {\n    // Try Supabase first\n    const { data, error } = await supabase\n      .from('purchase_invoices')\n      .insert([invoiceData])\n      .select()\n      .single()\n\n    if (error) {\n      console.warn('Supabase error for purchase invoice, using localStorage:', error)\n      // Fallback to localStorage\n      const invoiceId = `purchase_invoice_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n      const invoice = {\n        id: invoiceId,\n        ...invoiceData,\n        created_at: new Date().toISOString()\n      }\n\n      // Save to localStorage\n      const existingInvoices = JSON.parse(localStorage.getItem('purchase_invoices') || '[]')\n      existingInvoices.push(invoice)\n      localStorage.setItem('purchase_invoices', JSON.stringify(existingInvoices))\n\n      return { success: true, data: invoice }\n    }\n\n    return { success: true, data }\n  } catch (error) {\n    console.error('Error creating purchase invoice:', error)\n\n    // Final fallback to localStorage\n    try {\n      const invoiceId = `purchase_invoice_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n      const invoice = {\n        id: invoiceId,\n        ...invoiceData,\n        created_at: new Date().toISOString()\n      }\n\n      const existingInvoices = JSON.parse(localStorage.getItem('purchase_invoices') || '[]')\n      existingInvoices.push(invoice)\n      localStorage.setItem('purchase_invoices', JSON.stringify(existingInvoices))\n\n      return { success: true, data: invoice }\n    } catch (localError) {\n      console.error('LocalStorage fallback failed for purchase invoice:', localError)\n      return { success: false, error: localError }\n    }\n  }\n}\n\nexport const addPurchaseInvoiceItems = async (items: Array<{\n  invoice_id: string\n  medicine_id: string\n  batch_code: string\n  quantity: number\n  unit_cost: number\n  total_cost: number\n  expiry_date: string\n  medicine_name?: string\n}>) => {\n  try {\n    const { data, error } = await supabase\n      .from('purchase_invoice_items')\n      .insert(items)\n      .select()\n\n    if (error) {\n      console.warn('Supabase error for purchase invoice items, using localStorage:', error)\n      // Fallback to localStorage\n      const existingItems = JSON.parse(localStorage.getItem('purchase_invoice_items') || '[]')\n      const newItems = items.map(item => ({\n        id: `purchase_item_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n        ...item,\n        medicine_name: item.medicine_name || 'غير محدد',\n        medicineName: item.medicine_name || 'غير محدد',\n        created_at: new Date().toISOString()\n      }))\n\n      existingItems.push(...newItems)\n      localStorage.setItem('purchase_invoice_items', JSON.stringify(existingItems))\n\n      return { success: true, data: newItems }\n    }\n\n    return { success: true, data }\n  } catch (error) {\n    console.error('Error adding purchase invoice items:', error)\n\n    // Final fallback to localStorage\n    try {\n      const existingItems = JSON.parse(localStorage.getItem('purchase_invoice_items') || '[]')\n      const newItems = items.map(item => ({\n        id: `purchase_item_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n        ...item,\n        medicine_name: item.medicine_name || 'غير محدد',\n        medicineName: item.medicine_name || 'غير محدد',\n        created_at: new Date().toISOString()\n      }))\n\n      existingItems.push(...newItems)\n      localStorage.setItem('purchase_invoice_items', JSON.stringify(existingItems))\n\n      return { success: true, data: newItems }\n    } catch (localError) {\n      console.error('LocalStorage fallback failed for purchase items:', localError)\n      return { success: false, error: localError }\n    }\n  }\n}\n\n// Inventory movement operations\nexport const addInventoryMovement = async (movementData: {\n  medicine_batch_id: string\n  movement_type: 'in' | 'out' | 'adjustment'\n  quantity: number\n  reference_type: 'sale' | 'purchase' | 'return' | 'adjustment'\n  reference_id?: string\n  notes?: string\n}) => {\n  try {\n    const { data, error } = await supabase\n      .from('inventory_movements')\n      .insert([movementData])\n      .select()\n      .single()\n\n    if (error) {\n      console.warn('Supabase error for inventory movement, using localStorage:', error)\n      // Fallback to localStorage\n      const movement = {\n        id: `movement_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n        ...movementData,\n        created_at: new Date().toISOString()\n      }\n\n      const existingMovements = JSON.parse(localStorage.getItem('inventory_movements') || '[]')\n      existingMovements.push(movement)\n      localStorage.setItem('inventory_movements', JSON.stringify(existingMovements))\n\n      return { success: true, data: movement }\n    }\n\n    return { success: true, data }\n  } catch (error) {\n    console.error('Error adding inventory movement:', error)\n\n    // Final fallback to localStorage\n    try {\n      const movement = {\n        id: `movement_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n        ...movementData,\n        created_at: new Date().toISOString()\n      }\n\n      const existingMovements = JSON.parse(localStorage.getItem('inventory_movements') || '[]')\n      existingMovements.push(movement)\n      localStorage.setItem('inventory_movements', JSON.stringify(existingMovements))\n\n      return { success: true, data: movement }\n    } catch (localError) {\n      console.error('LocalStorage fallback failed for inventory movement:', localError)\n      return { success: false, error: localError }\n    }\n  }\n}\n\n// Customer operations\nexport const getCustomers = async () => {\n  try {\n    const { data, error } = await supabase\n      .from('customers')\n      .select('*')\n      .order('name')\n\n    if (error) throw error\n    return { success: true, data }\n  } catch (error) {\n    console.error('Error fetching customers:', error)\n    return { success: false, error }\n  }\n}\n\nexport const addCustomer = async (customerData: {\n  name: string\n  phone?: string\n  email?: string\n  address?: string\n  notes?: string\n}) => {\n  try {\n    const { data, error } = await supabase\n      .from('customers')\n      .insert([customerData])\n      .select()\n      .single()\n\n    if (error) throw error\n    return { success: true, data }\n  } catch (error) {\n    console.error('Error adding customer:', error)\n    return { success: false, error }\n  }\n}\n\n// Supplier operations\nexport const getSuppliers = async () => {\n  try {\n    const { data, error } = await supabase\n      .from('suppliers')\n      .select('*')\n      .order('name')\n\n    if (error) throw error\n    return { success: true, data }\n  } catch (error) {\n    console.error('Error fetching suppliers:', error)\n    return { success: false, error }\n  }\n}\n\nexport const addSupplier = async (supplierData: {\n  name: string\n  contact_person?: string\n  phone?: string\n  email?: string\n  address?: string\n  notes?: string\n}) => {\n  try {\n    const { data, error } = await supabase\n      .from('suppliers')\n      .insert([supplierData])\n      .select()\n      .single()\n\n    if (error) throw error\n    return { success: true, data }\n  } catch (error) {\n    console.error('Error adding supplier:', error)\n    return { success: false, error }\n  }\n}\n\n// Complete sales transaction with inventory update\nexport const completeSalesTransaction = async (\n  invoiceData: any,\n  items: any[]\n) => {\n  try {\n    console.log('🔄 بدء معاملة المبيعات الكاملة...')\n    console.log('📄 بيانات الفاتورة:', invoiceData)\n    console.log('📦 العناصر:', items)\n\n    // Start transaction by creating invoice\n    console.log('📝 إنشاء الفاتورة...')\n    const invoiceResult = await createSalesInvoice(invoiceData)\n    console.log('📝 نتيجة إنشاء الفاتورة:', invoiceResult)\n\n    if (!invoiceResult.success) {\n      console.error('❌ فشل في إنشاء الفاتورة:', invoiceResult.error)\n      throw new Error(`فشل في إنشاء الفاتورة: ${invoiceResult.error?.message || 'خطأ غير معروف'}`)\n    }\n\n    const invoiceId = invoiceResult.data.id\n    console.log('✅ تم إنشاء الفاتورة بنجاح، ID:', invoiceId)\n\n    // Process each item\n    console.log('📦 معالجة عناصر الفاتورة...')\n    const itemsToAdd = []\n\n    for (const item of items) {\n      console.log('📦 معالجة العنصر:', item)\n      const batchId = item.medicine_batch_id || item.batchId\n\n      // Prepare item for batch insert with medicine name\n      itemsToAdd.push({\n        invoice_id: invoiceId,\n        medicine_batch_id: batchId,\n        quantity: item.quantity,\n        unit_price: item.unit_price || item.unitPrice,\n        total_price: item.total_price || item.totalPrice,\n        is_gift: item.is_gift || item.isGift || false,\n        medicine_name: item.medicine_name || item.medicineName || 'غير محدد'\n      })\n\n      // Update batch quantity (only for non-gift items)\n      if (!(item.is_gift || item.isGift)) {\n        try {\n          const currentBatch = await supabase\n            .from('medicine_batches')\n            .select('quantity')\n            .eq('id', batchId)\n            .single()\n\n          if (currentBatch.data) {\n            const newQuantity = Math.max(0, currentBatch.data.quantity - item.quantity)\n            await updateBatchQuantity(batchId, newQuantity)\n            console.log(`✅ تم تحديث كمية الدفعة ${batchId} إلى ${newQuantity}`)\n          }\n        } catch (batchError) {\n          console.warn('تحذير: فشل في تحديث كمية الدفعة:', batchError)\n        }\n      }\n\n      // Add inventory movement\n      try {\n        await addInventoryMovement({\n          medicine_batch_id: batchId,\n          movement_type: 'out',\n          quantity: item.quantity,\n          reference_type: 'sale',\n          reference_id: invoiceId,\n          notes: (item.is_gift || item.isGift) ? 'هدية' : undefined\n        })\n        console.log(`✅ تم إضافة حركة المخزون للدفعة ${batchId}`)\n      } catch (movementError) {\n        console.warn('تحذير: فشل في إضافة حركة المخزون:', movementError)\n      }\n    }\n\n    // Add all invoice items in batch\n    console.log('📝 إضافة عناصر الفاتورة...')\n    const itemsResult = await addSalesInvoiceItems(itemsToAdd)\n    if (!itemsResult.success) {\n      console.warn('تحذير: فشل في إضافة عناصر الفاتورة:', itemsResult.error)\n    } else {\n      console.log('✅ تم إضافة جميع عناصر الفاتورة بنجاح')\n    }\n\n    // Add cash transaction if payment is cash\n    if (invoiceData.payment_method === 'cash' && invoiceData.payment_status === 'paid') {\n      try {\n        await addCashTransaction({\n          transaction_type: 'income',\n          category: 'مبيعات',\n          amount: invoiceData.final_amount,\n          description: `فاتورة مبيعات رقم ${invoiceData.invoice_number}`,\n          reference_type: 'sale',\n          reference_id: invoiceId,\n          payment_method: 'cash',\n          notes: invoiceData.notes\n        })\n        console.log('✅ تم إضافة معاملة الصندوق')\n      } catch (cashError) {\n        console.warn('تحذير: فشل في إضافة معاملة الصندوق:', cashError)\n      }\n    }\n\n    console.log('🎉 تمت معاملة المبيعات بنجاح!')\n    return { success: true, data: { invoiceId } }\n  } catch (error) {\n    console.error('❌ خطأ في إتمام معاملة المبيعات:', error)\n    return { success: false, error }\n  }\n}\n\n// Returns operations\nexport const getSalesInvoices = async () => {\n  try {\n    const { data, error } = await supabase\n      .from('sales_invoices')\n      .select(`\n        *,\n        customers (name, phone, address),\n        sales_invoice_items (\n          *,\n          medicine_batches (\n            batch_code,\n            expiry_date,\n            medicines (name, category, manufacturer, strength, form)\n          )\n        )\n      `)\n      .order('created_at', { ascending: false })\n\n    if (error) {\n      console.warn('Supabase error for sales invoices, using localStorage fallback:', error)\n      // Fallback to localStorage\n      const localInvoices = JSON.parse(localStorage.getItem('sales_invoices') || '[]')\n      const localItems = JSON.parse(localStorage.getItem('sales_invoice_items') || '[]')\n      const medicines = JSON.parse(localStorage.getItem('medicines') || '[]')\n      const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')\n\n      // Combine invoices with their items and ensure medicine names are available\n      const invoicesWithItems = localInvoices.map((invoice: any) => {\n        const items = localItems.filter((item: any) => item.invoice_id === invoice.id)\n\n        // Enhance items with medicine names if not already present\n        const enhancedItems = items.map((item: any) => {\n          if (item.medicine_batches?.medicines?.name) {\n            return item // Already has medicine name\n          }\n\n          // Find medicine name from batch\n          const batch = batches.find((b: any) => b.id === item.medicine_batch_id)\n          const medicine = medicines.find((m: any) => m.id === batch?.medicine_id)\n\n          return {\n            ...item,\n            medicine_name: medicine?.name || item.medicine_name || 'غير محدد',\n            medicine_batches: {\n              batch_code: batch?.batch_code || '',\n              expiry_date: batch?.expiry_date || '',\n              medicines: {\n                name: medicine?.name || item.medicine_name || 'غير محدد',\n                category: medicine?.category || '',\n                manufacturer: medicine?.manufacturer || '',\n                strength: medicine?.strength || '',\n                form: medicine?.form || ''\n              }\n            }\n          }\n        })\n\n        return {\n          ...invoice,\n          sales_invoice_items: enhancedItems\n        }\n      })\n\n      return { success: true, data: invoicesWithItems }\n    }\n\n    return { success: true, data }\n  } catch (error) {\n    console.error('Error fetching sales invoices:', error)\n\n    // Final fallback to localStorage\n    try {\n      const localInvoices = JSON.parse(localStorage.getItem('sales_invoices') || '[]')\n      const localItems = JSON.parse(localStorage.getItem('sales_invoice_items') || '[]')\n      const medicines = JSON.parse(localStorage.getItem('medicines') || '[]')\n      const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')\n\n      // Combine invoices with their items and ensure medicine names\n      const invoicesWithItems = localInvoices.map((invoice: any) => {\n        const items = localItems.filter((item: any) => item.invoice_id === invoice.id)\n\n        // Enhance items with medicine names if not already present\n        const enhancedItems = items.map((item: any) => {\n          if (item.medicine_batches?.medicines?.name) {\n            return item // Already has medicine name\n          }\n\n          // Find medicine name from batch\n          const batch = batches.find((b: any) => b.id === item.medicine_batch_id)\n          const medicine = medicines.find((m: any) => m.id === batch?.medicine_id)\n\n          return {\n            ...item,\n            medicine_name: medicine?.name || item.medicine_name || 'غير محدد',\n            medicine_batches: {\n              batch_code: batch?.batch_code || '',\n              expiry_date: batch?.expiry_date || '',\n              medicines: {\n                name: medicine?.name || item.medicine_name || 'غير محدد',\n                category: medicine?.category || '',\n                manufacturer: medicine?.manufacturer || '',\n                strength: medicine?.strength || '',\n                form: medicine?.form || ''\n              }\n            }\n          }\n        })\n\n        return {\n          ...invoice,\n          sales_invoice_items: enhancedItems\n        }\n      })\n\n      return { success: true, data: invoicesWithItems }\n    } catch (localError) {\n      console.error('LocalStorage fallback failed:', localError)\n      return { success: false, error }\n    }\n  }\n}\n\n// Get single sales invoice with full details for printing\nexport const getSalesInvoiceForPrint = async (invoiceId: string) => {\n  try {\n    const { data, error } = await supabase\n      .from('sales_invoices')\n      .select(`\n        *,\n        customers (name, phone, address),\n        sales_invoice_items (\n          *,\n          medicine_batches (\n            batch_code,\n            expiry_date,\n            medicines (name, category, manufacturer, strength, form)\n          )\n        )\n      `)\n      .eq('id', invoiceId)\n      .single()\n\n    if (error) {\n      console.warn('Supabase error for single invoice, using localStorage fallback:', error)\n      // Fallback to localStorage\n      const localInvoices = JSON.parse(localStorage.getItem('sales_invoices') || '[]')\n      const localItems = JSON.parse(localStorage.getItem('sales_invoice_items') || '[]')\n      const medicines = JSON.parse(localStorage.getItem('medicines') || '[]')\n      const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')\n\n      const invoice = localInvoices.find((inv: any) => inv.id === invoiceId)\n      if (invoice) {\n        const items = localItems.filter((item: any) => item.invoice_id === invoiceId)\n\n        // Enhance items with medicine names - FORCE REFRESH\n        console.log('🔧 بدء تحسين عناصر الفاتورة للطباعة...')\n        console.log('📦 عدد العناصر:', items.length)\n        console.log('💊 عدد الأدوية المتاحة:', medicines.length)\n        console.log('📋 عدد الدفعات المتاحة:', batches.length)\n\n        const itemsWithNames = items.map((item: any, index: number) => {\n          console.log(`\\n--- العنصر ${index + 1} ---`)\n          console.log('البيانات الأصلية:', item)\n\n          // Find medicine name from batch - ALWAYS recalculate\n          const batch = batches.find((b: any) => b.id === item.medicine_batch_id)\n          console.log('الدفعة الموجودة:', batch)\n\n          const medicine = medicines.find((m: any) => m.id === batch?.medicine_id)\n          console.log('الدواء الموجود:', medicine)\n\n          // Get the best available medicine name\n          const medicineName = medicine?.name || 'غير محدد'\n          console.log('اسم الدواء المحسوب:', medicineName)\n\n          const enhancedItem = {\n            ...item,\n            medicine_name: medicineName,\n            medicineName: medicineName, // Add both for compatibility\n            medicine_batches: {\n              id: batch?.id,\n              batch_code: batch?.batch_code || item.batch_code || '',\n              expiry_date: batch?.expiry_date || item.expiry_date || '',\n              medicine_id: batch?.medicine_id,\n              medicines: {\n                id: medicine?.id,\n                name: medicineName,\n                category: medicine?.category || '',\n                manufacturer: medicine?.manufacturer || '',\n                strength: medicine?.strength || '',\n                form: medicine?.form || ''\n              }\n            }\n          }\n\n          console.log('العنصر المحسن:', enhancedItem)\n          return enhancedItem\n        })\n\n        console.log('✅ تم تحسين جميع العناصر')\n        console.log('النتيجة النهائية:', itemsWithNames)\n\n        return {\n          success: true,\n          data: {\n            ...invoice,\n            sales_invoice_items: itemsWithNames\n          }\n        }\n      }\n    }\n\n    return { success: true, data }\n  } catch (error) {\n    console.error('Error fetching sales invoice for print:', error)\n    return { success: false, error }\n  }\n}\n\n// Get single purchase invoice with full details for printing\nexport const getPurchaseInvoiceForPrint = async (invoiceId: string) => {\n  try {\n    const { data, error } = await supabase\n      .from('purchase_invoices')\n      .select(`\n        *,\n        suppliers (name, contact_person, phone, address),\n        purchase_invoice_items (\n          *,\n          medicines (name, category, manufacturer, strength, form)\n        )\n      `)\n      .eq('id', invoiceId)\n      .single()\n\n    if (error) {\n      console.warn('Supabase error for single purchase invoice, using localStorage fallback:', error)\n      // Fallback to localStorage\n      const localInvoices = JSON.parse(localStorage.getItem('purchase_invoices') || '[]')\n      const localItems = JSON.parse(localStorage.getItem('purchase_invoice_items') || '[]')\n\n      const invoice = localInvoices.find((inv: any) => inv.id === invoiceId)\n      if (invoice) {\n        const items = localItems.filter((item: any) => item.invoice_id === invoiceId)\n\n        // Enhance items with medicine names\n        console.log('🔧 بدء تحسين عناصر فاتورة المشتريات للطباعة...')\n        console.log('📦 عدد العناصر:', items.length)\n\n        const itemsWithNames = items.map((item: any, index: number) => {\n          console.log(`\\n--- العنصر ${index + 1} ---`)\n          console.log('البيانات الأصلية:', item)\n\n          // Use existing medicine name if available\n          const medicineName = item.medicine_name || item.medicineName || 'غير محدد'\n          console.log('اسم الدواء:', medicineName)\n\n          const enhancedItem = {\n            ...item,\n            medicine_name: medicineName,\n            medicineName: medicineName,\n            medicines: {\n              name: medicineName,\n              category: item.category || '',\n              manufacturer: item.manufacturer || '',\n              strength: item.strength || '',\n              form: item.form || ''\n            }\n          }\n\n          console.log('العنصر المحسن:', enhancedItem)\n          return enhancedItem\n        })\n\n        console.log('✅ تم تحسين جميع عناصر المشتريات')\n\n        return {\n          success: true,\n          data: {\n            ...invoice,\n            purchase_invoice_items: itemsWithNames\n          }\n        }\n      }\n    }\n\n    return { success: true, data }\n  } catch (error) {\n    console.error('Error fetching purchase invoice for print:', error)\n    return { success: false, error }\n  }\n}\n\nexport const getPurchaseInvoices = async () => {\n  try {\n    const { data, error } = await supabase\n      .from('purchase_invoices')\n      .select(`\n        *,\n        suppliers (name, contact_person, phone, address),\n        purchase_invoice_items (\n          *,\n          medicines (name, category, manufacturer, strength, form)\n        )\n      `)\n      .order('created_at', { ascending: false })\n\n    if (error) {\n      console.warn('Supabase error for purchase invoices, using localStorage fallback:', error)\n      // Fallback to localStorage\n      const localInvoices = JSON.parse(localStorage.getItem('purchase_invoices') || '[]')\n      const localItems = JSON.parse(localStorage.getItem('purchase_invoice_items') || '[]')\n\n      // Combine invoices with their items\n      const invoicesWithItems = localInvoices.map((invoice: any) => ({\n        ...invoice,\n        purchase_invoice_items: localItems.filter((item: any) => item.invoice_id === invoice.id)\n      }))\n\n      return { success: true, data: invoicesWithItems }\n    }\n\n    return { success: true, data }\n  } catch (error) {\n    console.error('Error fetching purchase invoices:', error)\n\n    // Final fallback to localStorage\n    try {\n      const localInvoices = JSON.parse(localStorage.getItem('purchase_invoices') || '[]')\n      const localItems = JSON.parse(localStorage.getItem('purchase_invoice_items') || '[]')\n\n      // Combine invoices with their items\n      const invoicesWithItems = localInvoices.map((invoice: any) => ({\n        ...invoice,\n        purchase_invoice_items: localItems.filter((item: any) => item.invoice_id === invoice.id)\n      }))\n\n      return { success: true, data: invoicesWithItems }\n    } catch (localError) {\n      console.error('LocalStorage fallback failed:', localError)\n      return { success: false, error }\n    }\n  }\n}\n\nexport const createSalesReturn = async (returnData: {\n  return_number: string\n  original_invoice_id: string\n  customer_id?: string\n  customer_name?: string\n  total_amount: number\n  reason: string\n  status: string\n  notes?: string\n}) => {\n  try {\n    // Try Supabase first\n    const { data, error } = await supabase\n      .from('sales_returns')\n      .insert([returnData])\n      .select()\n      .single()\n\n    if (error) throw error\n    return { success: true, data }\n  } catch (error) {\n    console.warn('Supabase sales return failed, using localStorage fallback:', error)\n\n    // Fallback to localStorage\n    try {\n      const returnWithId = {\n        ...returnData,\n        id: `sr_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n        created_at: new Date().toISOString()\n      }\n\n      const existingReturns = JSON.parse(localStorage.getItem('sales_returns') || '[]')\n      existingReturns.push(returnWithId)\n      localStorage.setItem('sales_returns', JSON.stringify(existingReturns))\n\n      console.log('Sales return saved to localStorage:', returnWithId)\n      console.log('Total sales returns in localStorage:', existingReturns.length)\n\n      return { success: true, data: returnWithId }\n    } catch (fallbackError) {\n      console.error('Error creating sales return (fallback):', fallbackError)\n      return { success: false, error: fallbackError }\n    }\n  }\n}\n\nexport const createPurchaseReturn = async (returnData: {\n  return_number: string\n  original_invoice_id: string\n  supplier_id: string\n  total_amount: number\n  reason: string\n  status: string\n  notes?: string\n}) => {\n  try {\n    // Try Supabase first\n    const { data, error } = await supabase\n      .from('purchase_returns')\n      .insert([returnData])\n      .select()\n      .single()\n\n    if (error) throw error\n    return { success: true, data }\n  } catch (error) {\n    console.warn('Supabase purchase return failed, using localStorage fallback:', error)\n\n    // Fallback to localStorage\n    try {\n      const returnWithId = {\n        ...returnData,\n        id: `pr_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n        created_at: new Date().toISOString()\n      }\n\n      const existingReturns = JSON.parse(localStorage.getItem('purchase_returns') || '[]')\n      existingReturns.push(returnWithId)\n      localStorage.setItem('purchase_returns', JSON.stringify(existingReturns))\n\n      console.log('Purchase return saved to localStorage:', returnWithId)\n      console.log('Total purchase returns in localStorage:', existingReturns.length)\n\n      return { success: true, data: returnWithId }\n    } catch (fallbackError) {\n      console.error('Error creating purchase return (fallback):', fallbackError)\n      return { success: false, error: fallbackError }\n    }\n  }\n}\n\nexport const addReturnItems = async (items: Array<{\n  return_id: string\n  return_type: 'sales' | 'purchase'\n  medicine_batch_id?: string\n  medicine_id?: string\n  quantity: number\n  unit_price: number\n  total_price: number\n}>) => {\n  try {\n    // Try Supabase first\n    const tableName = items[0]?.return_type === 'sales' ? 'sales_return_items' : 'purchase_return_items'\n    const { data, error } = await supabase\n      .from(tableName)\n      .insert(items.map(item => ({\n        return_id: item.return_id,\n        medicine_batch_id: item.medicine_batch_id,\n        medicine_id: item.medicine_id,\n        quantity: item.quantity,\n        unit_price: item.unit_price,\n        total_price: item.total_price\n      })))\n      .select()\n\n    if (error) throw error\n    return { success: true, data }\n  } catch (error) {\n    console.warn('Supabase return items failed, using localStorage fallback:', error)\n\n    // Fallback to localStorage\n    try {\n      const storageKey = items[0]?.return_type === 'sales' ? 'sales_return_items' : 'purchase_return_items'\n      const itemsWithIds = items.map(item => ({\n        ...item,\n        id: `ri_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n        created_at: new Date().toISOString()\n      }))\n\n      const existingItems = JSON.parse(localStorage.getItem(storageKey) || '[]')\n      existingItems.push(...itemsWithIds)\n      localStorage.setItem(storageKey, JSON.stringify(existingItems))\n\n      return { success: true, data: itemsWithIds }\n    } catch (fallbackError) {\n      console.error('Error adding return items (fallback):', fallbackError)\n      return { success: false, error: fallbackError }\n    }\n  }\n}\n\nexport const processReturn = async (\n  returnType: 'sales' | 'purchase',\n  returnData: any,\n  items: any[]\n) => {\n  try {\n    // Create return record\n    const returnResult = returnType === 'sales'\n      ? await createSalesReturn(returnData)\n      : await createPurchaseReturn(returnData)\n\n    if (!returnResult.success) throw new Error('Failed to create return')\n\n    const returnId = returnResult.data.id\n\n    // Add return items\n    const returnItems = items.map(item => ({\n      return_id: returnId,\n      return_type: returnType,\n      medicine_batch_id: item.batchId,\n      medicine_id: item.medicineId,\n      quantity: item.quantity,\n      unit_price: item.unitPrice,\n      total_price: item.totalPrice\n    }))\n\n    await addReturnItems(returnItems)\n\n    // Try to update inventory (skip if Supabase is not available)\n    try {\n      // Update inventory for sales returns (add back to stock)\n      if (returnType === 'sales') {\n        for (const item of items) {\n          if (item.batchId) {\n            try {\n              // Get current batch quantity\n              const { data: batch } = await supabase\n                .from('medicine_batches')\n                .select('quantity')\n                .eq('id', item.batchId)\n                .single()\n\n              if (batch) {\n                // Add returned quantity back to stock\n                await updateBatchQuantity(item.batchId, batch.quantity + item.quantity)\n              }\n\n              // Add inventory movement\n              await addInventoryMovement({\n                medicine_batch_id: item.batchId,\n                movement_type: 'in',\n                quantity: item.quantity,\n                reference_type: 'return',\n                reference_id: returnId,\n                notes: `مرتجع مبيعات - ${returnData.reason}`\n              })\n            } catch (inventoryError) {\n              console.warn('Failed to update inventory for item:', item.batchId, inventoryError)\n            }\n          }\n        }\n      }\n\n      // Update inventory for purchase returns (remove from stock)\n      if (returnType === 'purchase') {\n        for (const item of items) {\n          if (item.batchId) {\n            try {\n              // Get current batch quantity\n              const { data: batch } = await supabase\n                .from('medicine_batches')\n                .select('quantity')\n                .eq('id', item.batchId)\n                .single()\n\n              if (batch) {\n                // Remove returned quantity from stock\n                const newQuantity = Math.max(0, batch.quantity - item.quantity)\n                await updateBatchQuantity(item.batchId, newQuantity)\n              }\n\n              // Add inventory movement\n              await addInventoryMovement({\n                medicine_batch_id: item.batchId,\n                movement_type: 'out',\n                quantity: item.quantity,\n                reference_type: 'return',\n                reference_id: returnId,\n                notes: `مرتجع مشتريات - ${returnData.reason}`\n              })\n            } catch (inventoryError) {\n              console.warn('Failed to update inventory for item:', item.batchId, inventoryError)\n            }\n          }\n        }\n      }\n    } catch (inventoryError) {\n      console.warn('Inventory update failed, but return was created successfully:', inventoryError)\n    }\n\n    return { success: true, data: { returnId } }\n  } catch (error) {\n    console.error('Error processing return:', error)\n    return { success: false, error }\n  }\n}\n\nexport const getReturns = async () => {\n  // Always try localStorage first for faster response\n  try {\n    const salesReturns = JSON.parse(localStorage.getItem('sales_returns') || '[]')\n    const purchaseReturns = JSON.parse(localStorage.getItem('purchase_returns') || '[]')\n    const customers = JSON.parse(localStorage.getItem('customers') || '[]')\n    const suppliers = JSON.parse(localStorage.getItem('suppliers') || '[]')\n\n    console.log('Loading returns from localStorage:', {\n      salesReturns: salesReturns.length,\n      purchaseReturns: purchaseReturns.length,\n      customers: customers.length,\n      suppliers: suppliers.length\n    })\n\n    // Enrich sales returns with customer data\n    const enrichedSalesReturns = salesReturns.map((returnItem: any) => {\n      const customer = customers.find((c: any) => c.id === returnItem.customer_id)\n      console.log(`Enriching sales return ${returnItem.id}:`, {\n        original_items: returnItem.return_items,\n        items_count: returnItem.return_items?.length || 0\n      })\n      return {\n        ...returnItem,\n        return_type: 'sales',\n        customers: customer ? {\n          name: customer.name,\n          phone: customer.phone,\n          address: customer.address\n        } : null,\n        customer_name: customer?.name || returnItem.customer_name || 'عميل غير محدد',\n        // تأكد من وجود المواد\n        return_items: returnItem.return_items || []\n      }\n    })\n\n    // Enrich purchase returns with supplier data\n    const enrichedPurchaseReturns = purchaseReturns.map((returnItem: any) => {\n      const supplier = suppliers.find((s: any) => s.id === returnItem.supplier_id)\n      console.log(`Enriching purchase return ${returnItem.id}:`, {\n        original_items: returnItem.return_items,\n        items_count: returnItem.return_items?.length || 0\n      })\n      return {\n        ...returnItem,\n        return_type: 'purchase',\n        suppliers: supplier ? {\n          name: supplier.name,\n          phone: supplier.phone,\n          address: supplier.address\n        } : null,\n        supplier_name: supplier?.name || returnItem.supplier_name || 'مورد غير محدد',\n        // تأكد من وجود المواد\n        return_items: returnItem.return_items || []\n      }\n    })\n\n    // If we have local data, return it immediately\n    if (enrichedSalesReturns.length > 0 || enrichedPurchaseReturns.length > 0) {\n      const allReturns = [\n        ...enrichedSalesReturns,\n        ...enrichedPurchaseReturns\n      ].sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())\n\n      console.log('Returning enriched returns from localStorage:', allReturns.slice(0, 2))\n      return { success: true, data: allReturns }\n    }\n  } catch (localError) {\n    console.warn('Error reading from localStorage:', localError)\n  }\n\n  // If no local data, try Supabase\n  try {\n    console.log('Trying Supabase for returns...')\n    const [salesReturns, purchaseReturns] = await Promise.all([\n      supabase\n        .from('sales_returns')\n        .select(`\n          *,\n          customers (name, phone),\n          sales_return_items (\n            *,\n            medicine_batches (\n              batch_code,\n              medicines (name)\n            )\n          )\n        `)\n        .order('created_at', { ascending: false }),\n\n      supabase\n        .from('purchase_returns')\n        .select(`\n          *,\n          suppliers (name, contact_person),\n          purchase_return_items (\n            *,\n            medicines (name)\n          )\n        `)\n        .order('created_at', { ascending: false })\n    ])\n\n    const allReturns = [\n      ...(salesReturns.data || []).map(item => ({\n        ...item,\n        return_type: 'sales',\n        customer_name: item.customers?.name || 'عميل غير محدد'\n      })),\n      ...(purchaseReturns.data || []).map(item => ({\n        ...item,\n        return_type: 'purchase',\n        supplier_name: item.suppliers?.name || 'مورد غير محدد'\n      }))\n    ].sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())\n\n    console.log('Returning returns from Supabase:', allReturns.slice(0, 2))\n    return { success: true, data: allReturns }\n  } catch (error) {\n    console.warn('Supabase returns failed, returning empty array:', error)\n\n    // Return empty array if both localStorage and Supabase fail\n    return { success: true, data: [] }\n  }\n}\n\n// Get return by ID with full details\nexport const getReturnById = async (returnId: string) => {\n  try {\n    // Try localStorage first\n    const salesReturns = JSON.parse(localStorage.getItem('sales_returns') || '[]')\n    const purchaseReturns = JSON.parse(localStorage.getItem('purchase_returns') || '[]')\n    const customers = JSON.parse(localStorage.getItem('customers') || '[]')\n    const suppliers = JSON.parse(localStorage.getItem('suppliers') || '[]')\n\n    let foundReturn = salesReturns.find((r: any) => r.id === returnId)\n    let returnType = 'sales'\n\n    if (!foundReturn) {\n      foundReturn = purchaseReturns.find((r: any) => r.id === returnId)\n      returnType = 'purchase'\n    }\n\n    if (foundReturn) {\n      // Enrich with customer/supplier data\n      if (returnType === 'sales') {\n        const customer = customers.find((c: any) => c.id === foundReturn.customer_id)\n        foundReturn = {\n          ...foundReturn,\n          return_type: 'sales',\n          customers: customer ? {\n            name: customer.name,\n            phone: customer.phone,\n            address: customer.address\n          } : null,\n          customer_name: customer?.name || foundReturn.customer_name || 'عميل غير محدد'\n        }\n      } else {\n        const supplier = suppliers.find((s: any) => s.id === foundReturn.supplier_id)\n        foundReturn = {\n          ...foundReturn,\n          return_type: 'purchase',\n          suppliers: supplier ? {\n            name: supplier.name,\n            phone: supplier.phone,\n            address: supplier.address\n          } : null,\n          supplier_name: supplier?.name || foundReturn.supplier_name || 'مورد غير محدد'\n        }\n      }\n\n      console.log('Found enriched return in localStorage:', foundReturn)\n      return { success: true, data: foundReturn }\n    }\n\n    // If not in localStorage, try Supabase\n    if (!supabase) {\n      console.warn('Supabase not available, return not found')\n      return { success: false, error: 'Return not found' }\n    }\n\n    // Try sales returns first\n    const { data: salesReturn, error: salesError } = await supabase\n      .from('sales_returns')\n      .select(`\n        *,\n        customers (name, phone, address),\n        sales_return_items (\n          *,\n          medicine_batches (\n            *,\n            medicines (name)\n          )\n        )\n      `)\n      .eq('id', returnId)\n      .single()\n\n    if (salesReturn && !salesError) {\n      const returnData = {\n        ...salesReturn,\n        return_type: 'sales',\n        return_items: salesReturn.sales_return_items || []\n      }\n      console.log('Found sales return in Supabase:', returnData)\n      return { success: true, data: returnData }\n    }\n\n    // Try purchase returns\n    const { data: purchaseReturn, error: purchaseError } = await supabase\n      .from('purchase_returns')\n      .select(`\n        *,\n        suppliers (name, phone, address),\n        purchase_return_items (\n          *,\n          medicines (name)\n        )\n      `)\n      .eq('id', returnId)\n      .single()\n\n    if (purchaseReturn && !purchaseError) {\n      const returnData = {\n        ...purchaseReturn,\n        return_type: 'purchase',\n        return_items: purchaseReturn.purchase_return_items || []\n      }\n      console.log('Found purchase return in Supabase:', returnData)\n      return { success: true, data: returnData }\n    }\n\n    console.warn('Return not found:', returnId)\n    return { success: false, error: 'Return not found' }\n  } catch (error) {\n    console.error('Error getting return by ID:', error)\n    return { success: false, error }\n  }\n}\n\n// Complete purchase transaction with inventory update\nexport const completePurchaseTransaction = async (\n  invoiceData: any,\n  items: any[]\n) => {\n  try {\n    // Create purchase invoice\n    const invoiceResult = await createPurchaseInvoice(invoiceData)\n    if (!invoiceResult.success) throw new Error('Failed to create purchase invoice')\n\n    const invoiceId = invoiceResult.data.id\n\n    // Process each item\n    for (const item of items) {\n      let medicineId = item.medicineId\n\n      // If medicine doesn't exist, create it\n      if (!medicineId) {\n        const newMedicineResult = await addMedicine({\n          name: item.medicineName,\n          category: item.category || 'أخرى',\n          manufacturer: item.manufacturer || '',\n          active_ingredient: item.activeIngredient || '',\n          strength: item.strength || '',\n          form: item.form || 'tablet',\n          unit_price: item.unitCost,\n          selling_price: item.sellingPrice || item.unitCost * 1.5\n        })\n\n        if (newMedicineResult.success) {\n          medicineId = newMedicineResult.data.id\n        } else {\n          console.error('Failed to create medicine:', newMedicineResult.error)\n          continue\n        }\n      }\n\n      // Add purchase invoice item\n      await addPurchaseInvoiceItems([{\n        invoice_id: invoiceId,\n        medicine_id: medicineId,\n        batch_code: item.batchCode,\n        quantity: item.quantity,\n        unit_cost: item.unitCost,\n        total_cost: item.totalCost,\n        expiry_date: item.expiryDate,\n        medicine_name: item.medicineName || 'غير محدد'\n      }])\n\n      // Create or update medicine batch\n      const batchResult = await addMedicineBatch({\n        medicine_id: medicineId,\n        batch_code: item.batchCode,\n        expiry_date: item.expiryDate,\n        quantity: item.quantity,\n        cost_price: item.unitCost,\n        selling_price: item.sellingPrice || item.unitCost * 1.5, // Default markup\n        supplier_id: invoiceData.supplier_id\n      })\n\n      if (batchResult.success) {\n        // Add inventory movement\n        await addInventoryMovement({\n          medicine_batch_id: batchResult.data.id,\n          movement_type: 'in',\n          quantity: item.quantity,\n          reference_type: 'purchase',\n          reference_id: invoiceId\n        })\n      }\n    }\n\n    // Add cash transaction if payment is cash\n    if (invoiceData.payment_method === 'cash' && invoiceData.payment_status === 'paid') {\n      await addCashTransaction({\n        transaction_type: 'expense',\n        category: 'مشتريات',\n        amount: invoiceData.final_amount,\n        description: `فاتورة مشتريات رقم ${invoiceData.invoice_number}`,\n        reference_type: 'purchase',\n        reference_id: invoiceId,\n        payment_method: 'cash',\n        notes: invoiceData.notes\n      })\n    }\n\n    return { success: true, data: { invoiceId } }\n  } catch (error) {\n    console.error('Error completing purchase transaction:', error)\n    return { success: false, error }\n  }\n}\n\n// Cash Box Operations\nexport const addCashTransaction = async (transactionData: {\n  transaction_type: 'income' | 'expense'\n  category: string\n  amount: number\n  description: string\n  reference_type?: string\n  reference_id?: string\n  payment_method: string\n  notes?: string\n}) => {\n  try {\n    // Try Supabase first\n    const { data, error } = await supabase\n      .from('cash_transactions')\n      .insert([transactionData])\n      .select()\n      .single()\n\n    if (error) throw error\n    return { success: true, data }\n  } catch (error) {\n    console.warn('Supabase cash transaction failed, using localStorage fallback:', error)\n\n    // Fallback to localStorage\n    try {\n      const transactionWithId = {\n        ...transactionData,\n        id: `ct_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n        created_at: new Date().toISOString()\n      }\n\n      const existingTransactions = JSON.parse(localStorage.getItem('cash_transactions') || '[]')\n      existingTransactions.push(transactionWithId)\n      localStorage.setItem('cash_transactions', JSON.stringify(existingTransactions))\n\n      console.log('Cash transaction saved to localStorage:', transactionWithId)\n      console.log('Total cash transactions in localStorage:', existingTransactions.length)\n\n      return { success: true, data: transactionWithId }\n    } catch (fallbackError) {\n      console.error('Error adding cash transaction (fallback):', fallbackError)\n      return { success: false, error: fallbackError }\n    }\n  }\n}\n\nexport const getCashTransactions = async (filters?: {\n  start_date?: string\n  end_date?: string\n  transaction_type?: string\n  category?: string\n}) => {\n  // Always try localStorage first for faster response\n  try {\n    const transactions = JSON.parse(localStorage.getItem('cash_transactions') || '[]')\n\n    // If we have local data, filter and return it\n    if (transactions.length > 0) {\n      console.log('Loading cash transactions from localStorage:', transactions.length)\n\n      let filteredTransactions = transactions\n\n      if (filters?.start_date) {\n        filteredTransactions = filteredTransactions.filter(t => t.created_at >= filters.start_date)\n      }\n      if (filters?.end_date) {\n        filteredTransactions = filteredTransactions.filter(t => t.created_at <= filters.end_date)\n      }\n      if (filters?.transaction_type) {\n        filteredTransactions = filteredTransactions.filter(t => t.transaction_type === filters.transaction_type)\n      }\n      if (filters?.category) {\n        filteredTransactions = filteredTransactions.filter(t => t.category === filters.category)\n      }\n\n      // Sort by created_at descending\n      filteredTransactions.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())\n\n      return { success: true, data: filteredTransactions }\n    }\n  } catch (localError) {\n    console.warn('Error reading cash transactions from localStorage:', localError)\n  }\n\n  // If no local data, try Supabase\n  try {\n    console.log('Trying Supabase for cash transactions...')\n    let query = supabase\n      .from('cash_transactions')\n      .select('*')\n      .order('created_at', { ascending: false })\n\n    if (filters?.start_date) {\n      query = query.gte('created_at', filters.start_date)\n    }\n    if (filters?.end_date) {\n      query = query.lte('created_at', filters.end_date)\n    }\n    if (filters?.transaction_type) {\n      query = query.eq('transaction_type', filters.transaction_type)\n    }\n    if (filters?.category) {\n      query = query.eq('category', filters.category)\n    }\n\n    const { data, error } = await query\n\n    if (error) throw error\n    return { success: true, data }\n  } catch (error) {\n    console.warn('Supabase cash transactions failed, returning empty array:', error)\n    return { success: true, data: [] }\n  }\n}\n\nexport const getCashBalance = async () => {\n  // Try localStorage first\n  try {\n    const transactions = JSON.parse(localStorage.getItem('cash_transactions') || '[]')\n\n    if (transactions.length > 0) {\n      console.log('Calculating cash balance from localStorage:', transactions.length, 'transactions')\n\n      const balance = transactions.reduce((total: number, transaction: any) => {\n        return transaction.transaction_type === 'income'\n          ? total + transaction.amount\n          : total - transaction.amount\n      }, 0)\n\n      return { success: true, data: balance }\n    }\n  } catch (localError) {\n    console.warn('Error calculating balance from localStorage:', localError)\n  }\n\n  // If no local data, try Supabase\n  try {\n    console.log('Trying Supabase for cash balance...')\n    const { data, error } = await supabase\n      .from('cash_transactions')\n      .select('transaction_type, amount')\n\n    if (error) throw error\n\n    const balance = data.reduce((total, transaction) => {\n      return transaction.transaction_type === 'income'\n        ? total + transaction.amount\n        : total - transaction.amount\n    }, 0)\n\n    return { success: true, data: balance }\n  } catch (error) {\n    console.warn('Supabase cash balance failed, returning 0:', error)\n    return { success: true, data: 0 }\n  }\n}\n\n// Customer and Supplier Debts\nexport const getCustomerDebts = async () => {\n  // Try localStorage first\n  try {\n    const salesInvoices = JSON.parse(localStorage.getItem('sales_invoices') || '[]')\n\n    if (salesInvoices.length > 0) {\n      console.log('Loading customer debts from localStorage:', salesInvoices.length, 'invoices')\n\n      // Filter for pending payments only\n      const pendingInvoices = salesInvoices.filter((invoice: any) =>\n        invoice.payment_status === 'pending' || invoice.payment_status === 'partial'\n      )\n\n      console.log('Found customer debts:', pendingInvoices.length)\n      return { success: true, data: pendingInvoices }\n    } else {\n      // Create sample debt data if no invoices exist\n      console.log('No sales invoices found, creating sample customer debts')\n      const sampleDebts = [\n        {\n          id: 'debt_1',\n          invoice_number: 'INV-001',\n          customer_id: 'cust_1',\n          customer_name: 'أحمد محمد علي',\n          final_amount: 150000,\n          payment_status: 'pending',\n          created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),\n          customers: { name: 'أحمد محمد علي', phone: '07901111111' }\n        },\n        {\n          id: 'debt_2',\n          invoice_number: 'INV-003',\n          customer_id: 'cust_2',\n          customer_name: 'فاطمة حسن محمد',\n          final_amount: 85000,\n          payment_status: 'partial',\n          created_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),\n          customers: { name: 'فاطمة حسن محمد', phone: '07802222222' }\n        }\n      ]\n      return { success: true, data: sampleDebts }\n    }\n  } catch (localError) {\n    console.warn('Error reading customer debts from localStorage:', localError)\n  }\n\n  // If no local data, try Supabase\n  try {\n    console.log('Trying Supabase for customer debts...')\n    const { data, error } = await supabase\n      .from('sales_invoices')\n      .select(`\n        id,\n        invoice_number,\n        customer_id,\n        customer_name,\n        final_amount,\n        payment_status,\n        created_at,\n        customers (name, phone)\n      `)\n      .eq('payment_status', 'pending')\n      .order('created_at', { ascending: false })\n\n    if (error) throw error\n    return { success: true, data }\n  } catch (error) {\n    console.warn('Supabase customer debts failed, returning empty array:', error)\n    return { success: true, data: [] }\n  }\n}\n\nexport const getSupplierDebts = async () => {\n  // Try localStorage first\n  try {\n    const purchaseInvoices = JSON.parse(localStorage.getItem('purchase_invoices') || '[]')\n\n    if (purchaseInvoices.length > 0) {\n      console.log('Loading supplier debts from localStorage:', purchaseInvoices.length, 'invoices')\n\n      // Filter for pending payments only\n      const pendingInvoices = purchaseInvoices.filter((invoice: any) =>\n        invoice.payment_status === 'pending' || invoice.payment_status === 'partial'\n      )\n\n      console.log('Found supplier debts:', pendingInvoices.length)\n      return { success: true, data: pendingInvoices }\n    } else {\n      // Create sample debt data if no invoices exist\n      console.log('No purchase invoices found, creating sample supplier debts')\n      const sampleDebts = [\n        {\n          id: 'debt_3',\n          invoice_number: 'PUR-001',\n          supplier_id: 'sup_1',\n          final_amount: 2500000,\n          payment_status: 'pending',\n          created_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),\n          suppliers: { name: 'شركة الأدوية العراقية', contact_person: 'أحمد محمد', phone: '07901234567' }\n        },\n        {\n          id: 'debt_4',\n          invoice_number: 'PUR-004',\n          supplier_id: 'sup_2',\n          final_amount: 1800000,\n          payment_status: 'partial',\n          created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),\n          suppliers: { name: 'شركة بغداد للأدوية', contact_person: 'فاطمة علي', phone: '07801234567' }\n        }\n      ]\n      return { success: true, data: sampleDebts }\n    }\n  } catch (localError) {\n    console.warn('Error reading supplier debts from localStorage:', localError)\n  }\n\n  // If no local data, try Supabase\n  try {\n    console.log('Trying Supabase for supplier debts...')\n    const { data, error } = await supabase\n      .from('purchase_invoices')\n      .select(`\n        id,\n        invoice_number,\n        supplier_id,\n        final_amount,\n        payment_status,\n        created_at,\n        suppliers (name, contact_person, phone)\n      `)\n      .eq('payment_status', 'pending')\n      .order('created_at', { ascending: false })\n\n    if (error) throw error\n    return { success: true, data }\n  } catch (error) {\n    console.warn('Supabase supplier debts failed, returning empty array:', error)\n    return { success: true, data: [] }\n  }\n}\n\nexport const updatePaymentStatus = async (\n  invoiceType: 'sales' | 'purchase',\n  invoiceId: string,\n  paymentStatus: string,\n  paidAmount?: number\n) => {\n  try {\n    // Try Supabase first\n    const tableName = invoiceType === 'sales' ? 'sales_invoices' : 'purchase_invoices'\n\n    const { data, error } = await supabase\n      .from(tableName)\n      .update({\n        payment_status: paymentStatus,\n        ...(paidAmount && { paid_amount: paidAmount })\n      })\n      .eq('id', invoiceId)\n      .select()\n      .single()\n\n    if (error) throw error\n\n    // Add cash transaction if payment is completed\n    if (paymentStatus === 'paid' && paidAmount) {\n      const transactionType = invoiceType === 'sales' ? 'income' : 'expense'\n      const category = invoiceType === 'sales' ? 'مبيعات' : 'مشتريات'\n\n      await addCashTransaction({\n        transaction_type: transactionType,\n        category,\n        amount: paidAmount,\n        description: `دفع فاتورة ${invoiceType === 'sales' ? 'مبيعات' : 'مشتريات'} رقم ${data.invoice_number}`,\n        reference_type: invoiceType,\n        reference_id: invoiceId,\n        payment_method: 'cash'\n      })\n    }\n\n    return { success: true, data }\n  } catch (error) {\n    console.warn('Supabase payment update failed, using localStorage fallback:', error)\n\n    // Fallback to localStorage\n    try {\n      const storageKey = invoiceType === 'sales' ? 'sales_invoices' : 'purchase_invoices'\n      const invoices = JSON.parse(localStorage.getItem(storageKey) || '[]')\n\n      const invoiceIndex = invoices.findIndex((inv: any) => inv.id === invoiceId)\n      if (invoiceIndex !== -1) {\n        invoices[invoiceIndex].payment_status = paymentStatus\n        if (paidAmount) {\n          invoices[invoiceIndex].paid_amount = paidAmount\n        }\n\n        localStorage.setItem(storageKey, JSON.stringify(invoices))\n\n        // Add cash transaction if payment is completed\n        if (paymentStatus === 'paid' && paidAmount) {\n          const transactionType = invoiceType === 'sales' ? 'income' : 'expense'\n          const category = invoiceType === 'sales' ? 'مبيعات' : 'مشتريات'\n\n          await addCashTransaction({\n            transaction_type: transactionType,\n            category,\n            amount: paidAmount,\n            description: `دفع فاتورة ${invoiceType === 'sales' ? 'مبيعات' : 'مشتريات'} رقم ${invoices[invoiceIndex].invoice_number}`,\n            reference_type: invoiceType,\n            reference_id: invoiceId,\n            payment_method: 'cash'\n          })\n        }\n\n        console.log('Payment status updated in localStorage:', invoices[invoiceIndex])\n        return { success: true, data: invoices[invoiceIndex] }\n      } else {\n        throw new Error('Invoice not found in localStorage')\n      }\n    } catch (fallbackError) {\n      console.error('Error updating payment status (fallback):', fallbackError)\n      return { success: false, error: fallbackError }\n    }\n  }\n}\n\n// Advanced Reports Functions\nexport const getSalesReport = async (filters: {\n  start_date?: string\n  end_date?: string\n  customer_id?: string\n  medicine_id?: string\n}) => {\n  try {\n    let query = supabase\n      .from('sales_invoices')\n      .select(`\n        *,\n        customers (name, phone),\n        sales_invoice_items (\n          *,\n          medicine_batches (\n            batch_code,\n            medicines (name, category)\n          )\n        )\n      `)\n      .order('created_at', { ascending: false })\n\n    if (filters.start_date) {\n      query = query.gte('created_at', filters.start_date)\n    }\n    if (filters.end_date) {\n      query = query.lte('created_at', filters.end_date)\n    }\n    if (filters.customer_id) {\n      query = query.eq('customer_id', filters.customer_id)\n    }\n\n    const { data, error } = await query\n\n    if (error) throw error\n\n    // Filter by medicine if specified\n    let filteredData = data\n    if (filters.medicine_id) {\n      filteredData = data.filter(invoice =>\n        invoice.sales_invoice_items.some((item: any) =>\n          item.medicine_batches?.medicines?.id === filters.medicine_id\n        )\n      )\n    }\n\n    return { success: true, data: filteredData }\n  } catch (error) {\n    console.error('Error fetching sales report:', error)\n    return { success: false, error }\n  }\n}\n\nexport const getPurchasesReport = async (filters: {\n  start_date?: string\n  end_date?: string\n  supplier_id?: string\n  medicine_id?: string\n}) => {\n  try {\n    let query = supabase\n      .from('purchase_invoices')\n      .select(`\n        *,\n        suppliers (name, contact_person, phone),\n        purchase_invoice_items (\n          *,\n          medicines (name, category)\n        )\n      `)\n      .order('created_at', { ascending: false })\n\n    if (filters.start_date) {\n      query = query.gte('created_at', filters.start_date)\n    }\n    if (filters.end_date) {\n      query = query.lte('created_at', filters.end_date)\n    }\n    if (filters.supplier_id) {\n      query = query.eq('supplier_id', filters.supplier_id)\n    }\n\n    const { data, error } = await query\n\n    if (error) throw error\n\n    // Filter by medicine if specified\n    let filteredData = data\n    if (filters.medicine_id) {\n      filteredData = data.filter(invoice =>\n        invoice.purchase_invoice_items.some((item: any) =>\n          item.medicines?.id === filters.medicine_id\n        )\n      )\n    }\n\n    return { success: true, data: filteredData }\n  } catch (error) {\n    console.error('Error fetching purchases report:', error)\n    return { success: false, error }\n  }\n}\n\nexport const getCustomerStatement = async (customerId: string, filters: {\n  start_date?: string\n  end_date?: string\n}) => {\n  try {\n    let salesQuery = supabase\n      .from('sales_invoices')\n      .select(`\n        id,\n        invoice_number,\n        total_amount,\n        discount_amount,\n        final_amount,\n        payment_status,\n        payment_method,\n        created_at,\n        notes\n      `)\n      .eq('customer_id', customerId)\n      .order('created_at', { ascending: false })\n\n    let returnsQuery = supabase\n      .from('sales_returns')\n      .select(`\n        id,\n        return_number,\n        total_amount,\n        reason,\n        status,\n        created_at\n      `)\n      .eq('customer_id', customerId)\n      .order('created_at', { ascending: false })\n\n    if (filters.start_date) {\n      salesQuery = salesQuery.gte('created_at', filters.start_date)\n      returnsQuery = returnsQuery.gte('created_at', filters.start_date)\n    }\n    if (filters.end_date) {\n      salesQuery = salesQuery.lte('created_at', filters.end_date)\n      returnsQuery = returnsQuery.lte('created_at', filters.end_date)\n    }\n\n    const [salesResult, returnsResult] = await Promise.all([\n      salesQuery,\n      returnsQuery\n    ])\n\n    if (salesResult.error) throw salesResult.error\n    if (returnsResult.error) throw returnsResult.error\n\n    return {\n      success: true,\n      data: {\n        sales: salesResult.data,\n        returns: returnsResult.data\n      }\n    }\n  } catch (error) {\n    console.error('Error fetching customer statement:', error)\n    return { success: false, error }\n  }\n}\n\nexport const getSupplierStatement = async (supplierId: string, filters: {\n  start_date?: string\n  end_date?: string\n}) => {\n  try {\n    let purchasesQuery = supabase\n      .from('purchase_invoices')\n      .select(`\n        id,\n        invoice_number,\n        total_amount,\n        discount_amount,\n        final_amount,\n        payment_status,\n        payment_method,\n        created_at,\n        notes\n      `)\n      .eq('supplier_id', supplierId)\n      .order('created_at', { ascending: false })\n\n    let returnsQuery = supabase\n      .from('purchase_returns')\n      .select(`\n        id,\n        return_number,\n        total_amount,\n        reason,\n        status,\n        created_at\n      `)\n      .eq('supplier_id', supplierId)\n      .order('created_at', { ascending: false })\n\n    if (filters.start_date) {\n      purchasesQuery = purchasesQuery.gte('created_at', filters.start_date)\n      returnsQuery = returnsQuery.gte('created_at', filters.start_date)\n    }\n    if (filters.end_date) {\n      purchasesQuery = purchasesQuery.lte('created_at', filters.end_date)\n      returnsQuery = returnsQuery.lte('created_at', filters.end_date)\n    }\n\n    const [purchasesResult, returnsResult] = await Promise.all([\n      purchasesQuery,\n      returnsQuery\n    ])\n\n    if (purchasesResult.error) throw purchasesResult.error\n    if (returnsResult.error) throw returnsResult.error\n\n    return {\n      success: true,\n      data: {\n        purchases: purchasesResult.data,\n        returns: returnsResult.data\n      }\n    }\n  } catch (error) {\n    console.error('Error fetching supplier statement:', error)\n    return { success: false, error }\n  }\n}\n\nexport const getMedicineMovementReport = async (medicineId: string, filters: {\n  start_date?: string\n  end_date?: string\n}) => {\n  try {\n    let query = supabase\n      .from('inventory_movements')\n      .select(`\n        *,\n        medicine_batches (\n          batch_code,\n          expiry_date,\n          medicines (name, category)\n        )\n      `)\n      .order('created_at', { ascending: false })\n\n    if (filters.start_date) {\n      query = query.gte('created_at', filters.start_date)\n    }\n    if (filters.end_date) {\n      query = query.lte('created_at', filters.end_date)\n    }\n\n    const { data, error } = await query\n\n    if (error) throw error\n\n    // Filter by medicine\n    const filteredData = data.filter(movement =>\n      movement.medicine_batches?.medicines?.id === medicineId\n    )\n\n    return { success: true, data: filteredData }\n  } catch (error) {\n    console.error('Error fetching medicine movement report:', error)\n    return { success: false, error }\n  }\n}\n\nexport const getInventoryReport = async (filters: {\n  category?: string\n  low_stock?: boolean\n  expired?: boolean\n  expiring_soon?: boolean\n}) => {\n  try {\n    let query = supabase\n      .from('medicine_batches')\n      .select(`\n        *,\n        medicines (name, category, manufacturer)\n      `)\n      .order('expiry_date', { ascending: true })\n\n    const { data, error } = await query\n\n    if (error) throw error\n\n    let filteredData = data\n\n    // Filter by category\n    if (filters.category) {\n      filteredData = filteredData.filter(batch =>\n        batch.medicines?.category === filters.category\n      )\n    }\n\n    // Filter by low stock (less than 10 units)\n    if (filters.low_stock) {\n      filteredData = filteredData.filter(batch => batch.quantity < 10)\n    }\n\n    // Filter by expired\n    if (filters.expired) {\n      const today = new Date().toISOString().split('T')[0]\n      filteredData = filteredData.filter(batch => batch.expiry_date < today)\n    }\n\n    // Filter by expiring soon (within 30 days)\n    if (filters.expiring_soon) {\n      const thirtyDaysFromNow = new Date()\n      thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30)\n      const futureDate = thirtyDaysFromNow.toISOString().split('T')[0]\n      const today = new Date().toISOString().split('T')[0]\n\n      filteredData = filteredData.filter(batch =>\n        batch.expiry_date >= today && batch.expiry_date <= futureDate\n      )\n    }\n\n    return { success: true, data: filteredData }\n  } catch (error) {\n    console.error('Error fetching inventory report:', error)\n    return { success: false, error }\n  }\n}\n\n// Update return status (simplified function)\nexport const updateReturnStatus = async (returnId: string, status: string, rejectionReason?: string) => {\n  const updates: any = { status }\n  if (rejectionReason) {\n    updates.rejection_reason = rejectionReason\n  }\n  return updateReturn(returnId, updates)\n}\n\n// Get return for printing with full details\nexport const getReturnForPrint = async (returnId: string) => {\n  try {\n    console.log('🔍 البحث عن المرتجع للطباعة:', returnId)\n\n    // Check localStorage first\n    const salesReturns = JSON.parse(localStorage.getItem('sales_returns') || '[]')\n    const purchaseReturns = JSON.parse(localStorage.getItem('purchase_returns') || '[]')\n    const salesReturnItems = JSON.parse(localStorage.getItem('sales_return_items') || '[]')\n    const purchaseReturnItems = JSON.parse(localStorage.getItem('purchase_return_items') || '[]')\n\n    // Find in sales returns\n    let foundReturn = salesReturns.find((ret: any) => ret.id === returnId)\n    if (foundReturn) {\n      const items = salesReturnItems.filter((item: any) => item.return_id === returnId)\n\n      // Enhance items with medicine names from localStorage medicines data\n      const medicines = JSON.parse(localStorage.getItem('medicines') || '[]')\n      const medicineBatches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')\n\n      const enhancedItems = items.map((item: any) => {\n        let medicineName = item.medicine_name || item.medicineName || 'غير محدد'\n        let batchCode = item.batch_code || item.batchCode || ''\n        let expiryDate = item.expiry_date || item.expiryDate || ''\n\n        // Try to get medicine name from batch if not available\n        if (medicineName === 'غير محدد' && item.medicine_batch_id) {\n          const batch = medicineBatches.find((b: any) => b.id === item.medicine_batch_id)\n          if (batch) {\n            batchCode = batch.batch_number || batchCode\n            expiryDate = batch.expiry_date || expiryDate\n\n            const medicine = medicines.find((m: any) => m.id === batch.medicine_id)\n            if (medicine) {\n              medicineName = medicine.name || medicineName\n            }\n          }\n        }\n\n        // Try to get medicine name directly if still not available\n        if (medicineName === 'غير محدد' && item.medicine_id) {\n          const medicine = medicines.find((m: any) => m.id === item.medicine_id)\n          if (medicine) {\n            medicineName = medicine.name || medicineName\n          }\n        }\n\n        return {\n          ...item,\n          medicine_name: medicineName,\n          batch_code: batchCode,\n          expiry_date: expiryDate,\n          unit_price: item.unit_price || item.unitPrice || 0,\n          total_price: item.total_price || item.totalPrice || 0\n        }\n      })\n\n      const returnData = {\n        ...foundReturn,\n        type: 'sales',\n        return_type: 'sales',\n        return_invoice_items: enhancedItems\n      }\n      console.log('✅ تم العثور على مرتجع مبيعات:', returnData)\n      return { success: true, data: returnData }\n    }\n\n    // Find in purchase returns\n    foundReturn = purchaseReturns.find((ret: any) => ret.id === returnId)\n    if (foundReturn) {\n      const items = purchaseReturnItems.filter((item: any) => item.return_id === returnId)\n\n      // Enhance items with medicine names from localStorage medicines data\n      const medicines = JSON.parse(localStorage.getItem('medicines') || '[]')\n\n      const enhancedItems = items.map((item: any) => {\n        let medicineName = item.medicine_name || item.medicineName || 'غير محدد'\n\n        // Try to get medicine name directly if not available\n        if (medicineName === 'غير محدد' && item.medicine_id) {\n          const medicine = medicines.find((m: any) => m.id === item.medicine_id)\n          if (medicine) {\n            medicineName = medicine.name || medicineName\n          }\n        }\n\n        return {\n          ...item,\n          medicine_name: medicineName,\n          batch_code: item.batch_code || item.batchCode || '',\n          expiry_date: item.expiry_date || item.expiryDate || '',\n          unit_cost: item.unit_cost || item.unitCost || 0,\n          total_cost: item.total_cost || item.totalCost || 0\n        }\n      })\n\n      const returnData = {\n        ...foundReturn,\n        type: 'purchase',\n        return_type: 'purchase',\n        return_invoice_items: enhancedItems\n      }\n      console.log('✅ تم العثور على مرتجع مشتريات:', returnData)\n      return { success: true, data: returnData }\n    }\n\n    // If not found in localStorage, try Supabase\n    console.log('⚠️ لم يتم العثور على المرتجع في localStorage، محاولة Supabase...')\n\n    // Try sales returns first\n    const { data: salesReturn, error: salesError } = await supabase\n      .from('sales_returns')\n      .select(`\n        *,\n        customers (name, phone, address),\n        sales_return_items (\n          *,\n          medicine_batches (\n            batch_code,\n            expiry_date,\n            medicines (name, category, manufacturer, strength, form)\n          )\n        )\n      `)\n      .eq('id', returnId)\n      .single()\n\n    if (salesReturn && !salesError) {\n      const returnData = {\n        ...salesReturn,\n        type: 'sales',\n        return_type: 'sales',\n        return_invoice_items: (salesReturn.sales_return_items || []).map((item: any) => ({\n          ...item,\n          medicine_name: item.medicine_batches?.medicines?.name || item.medicine_name || 'غير محدد',\n          unit_price: item.unit_price || 0,\n          total_price: item.total_price || 0\n        }))\n      }\n      console.log('✅ تم العثور على مرتجع مبيعات في Supabase:', returnData)\n      return { success: true, data: returnData }\n    }\n\n    // Try purchase returns\n    const { data: purchaseReturn, error: purchaseError } = await supabase\n      .from('purchase_returns')\n      .select(`\n        *,\n        suppliers (name, contact_person, phone, address),\n        purchase_return_items (\n          *,\n          medicines (name, category, manufacturer, strength, form)\n        )\n      `)\n      .eq('id', returnId)\n      .single()\n\n    if (purchaseReturn && !purchaseError) {\n      const returnData = {\n        ...purchaseReturn,\n        type: 'purchase',\n        return_type: 'purchase',\n        return_invoice_items: (purchaseReturn.purchase_return_items || []).map((item: any) => ({\n          ...item,\n          medicine_name: item.medicines?.name || item.medicine_name || 'غير محدد',\n          unit_cost: item.unit_cost || 0,\n          total_cost: item.total_cost || 0\n        }))\n      }\n      console.log('✅ تم العثور على مرتجع مشتريات في Supabase:', returnData)\n      return { success: true, data: returnData }\n    }\n\n    console.log('❌ لم يتم العثور على المرتجع')\n    return { success: false, error: 'Return not found' }\n  } catch (error) {\n    console.error('Error fetching return for print:', error)\n    return { success: false, error }\n  }\n}\n\n// Update return status\nexport const updateReturn = async (returnId: string, updates: { status?: string, rejection_reason?: string }) => {\n  try {\n    // Update in localStorage first\n    const salesReturns = JSON.parse(localStorage.getItem('sales_returns') || '[]')\n    const purchaseReturns = JSON.parse(localStorage.getItem('purchase_returns') || '[]')\n\n    // Find and update in sales returns\n    const salesIndex = salesReturns.findIndex((ret: any) => ret.id === returnId)\n    if (salesIndex !== -1) {\n      salesReturns[salesIndex] = { ...salesReturns[salesIndex], ...updates, updated_at: new Date().toISOString() }\n      localStorage.setItem('sales_returns', JSON.stringify(salesReturns))\n\n      // Try to update in Supabase\n      try {\n        const { error } = await supabase\n          .from('sales_returns')\n          .update(updates)\n          .eq('id', returnId)\n\n        if (error) {\n          console.warn('Failed to update return in Supabase:', error)\n        }\n      } catch (supabaseError) {\n        console.warn('Supabase update failed, continuing with localStorage:', supabaseError)\n      }\n\n      return { success: true, data: salesReturns[salesIndex] }\n    }\n\n    // Find and update in purchase returns\n    const purchaseIndex = purchaseReturns.findIndex((ret: any) => ret.id === returnId)\n    if (purchaseIndex !== -1) {\n      purchaseReturns[purchaseIndex] = { ...purchaseReturns[purchaseIndex], ...updates, updated_at: new Date().toISOString() }\n      localStorage.setItem('purchase_returns', JSON.stringify(purchaseReturns))\n\n      // Try to update in Supabase\n      try {\n        const { error } = await supabase\n          .from('purchase_returns')\n          .update(updates)\n          .eq('id', returnId)\n\n        if (error) {\n          console.warn('Failed to update return in Supabase:', error)\n        }\n      } catch (supabaseError) {\n        console.warn('Supabase update failed, continuing with localStorage:', supabaseError)\n      }\n\n      return { success: true, data: purchaseReturns[purchaseIndex] }\n    }\n\n    return { success: false, error: 'Return not found' }\n  } catch (error) {\n    console.error('Error updating return:', error)\n    return { success: false, error }\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AAGO,MAAM,cAAc,OAAO;IAUhC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,aACL,MAAM,CAAC;YAAC;gBACP,MAAM,aAAa,IAAI;gBACvB,UAAU,aAAa,QAAQ;gBAC/B,cAAc,aAAa,YAAY,IAAI;gBAC3C,mBAAmB,aAAa,iBAAiB,IAAI;gBACrD,UAAU,aAAa,QAAQ,IAAI;gBACnC,MAAM,aAAa,IAAI;gBACvB,YAAY,aAAa,UAAU;gBACnC,eAAe,aAAa,aAAa;YAC3C;SAAE,EACD,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAEO,MAAM,eAAe;IAC1B,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,aACL,MAAM,CAAE,oPAaR,KAAK,CAAC;QAET,IAAI,OAAO;YACT,QAAQ,IAAI,CAAC,0DAA0D;YACvE,2BAA2B;YAC3B,OAAO;QACT;QAEA,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,iCAAiC;QACjC,OAAO;IACT;AACF;AAEA,qDAAqD;AACrD,MAAM,+BAA+B;IACnC,IAAI;QACF,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;QAClE,MAAM,UAAU,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,uBAAuB;QAEvE,sDAAsD;QACtD,IAAI,UAAU,MAAM,KAAK,GAAG;YAC1B,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;QAEA,uCAAuC;QACvC,MAAM,uBAAuB,UAAU,GAAG,CAAC,CAAC,WAAkB,CAAC;gBAC7D,GAAG,QAAQ;gBACX,kBAAkB,QAAQ,MAAM,CAAC,CAAC,QAAe,MAAM,WAAW,KAAK,SAAS,EAAE;gBAClF,SAAS,QAAQ,MAAM,CAAC,CAAC,QAAe,MAAM,WAAW,KAAK,SAAS,EAAE;YAC3E,CAAC;QAED,QAAQ,GAAG,CAAC,AAAC,cAAyC,OAA5B,qBAAqB,MAAM,EAAC;QACtD,OAAO;YAAE,SAAS;YAAM,MAAM;QAAqB;IACrD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8CAA8C;QAC5D,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAEA,kDAAkD;AAClD,MAAM,4BAA4B;IAChC,IAAI;QACF,MAAM,kBAAkB;YACtB;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,cAAc;gBACd,UAAU;gBACV,MAAM;gBACN,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,cAAc;gBACd,UAAU;gBACV,MAAM;gBACN,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,cAAc;gBACd,UAAU;gBACV,MAAM;gBACN,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,cAAc;gBACd,UAAU;gBACV,MAAM;gBACN,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,cAAc;gBACd,UAAU;gBACV,MAAM;gBACN,YAAY,IAAI,OAAO,WAAW;YACpC;SACD;QAED,MAAM,gBAAgB;YACpB;gBACE,IAAI;gBACJ,aAAa;gBACb,YAAY;gBACZ,aAAa;gBACb,UAAU;gBACV,YAAY;gBACZ,eAAe;gBACf,eAAe;gBACf,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,aAAa;gBACb,YAAY;gBACZ,aAAa;gBACb,UAAU;gBACV,YAAY;gBACZ,eAAe;gBACf,eAAe;gBACf,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,aAAa;gBACb,YAAY;gBACZ,aAAa;gBACb,UAAU;gBACV,YAAY;gBACZ,eAAe;gBACf,eAAe;gBACf,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,aAAa;gBACb,YAAY;gBACZ,aAAa;gBACb,UAAU;gBACV,YAAY;gBACZ,eAAe;gBACf,eAAe;gBACf,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,aAAa;gBACb,YAAY;gBACZ,aAAa;gBACb,UAAU;gBACV,YAAY;gBACZ,eAAe;gBACf,eAAe;gBACf,YAAY,IAAI,OAAO,WAAW;YACpC;SACD;QAED,0BAA0B;QAC1B,MAAM,kBAAkB;YACtB;gBACE,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,SAAS;gBACT,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,SAAS;gBACT,YAAY,IAAI,OAAO,WAAW;YACpC;SACD;QAED,uBAAuB;QACvB,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;QACjD,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;QACxD,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;QAEjD,uCAAuC;QACvC,aAAa,OAAO,CAAC,kBAAkB,KAAK,SAAS,CAAC,EAAE;QACxD,aAAa,OAAO,CAAC,uBAAuB,KAAK,SAAS,CAAC,EAAE;QAE7D,uCAAuC;QACvC,MAAM,uBAAuB,gBAAgB,GAAG,CAAC,CAAC,WAAkB,CAAC;gBACnE,GAAG,QAAQ;gBACX,kBAAkB,cAAc,MAAM,CAAC,CAAC,QAAe,MAAM,WAAW,KAAK,SAAS,EAAE;gBACxF,SAAS,cAAc,MAAM,CAAC,CAAC,QAAe,MAAM,WAAW,KAAK,SAAS,EAAE;YACjF,CAAC;QAED,QAAQ,GAAG,CAAC,AAAC,cAAyC,OAA5B,qBAAqB,MAAM,EAAC;QACtD,QAAQ,GAAG,CAAC,AAAC,cAAkC,OAArB,cAAc,MAAM,EAAC;QAC/C,QAAQ,GAAG,CAAC,AAAC,cAAoC,OAAvB,gBAAgB,MAAM,EAAC;QACjD,OAAO;YAAE,SAAS;YAAM,MAAM;QAAqB;IACrD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAGO,MAAM,uBAAuB;IAClC,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,8BAA8B;QAC9B,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;QAClE,MAAM,UAAU,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,uBAAuB;QAEvE,IAAI,UAAU,MAAM,KAAK,KAAK,QAAQ,MAAM,KAAK,GAAG;YAClD,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;QAEA,QAAQ,GAAG,CAAC,AAAC,+BAAwD,OAA1B,UAAU,MAAM,EAAC,WAAwB,OAAf,QAAQ,MAAM,EAAC;QACpF,OAAO;YAAE,SAAS;YAAM,MAAM;QAAU;IAC1C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAGO,MAAM,mBAAmB,OAAO;IASrC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,oBACL,MAAM,CAAC;YAAC;SAAU,EAClB,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAEO,MAAM,sBAAsB,OAAO,SAAiB;IACzD,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,oBACL,MAAM,CAAC;YAAE,UAAU;QAAY,GAC/B,EAAE,CAAC,MAAM,SACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,IAAI,CAAC,+DAA+D;YAC5E,2BAA2B;YAC3B,MAAM,kBAAkB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,uBAAuB;YAC/E,MAAM,aAAa,gBAAgB,SAAS,CAAC,CAAC,QAAe,MAAM,EAAE,KAAK;YAE1E,IAAI,eAAe,CAAC,GAAG;gBACrB,eAAe,CAAC,WAAW,CAAC,QAAQ,GAAG;gBACvC,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;gBACxD,OAAO;oBAAE,SAAS;oBAAM,MAAM,eAAe,CAAC,WAAW;gBAAC;YAC5D;YAEA,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAkC;QACpE;QAEA,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAEhD,iCAAiC;QACjC,IAAI;YACF,MAAM,kBAAkB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,uBAAuB;YAC/E,MAAM,aAAa,gBAAgB,SAAS,CAAC,CAAC,QAAe,MAAM,EAAE,KAAK;YAE1E,IAAI,eAAe,CAAC,GAAG;gBACrB,eAAe,CAAC,WAAW,CAAC,QAAQ,GAAG;gBACvC,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;gBACxD,OAAO;oBAAE,SAAS;oBAAM,MAAM,eAAe,CAAC,WAAW;gBAAC;YAC5D;YAEA,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAkB;QACpD,EAAE,OAAO,YAAY;YACnB,QAAQ,KAAK,CAAC,kDAAkD;YAChE,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAW;QAC7C;IACF;AACF;AAGO,MAAM,qBAAqB,OAAO;IAYvC,IAAI;QACF,qBAAqB;QACrB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,kBACL,MAAM,CAAC;YAAC;SAAY,EACpB,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,IAAI,CAAC,uCAAuC;YACpD,2BAA2B;YAC3B,MAAM,YAAY,AAAC,WAAwB,OAAd,KAAK,GAAG,IAAG,KAA2C,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;YAChF,MAAM,UAAU;gBACd,IAAI;gBACJ,GAAG,WAAW;gBACd,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,uBAAuB;YACvB,MAAM,mBAAmB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,qBAAqB;YAC9E,iBAAiB,IAAI,CAAC;YACtB,aAAa,OAAO,CAAC,kBAAkB,KAAK,SAAS,CAAC;YAEtD,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAQ;QACxC;QAEA,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAE/C,iCAAiC;QACjC,IAAI;YACF,MAAM,YAAY,AAAC,WAAwB,OAAd,KAAK,GAAG,IAAG,KAA2C,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;YAChF,MAAM,UAAU;gBACd,IAAI;gBACJ,GAAG,WAAW;gBACd,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,MAAM,mBAAmB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,qBAAqB;YAC9E,iBAAiB,IAAI,CAAC;YACtB,aAAa,OAAO,CAAC,kBAAkB,KAAK,SAAS,CAAC;YAEtD,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAQ;QACxC,EAAE,OAAO,YAAY;YACnB,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAW;QAC7C;IACF;AACF;AAEO,MAAM,uBAAuB,OAAO;IASzC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,uBACL,MAAM,CAAC,OACP,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,IAAI,CAAC,yDAAyD;YACtE,8DAA8D;YAC9D,QAAQ,GAAG,CAAC,6BAA6B;YAEzC,MAAM,gBAAgB,MAAM,GAAG,CAAC,CAAA;gBAC9B,6DAA6D;gBAC7D,MAAM,eAAe,KAAK,aAAa,IAAI,KAAK,YAAY;gBAE5D,IAAI,gBAAgB,iBAAiB,YAAY;oBAC/C,QAAQ,GAAG,CAAC,AAAC,iCAA6C,OAAb;oBAC7C,OAAO;wBACL,IAAI,AAAC,QAAqB,OAAd,KAAK,GAAG,IAAG,KAA2C,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;wBAC/D,GAAG,IAAI;wBACP,eAAe;wBACf,cAAc;wBACd,kBAAkB;4BAChB,YAAY;4BACZ,aAAa;4BACb,WAAW;gCACT,MAAM;gCACN,UAAU;gCACV,cAAc;gCACd,UAAU;gCACV,MAAM;4BACR;wBACF;wBACA,YAAY,IAAI,OAAO,WAAW;oBACpC;gBACF,OAAO;oBACL,QAAQ,GAAG,CAAE;oBACb,gDAAgD;oBAChD,OAAO;gBACT;YACF;YAEA,+CAA+C;YAC/C,MAAM,0BAA0B,cAAc,MAAM,CAAC,CAAA,OACnD,CAAC,KAAK,aAAa,IAAI,KAAK,aAAa,KAAK;YAGhD,IAAI,aAAa;YACjB,IAAI,wBAAwB,MAAM,GAAG,GAAG;gBACtC,QAAQ,GAAG,CAAC,AAAC,YAA0C,OAA/B,wBAAwB,MAAM,EAAC;gBACvD,MAAM,iBAAiB,MAAM,8BAA8B;gBAE3D,wCAAwC;gBACxC,aAAa,cAAc,GAAG,CAAC,CAAA;oBAC7B,IAAI,CAAC,KAAK,aAAa,IAAI,KAAK,aAAa,KAAK,YAAY;wBAC5D,MAAM,WAAW,eAAe,IAAI,CAAC,CAAA,IAAK,EAAE,iBAAiB,KAAK,KAAK,iBAAiB;wBACxF,OAAO,YAAY;oBACrB;oBACA,OAAO;gBACT;YACF;YAEA,MAAM,gBAAgB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,0BAA0B;YAChF,cAAc,IAAI,IAAI;YACtB,aAAa,OAAO,CAAC,uBAAuB,KAAK,SAAS,CAAC;YAE3D,QAAQ,GAAG,CAAC,qCAAqC;YACjD,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAW;QAC3C;QAEA,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QAEnD,iCAAiC;QACjC,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,MAAM,gBAAgB,MAAM,GAAG,CAAC,CAAA;gBAC9B,MAAM,eAAe,KAAK,aAAa,IAAI,KAAK,YAAY,IAAI;gBAEhE,OAAO;oBACL,IAAI,AAAC,QAAqB,OAAd,KAAK,GAAG,IAAG,KAA2C,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;oBAC/D,GAAG,IAAI;oBACP,eAAe;oBACf,cAAc;oBACd,kBAAkB;wBAChB,YAAY;wBACZ,aAAa;wBACb,WAAW;4BACT,MAAM;4BACN,UAAU;4BACV,cAAc;4BACd,UAAU;4BACV,MAAM;wBACR;oBACF;oBACA,YAAY,IAAI,OAAO,WAAW;gBACpC;YACF;YAEA,MAAM,gBAAgB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,0BAA0B;YAChF,cAAc,IAAI,IAAI;YACtB,aAAa,OAAO,CAAC,uBAAuB,KAAK,SAAS,CAAC;YAE3D,QAAQ,GAAG,CAAC,sCAAsC;YAClD,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAc;QAC9C,EAAE,OAAO,YAAY;YACnB,QAAQ,KAAK,CAAC,2CAA2C;YACzD,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAW;QAC7C;IACF;AACF;AAEA,uDAAuD;AACvD,MAAM,gCAAgC,OAAO;IAC3C,IAAI;QACF,8BAA8B;QAC9B,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;QAClE,MAAM,UAAU,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,uBAAuB;QAEvE,OAAO,MAAM,GAAG,CAAC,CAAA;YACf,qEAAqE;YACrE,IAAI,eAAe,KAAK,aAAa,IAAI,KAAK,YAAY;YAE1D,IAAI,CAAC,gBAAgB,iBAAiB,YAAY;gBAChD,gCAAgC;gBAChC,MAAM,QAAQ,QAAQ,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,KAAK,iBAAiB;gBACtE,MAAM,WAAW,UAAU,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,MAAK,kBAAA,4BAAA,MAAO,WAAW;gBACvE,eAAe,CAAA,qBAAA,+BAAA,SAAU,IAAI,KAAI;YACnC;YAEA,MAAM,QAAQ,QAAQ,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,KAAK,iBAAiB;YACtE,MAAM,WAAW,UAAU,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,MAAK,kBAAA,4BAAA,MAAO,WAAW;YAEvE,OAAO;gBACL,IAAI,AAAC,QAAqB,OAAd,KAAK,GAAG,IAAG,KAA2C,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;gBAC/D,GAAG,IAAI;gBACP,eAAe;gBACf,cAAc;gBACd,kBAAkB;oBAChB,YAAY,CAAA,kBAAA,4BAAA,MAAO,UAAU,KAAI;oBACjC,aAAa,CAAA,kBAAA,4BAAA,MAAO,WAAW,KAAI;oBACnC,WAAW;wBACT,MAAM;wBACN,UAAU,CAAA,qBAAA,+BAAA,SAAU,QAAQ,KAAI;wBAChC,cAAc,CAAA,qBAAA,+BAAA,SAAU,YAAY,KAAI;wBACxC,UAAU,CAAA,qBAAA,+BAAA,SAAU,QAAQ,KAAI;wBAChC,MAAM,CAAA,qBAAA,+BAAA,SAAU,IAAI,KAAI;oBAC1B;gBACF;gBACA,YAAY,IAAI,OAAO,WAAW;YACpC;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wCAAwC;QACtD,yDAAyD;QACzD,OAAO,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACxB,IAAI,AAAC,QAAqB,OAAd,KAAK,GAAG,IAAG,KAA2C,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;gBAC/D,GAAG,IAAI;gBACP,eAAe;gBACf,YAAY,IAAI,OAAO,WAAW;YACpC,CAAC;IACH;AACF;AAGO,MAAM,8BAA8B;IACzC,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,MAAM,aAAa,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,0BAA0B;QAC7E,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;QAClE,MAAM,UAAU,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,uBAAuB;QAEvE,QAAQ,GAAG,CAAC,AAAC,0BAA2C,OAAlB,WAAW,MAAM;QACvD,QAAQ,GAAG,CAAC,AAAC,mBAAmC,OAAjB,UAAU,MAAM;QAC/C,QAAQ,GAAG,CAAC,AAAC,mBAAiC,OAAf,QAAQ,MAAM;QAE7C,IAAI,aAAa;QACjB,IAAI,gBAAgB;QAEpB,MAAM,aAAa,WAAW,GAAG,CAAC,CAAC;gBAE7B,kCAAA;YADJ,qDAAqD;YACrD,IAAI,EAAA,yBAAA,KAAK,gBAAgB,cAArB,8CAAA,mCAAA,uBAAuB,SAAS,cAAhC,uDAAA,iCAAkC,IAAI,KAAI,KAAK,gBAAgB,CAAC,SAAS,CAAC,IAAI,KAAK,YAAY;gBACjG,OAAO;YACT;YAEA,gCAAgC;YAChC,MAAM,QAAQ,QAAQ,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,KAAK,iBAAiB;YACtE,MAAM,WAAW,UAAU,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,MAAK,kBAAA,4BAAA,MAAO,WAAW;YAEvE,IAAI,qBAAA,+BAAA,SAAU,IAAI,EAAE;gBAClB;gBACA,QAAQ,GAAG,CAAC,AAnmBpB,AAmmBqB,0BAAkB,SAAS,IAAI,EAAC,aAA6B,OAAlB,kBAAA,4BAAA,MAAO,UAAU,EAAC;gBAE1E,OAAO;oBACL,GAAG,IAAI;oBACP,eAAe,SAAS,IAAI;oBAC5B,cAAc,SAAS,IAAI;oBAC3B,kBAAkB;wBAChB,YAAY,CAAA,kBAAA,4BAAA,MAAO,UAAU,KAAI,KAAK,UAAU,IAAI;wBACpD,aAAa,CAAA,kBAAA,4BAAA,MAAO,WAAW,KAAI,KAAK,WAAW,IAAI;wBACvD,WAAW;4BACT,MAAM,SAAS,IAAI;4BACnB,UAAU,SAAS,QAAQ,IAAI;4BAC/B,cAAc,SAAS,YAAY,IAAI;4BACvC,UAAU,SAAS,QAAQ,IAAI;4BAC/B,MAAM,SAAS,IAAI,IAAI;wBACzB;oBACF;gBACF;YACF,OAAO;gBACL;gBACA,QAAQ,GAAG,CAAC,AAAC,uCAA6D,OAAvB,KAAK,iBAAiB;gBAEzE,oCAAoC;gBACpC,MAAM,eAAe,KAAK,aAAa,IAAI,KAAK,YAAY,IAAI;gBAChE,OAAO;oBACL,GAAG,IAAI;oBACP,eAAe;oBACf,cAAc;oBACd,kBAAkB;wBAChB,YAAY,CAAA,kBAAA,4BAAA,MAAO,UAAU,KAAI,KAAK,UAAU,IAAI;wBACpD,aAAa,CAAA,kBAAA,4BAAA,MAAO,WAAW,KAAI,KAAK,WAAW,IAAI;wBACvD,WAAW;4BACT,MAAM;4BACN,UAAU;4BACV,cAAc;4BACd,UAAU;4BACV,MAAM;wBACR;oBACF;gBACF;YACF;QACF;QAEA,uCAAuC;QACvC,aAAa,OAAO,CAAC,uBAAuB,KAAK,SAAS,CAAC;QAE3D,QAAQ,GAAG,CAAC,AAAC,cAAuC,OAA1B,YAAW,iBAAiC,OAAlB,WAAW,MAAM;QACrE,QAAQ,GAAG,CAAC,AAAC,wBAAqC,OAAd,eAAc;QAElD,OAAO;YACL,SAAS;YACT;YACA;YACA,YAAY,WAAW,MAAM;QAC/B;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAGO,MAAM,2BAA2B,CAAC;IACvC,IAAI;QACF,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;QAClE,MAAM,UAAU,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,uBAAuB;QAEvE,MAAM,QAAQ,QAAQ,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK;QAChD,MAAM,WAAW,UAAU,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,MAAK,kBAAA,4BAAA,MAAO,WAAW;QAEvE,OAAO,CAAA,qBAAA,+BAAA,SAAU,IAAI,KAAI;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO;IACT;AACF;AAGO,MAAM,wBAAwB,OAAO;IAW1C,IAAI;QACF,qBAAqB;QACrB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,qBACL,MAAM,CAAC;YAAC;SAAY,EACpB,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,IAAI,CAAC,4DAA4D;YACzE,2BAA2B;YAC3B,MAAM,YAAY,AAAC,oBAAiC,OAAd,KAAK,GAAG,IAAG,KAA2C,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;YACzF,MAAM,UAAU;gBACd,IAAI;gBACJ,GAAG,WAAW;gBACd,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,uBAAuB;YACvB,MAAM,mBAAmB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,wBAAwB;YACjF,iBAAiB,IAAI,CAAC;YACtB,aAAa,OAAO,CAAC,qBAAqB,KAAK,SAAS,CAAC;YAEzD,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAQ;QACxC;QAEA,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAElD,iCAAiC;QACjC,IAAI;YACF,MAAM,YAAY,AAAC,oBAAiC,OAAd,KAAK,GAAG,IAAG,KAA2C,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;YACzF,MAAM,UAAU;gBACd,IAAI;gBACJ,GAAG,WAAW;gBACd,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,MAAM,mBAAmB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,wBAAwB;YACjF,iBAAiB,IAAI,CAAC;YACtB,aAAa,OAAO,CAAC,qBAAqB,KAAK,SAAS,CAAC;YAEzD,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAQ;QACxC,EAAE,OAAO,YAAY;YACnB,QAAQ,KAAK,CAAC,sDAAsD;YACpE,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAW;QAC7C;IACF;AACF;AAEO,MAAM,0BAA0B,OAAO;IAU5C,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,0BACL,MAAM,CAAC,OACP,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,IAAI,CAAC,kEAAkE;YAC/E,2BAA2B;YAC3B,MAAM,gBAAgB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,6BAA6B;YACnF,MAAM,WAAW,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAClC,IAAI,AAAC,iBAA8B,OAAd,KAAK,GAAG,IAAG,KAA2C,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;oBACxE,GAAG,IAAI;oBACP,eAAe,KAAK,aAAa,IAAI;oBACrC,cAAc,KAAK,aAAa,IAAI;oBACpC,YAAY,IAAI,OAAO,WAAW;gBACpC,CAAC;YAED,cAAc,IAAI,IAAI;YACtB,aAAa,OAAO,CAAC,0BAA0B,KAAK,SAAS,CAAC;YAE9D,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAS;QACzC;QAEA,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wCAAwC;QAEtD,iCAAiC;QACjC,IAAI;YACF,MAAM,gBAAgB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,6BAA6B;YACnF,MAAM,WAAW,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAClC,IAAI,AAAC,iBAA8B,OAAd,KAAK,GAAG,IAAG,KAA2C,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;oBACxE,GAAG,IAAI;oBACP,eAAe,KAAK,aAAa,IAAI;oBACrC,cAAc,KAAK,aAAa,IAAI;oBACpC,YAAY,IAAI,OAAO,WAAW;gBACpC,CAAC;YAED,cAAc,IAAI,IAAI;YACtB,aAAa,OAAO,CAAC,0BAA0B,KAAK,SAAS,CAAC;YAE9D,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAS;QACzC,EAAE,OAAO,YAAY;YACnB,QAAQ,KAAK,CAAC,oDAAoD;YAClE,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAW;QAC7C;IACF;AACF;AAGO,MAAM,uBAAuB,OAAO;IAQzC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,uBACL,MAAM,CAAC;YAAC;SAAa,EACrB,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,IAAI,CAAC,8DAA8D;YAC3E,2BAA2B;YAC3B,MAAM,WAAW;gBACf,IAAI,AAAC,YAAyB,OAAd,KAAK,GAAG,IAAG,KAA2C,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;gBACnE,GAAG,YAAY;gBACf,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,MAAM,oBAAoB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,0BAA0B;YACpF,kBAAkB,IAAI,CAAC;YACvB,aAAa,OAAO,CAAC,uBAAuB,KAAK,SAAS,CAAC;YAE3D,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAS;QACzC;QAEA,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAElD,iCAAiC;QACjC,IAAI;YACF,MAAM,WAAW;gBACf,IAAI,AAAC,YAAyB,OAAd,KAAK,GAAG,IAAG,KAA2C,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;gBACnE,GAAG,YAAY;gBACf,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,MAAM,oBAAoB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,0BAA0B;YACpF,kBAAkB,IAAI,CAAC;YACvB,aAAa,OAAO,CAAC,uBAAuB,KAAK,SAAS,CAAC;YAE3D,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAS;QACzC,EAAE,OAAO,YAAY;YACnB,QAAQ,KAAK,CAAC,wDAAwD;YACtE,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAW;QAC7C;IACF;AACF;AAGO,MAAM,eAAe;IAC1B,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,aACL,MAAM,CAAC,KACP,KAAK,CAAC;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAEO,MAAM,cAAc,OAAO;IAOhC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,aACL,MAAM,CAAC;YAAC;SAAa,EACrB,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAGO,MAAM,eAAe;IAC1B,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,aACL,MAAM,CAAC,KACP,KAAK,CAAC;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAEO,MAAM,cAAc,OAAO;IAQhC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,aACL,MAAM,CAAC;YAAC;SAAa,EACrB,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAGO,MAAM,2BAA2B,OACtC,aACA;IAEA,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,uBAAuB;QACnC,QAAQ,GAAG,CAAC,eAAe;QAE3B,wCAAwC;QACxC,QAAQ,GAAG,CAAC;QACZ,MAAM,gBAAgB,MAAM,mBAAmB;QAC/C,QAAQ,GAAG,CAAC,4BAA4B;QAExC,IAAI,CAAC,cAAc,OAAO,EAAE;gBAEgB;YAD1C,QAAQ,KAAK,CAAC,4BAA4B,cAAc,KAAK;YAC7D,MAAM,IAAI,MAAM,AAAC,0BAAyE,OAAhD,EAAA,uBAAA,cAAc,KAAK,cAAnB,2CAAA,qBAAqB,OAAO,KAAI;QAC5E;QAEA,MAAM,YAAY,cAAc,IAAI,CAAC,EAAE;QACvC,QAAQ,GAAG,CAAC,kCAAkC;QAE9C,oBAAoB;QACpB,QAAQ,GAAG,CAAC;QACZ,MAAM,aAAa,EAAE;QAErB,KAAK,MAAM,QAAQ,MAAO;YACxB,QAAQ,GAAG,CAAC,qBAAqB;YACjC,MAAM,UAAU,KAAK,iBAAiB,IAAI,KAAK,OAAO;YAEtD,mDAAmD;YACnD,WAAW,IAAI,CAAC;gBACd,YAAY;gBACZ,mBAAmB;gBACnB,UAAU,KAAK,QAAQ;gBACvB,YAAY,KAAK,UAAU,IAAI,KAAK,SAAS;gBAC7C,aAAa,KAAK,WAAW,IAAI,KAAK,UAAU;gBAChD,SAAS,KAAK,OAAO,IAAI,KAAK,MAAM,IAAI;gBACxC,eAAe,KAAK,aAAa,IAAI,KAAK,YAAY,IAAI;YAC5D;YAEA,kDAAkD;YAClD,IAAI,CAAC,CAAC,KAAK,OAAO,IAAI,KAAK,MAAM,GAAG;gBAClC,IAAI;oBACF,MAAM,eAAe,MAAM,yHAAA,CAAA,WAAQ,CAChC,IAAI,CAAC,oBACL,MAAM,CAAC,YACP,EAAE,CAAC,MAAM,SACT,MAAM;oBAET,IAAI,aAAa,IAAI,EAAE;wBACrB,MAAM,cAAc,KAAK,GAAG,CAAC,GAAG,aAAa,IAAI,CAAC,QAAQ,GAAG,KAAK,QAAQ;wBAC1E,MAAM,oBAAoB,SAAS;wBACnC,QAAQ,GAAG,CAAC,AAAC,0BAAwC,OAAf,SAAQ,SAAmB,OAAZ;oBACvD;gBACF,EAAE,OAAO,YAAY;oBACnB,QAAQ,IAAI,CAAC,oCAAoC;gBACnD;YACF;YAEA,yBAAyB;YACzB,IAAI;gBACF,MAAM,qBAAqB;oBACzB,mBAAmB;oBACnB,eAAe;oBACf,UAAU,KAAK,QAAQ;oBACvB,gBAAgB;oBAChB,cAAc;oBACd,OAAO,AAAC,KAAK,OAAO,IAAI,KAAK,MAAM,GAAI,SAAS;gBAClD;gBACA,QAAQ,GAAG,CAAC,AAAC,kCAAyC,OAAR;YAChD,EAAE,OAAO,eAAe;gBACtB,QAAQ,IAAI,CAAC,qCAAqC;YACpD;QACF;QAEA,iCAAiC;QACjC,QAAQ,GAAG,CAAC;QACZ,MAAM,cAAc,MAAM,qBAAqB;QAC/C,IAAI,CAAC,YAAY,OAAO,EAAE;YACxB,QAAQ,IAAI,CAAC,uCAAuC,YAAY,KAAK;QACvE,OAAO;YACL,QAAQ,GAAG,CAAC;QACd;QAEA,0CAA0C;QAC1C,IAAI,YAAY,cAAc,KAAK,UAAU,YAAY,cAAc,KAAK,QAAQ;YAClF,IAAI;gBACF,MAAM,mBAAmB;oBACvB,kBAAkB;oBAClB,UAAU;oBACV,QAAQ,YAAY,YAAY;oBAChC,aAAa,AAAC,qBAA+C,OAA3B,YAAY,cAAc;oBAC5D,gBAAgB;oBAChB,cAAc;oBACd,gBAAgB;oBAChB,OAAO,YAAY,KAAK;gBAC1B;gBACA,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,WAAW;gBAClB,QAAQ,IAAI,CAAC,uCAAuC;YACtD;QACF;QAEA,QAAQ,GAAG,CAAC;QACZ,OAAO;YAAE,SAAS;YAAM,MAAM;gBAAE;YAAU;QAAE;IAC9C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAGO,MAAM,mBAAmB;IAC9B,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,kBACL,MAAM,CAAE,+RAYR,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO;YACT,QAAQ,IAAI,CAAC,mEAAmE;YAChF,2BAA2B;YAC3B,MAAM,gBAAgB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,qBAAqB;YAC3E,MAAM,aAAa,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,0BAA0B;YAC7E,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;YAClE,MAAM,UAAU,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,uBAAuB;YAEvE,4EAA4E;YAC5E,MAAM,oBAAoB,cAAc,GAAG,CAAC,CAAC;gBAC3C,MAAM,QAAQ,WAAW,MAAM,CAAC,CAAC,OAAc,KAAK,UAAU,KAAK,QAAQ,EAAE;gBAE7E,2DAA2D;gBAC3D,MAAM,gBAAgB,MAAM,GAAG,CAAC,CAAC;wBAC3B,kCAAA;oBAAJ,KAAI,yBAAA,KAAK,gBAAgB,cAArB,8CAAA,mCAAA,uBAAuB,SAAS,cAAhC,uDAAA,iCAAkC,IAAI,EAAE;wBAC1C,OAAO,KAAK,4BAA4B;;oBAC1C;oBAEA,gCAAgC;oBAChC,MAAM,QAAQ,QAAQ,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,KAAK,iBAAiB;oBACtE,MAAM,WAAW,UAAU,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,MAAK,kBAAA,4BAAA,MAAO,WAAW;oBAEvE,OAAO;wBACL,GAAG,IAAI;wBACP,eAAe,CAAA,qBAAA,+BAAA,SAAU,IAAI,KAAI,KAAK,aAAa,IAAI;wBACvD,kBAAkB;4BAChB,YAAY,CAAA,kBAAA,4BAAA,MAAO,UAAU,KAAI;4BACjC,aAAa,CAAA,kBAAA,4BAAA,MAAO,WAAW,KAAI;4BACnC,WAAW;gCACT,MAAM,CAAA,qBAAA,+BAAA,SAAU,IAAI,KAAI,KAAK,aAAa,IAAI;gCAC9C,UAAU,CAAA,qBAAA,+BAAA,SAAU,QAAQ,KAAI;gCAChC,cAAc,CAAA,qBAAA,+BAAA,SAAU,YAAY,KAAI;gCACxC,UAAU,CAAA,qBAAA,+BAAA,SAAU,QAAQ,KAAI;gCAChC,MAAM,CAAA,qBAAA,+BAAA,SAAU,IAAI,KAAI;4BAC1B;wBACF;oBACF;gBACF;gBAEA,OAAO;oBACL,GAAG,OAAO;oBACV,qBAAqB;gBACvB;YACF;YAEA,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAkB;QAClD;QAEA,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAEhD,iCAAiC;QACjC,IAAI;YACF,MAAM,gBAAgB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,qBAAqB;YAC3E,MAAM,aAAa,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,0BAA0B;YAC7E,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;YAClE,MAAM,UAAU,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,uBAAuB;YAEvE,8DAA8D;YAC9D,MAAM,oBAAoB,cAAc,GAAG,CAAC,CAAC;gBAC3C,MAAM,QAAQ,WAAW,MAAM,CAAC,CAAC,OAAc,KAAK,UAAU,KAAK,QAAQ,EAAE;gBAE7E,2DAA2D;gBAC3D,MAAM,gBAAgB,MAAM,GAAG,CAAC,CAAC;wBAC3B,kCAAA;oBAAJ,KAAI,yBAAA,KAAK,gBAAgB,cAArB,8CAAA,mCAAA,uBAAuB,SAAS,cAAhC,uDAAA,iCAAkC,IAAI,EAAE;wBAC1C,OAAO,KAAK,4BAA4B;;oBAC1C;oBAEA,gCAAgC;oBAChC,MAAM,QAAQ,QAAQ,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,KAAK,iBAAiB;oBACtE,MAAM,WAAW,UAAU,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,MAAK,kBAAA,4BAAA,MAAO,WAAW;oBAEvE,OAAO;wBACL,GAAG,IAAI;wBACP,eAAe,CAAA,qBAAA,+BAAA,SAAU,IAAI,KAAI,KAAK,aAAa,IAAI;wBACvD,kBAAkB;4BAChB,YAAY,CAAA,kBAAA,4BAAA,MAAO,UAAU,KAAI;4BACjC,aAAa,CAAA,kBAAA,4BAAA,MAAO,WAAW,KAAI;4BACnC,WAAW;gCACT,MAAM,CAAA,qBAAA,+BAAA,SAAU,IAAI,KAAI,KAAK,aAAa,IAAI;gCAC9C,UAAU,CAAA,qBAAA,+BAAA,SAAU,QAAQ,KAAI;gCAChC,cAAc,CAAA,qBAAA,+BAAA,SAAU,YAAY,KAAI;gCACxC,UAAU,CAAA,qBAAA,+BAAA,SAAU,QAAQ,KAAI;gCAChC,MAAM,CAAA,qBAAA,+BAAA,SAAU,IAAI,KAAI;4BAC1B;wBACF;oBACF;gBACF;gBAEA,OAAO;oBACL,GAAG,OAAO;oBACV,qBAAqB;gBACvB;YACF;YAEA,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAkB;QAClD,EAAE,OAAO,YAAY;YACnB,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO;gBAAE,SAAS;gBAAO;YAAM;QACjC;IACF;AACF;AAGO,MAAM,0BAA0B,OAAO;IAC5C,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,kBACL,MAAM,CAAE,+RAYR,EAAE,CAAC,MAAM,WACT,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,IAAI,CAAC,mEAAmE;YAChF,2BAA2B;YAC3B,MAAM,gBAAgB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,qBAAqB;YAC3E,MAAM,aAAa,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,0BAA0B;YAC7E,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;YAClE,MAAM,UAAU,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,uBAAuB;YAEvE,MAAM,UAAU,cAAc,IAAI,CAAC,CAAC,MAAa,IAAI,EAAE,KAAK;YAC5D,IAAI,SAAS;gBACX,MAAM,QAAQ,WAAW,MAAM,CAAC,CAAC,OAAc,KAAK,UAAU,KAAK;gBAEnE,oDAAoD;gBACpD,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC,mBAAmB,MAAM,MAAM;gBAC3C,QAAQ,GAAG,CAAC,2BAA2B,UAAU,MAAM;gBACvD,QAAQ,GAAG,CAAC,2BAA2B,QAAQ,MAAM;gBAErD,MAAM,iBAAiB,MAAM,GAAG,CAAC,CAAC,MAAW;oBAC3C,QAAQ,GAAG,CAAC,AAAC,gBAAyB,OAAV,QAAQ,GAAE;oBACtC,QAAQ,GAAG,CAAC,qBAAqB;oBAEjC,qDAAqD;oBACrD,MAAM,QAAQ,QAAQ,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,KAAK,iBAAiB;oBACtE,QAAQ,GAAG,CAAC,oBAAoB;oBAEhC,MAAM,WAAW,UAAU,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,MAAK,kBAAA,4BAAA,MAAO,WAAW;oBACvE,QAAQ,GAAG,CAAC,mBAAmB;oBAE/B,uCAAuC;oBACvC,MAAM,eAAe,CAAA,qBAAA,+BAAA,SAAU,IAAI,KAAI;oBACvC,QAAQ,GAAG,CAAC,uBAAuB;oBAEnC,MAAM,eAAe;wBACnB,GAAG,IAAI;wBACP,eAAe;wBACf,cAAc;wBACd,kBAAkB;4BAChB,EAAE,EAAE,kBAAA,4BAAA,MAAO,EAAE;4BACb,YAAY,CAAA,kBAAA,4BAAA,MAAO,UAAU,KAAI,KAAK,UAAU,IAAI;4BACpD,aAAa,CAAA,kBAAA,4BAAA,MAAO,WAAW,KAAI,KAAK,WAAW,IAAI;4BACvD,WAAW,EAAE,kBAAA,4BAAA,MAAO,WAAW;4BAC/B,WAAW;gCACT,EAAE,EAAE,qBAAA,+BAAA,SAAU,EAAE;gCAChB,MAAM;gCACN,UAAU,CAAA,qBAAA,+BAAA,SAAU,QAAQ,KAAI;gCAChC,cAAc,CAAA,qBAAA,+BAAA,SAAU,YAAY,KAAI;gCACxC,UAAU,CAAA,qBAAA,+BAAA,SAAU,QAAQ,KAAI;gCAChC,MAAM,CAAA,qBAAA,+BAAA,SAAU,IAAI,KAAI;4BAC1B;wBACF;oBACF;oBAEA,QAAQ,GAAG,CAAC,kBAAkB;oBAC9B,OAAO;gBACT;gBAEA,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC,qBAAqB;gBAEjC,OAAO;oBACL,SAAS;oBACT,MAAM;wBACJ,GAAG,OAAO;wBACV,qBAAqB;oBACvB;gBACF;YACF;QACF;QAEA,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2CAA2C;QACzD,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAGO,MAAM,6BAA6B,OAAO;IAC/C,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,qBACL,MAAM,CAAE,kNAQR,EAAE,CAAC,MAAM,WACT,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,IAAI,CAAC,4EAA4E;YACzF,2BAA2B;YAC3B,MAAM,gBAAgB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,wBAAwB;YAC9E,MAAM,aAAa,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,6BAA6B;YAEhF,MAAM,UAAU,cAAc,IAAI,CAAC,CAAC,MAAa,IAAI,EAAE,KAAK;YAC5D,IAAI,SAAS;gBACX,MAAM,QAAQ,WAAW,MAAM,CAAC,CAAC,OAAc,KAAK,UAAU,KAAK;gBAEnE,oCAAoC;gBACpC,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC,mBAAmB,MAAM,MAAM;gBAE3C,MAAM,iBAAiB,MAAM,GAAG,CAAC,CAAC,MAAW;oBAC3C,QAAQ,GAAG,CAAC,AAAC,gBAAyB,OAAV,QAAQ,GAAE;oBACtC,QAAQ,GAAG,CAAC,qBAAqB;oBAEjC,0CAA0C;oBAC1C,MAAM,eAAe,KAAK,aAAa,IAAI,KAAK,YAAY,IAAI;oBAChE,QAAQ,GAAG,CAAC,eAAe;oBAE3B,MAAM,eAAe;wBACnB,GAAG,IAAI;wBACP,eAAe;wBACf,cAAc;wBACd,WAAW;4BACT,MAAM;4BACN,UAAU,KAAK,QAAQ,IAAI;4BAC3B,cAAc,KAAK,YAAY,IAAI;4BACnC,UAAU,KAAK,QAAQ,IAAI;4BAC3B,MAAM,KAAK,IAAI,IAAI;wBACrB;oBACF;oBAEA,QAAQ,GAAG,CAAC,kBAAkB;oBAC9B,OAAO;gBACT;gBAEA,QAAQ,GAAG,CAAC;gBAEZ,OAAO;oBACL,SAAS;oBACT,MAAM;wBACJ,GAAG,OAAO;wBACV,wBAAwB;oBAC1B;gBACF;YACF;QACF;QAEA,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8CAA8C;QAC5D,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAEO,MAAM,sBAAsB;IACjC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,qBACL,MAAM,CAAE,kNAQR,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO;YACT,QAAQ,IAAI,CAAC,sEAAsE;YACnF,2BAA2B;YAC3B,MAAM,gBAAgB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,wBAAwB;YAC9E,MAAM,aAAa,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,6BAA6B;YAEhF,oCAAoC;YACpC,MAAM,oBAAoB,cAAc,GAAG,CAAC,CAAC,UAAiB,CAAC;oBAC7D,GAAG,OAAO;oBACV,wBAAwB,WAAW,MAAM,CAAC,CAAC,OAAc,KAAK,UAAU,KAAK,QAAQ,EAAE;gBACzF,CAAC;YAED,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAkB;QAClD;QAEA,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QAEnD,iCAAiC;QACjC,IAAI;YACF,MAAM,gBAAgB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,wBAAwB;YAC9E,MAAM,aAAa,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,6BAA6B;YAEhF,oCAAoC;YACpC,MAAM,oBAAoB,cAAc,GAAG,CAAC,CAAC,UAAiB,CAAC;oBAC7D,GAAG,OAAO;oBACV,wBAAwB,WAAW,MAAM,CAAC,CAAC,OAAc,KAAK,UAAU,KAAK,QAAQ,EAAE;gBACzF,CAAC;YAED,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAkB;QAClD,EAAE,OAAO,YAAY;YACnB,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO;gBAAE,SAAS;gBAAO;YAAM;QACjC;IACF;AACF;AAEO,MAAM,oBAAoB,OAAO;IAUtC,IAAI;QACF,qBAAqB;QACrB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,iBACL,MAAM,CAAC;YAAC;SAAW,EACnB,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,8DAA8D;QAE3E,2BAA2B;QAC3B,IAAI;YACF,MAAM,eAAe;gBACnB,GAAG,UAAU;gBACb,IAAI,AAAC,MAAmB,OAAd,KAAK,GAAG,IAAG,KAA2C,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;gBAC7D,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,MAAM,kBAAkB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,oBAAoB;YAC5E,gBAAgB,IAAI,CAAC;YACrB,aAAa,OAAO,CAAC,iBAAiB,KAAK,SAAS,CAAC;YAErD,QAAQ,GAAG,CAAC,uCAAuC;YACnD,QAAQ,GAAG,CAAC,wCAAwC,gBAAgB,MAAM;YAE1E,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAa;QAC7C,EAAE,OAAO,eAAe;YACtB,QAAQ,KAAK,CAAC,2CAA2C;YACzD,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAc;QAChD;IACF;AACF;AAEO,MAAM,uBAAuB,OAAO;IASzC,IAAI;QACF,qBAAqB;QACrB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,oBACL,MAAM,CAAC;YAAC;SAAW,EACnB,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,iEAAiE;QAE9E,2BAA2B;QAC3B,IAAI;YACF,MAAM,eAAe;gBACnB,GAAG,UAAU;gBACb,IAAI,AAAC,MAAmB,OAAd,KAAK,GAAG,IAAG,KAA2C,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;gBAC7D,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,MAAM,kBAAkB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,uBAAuB;YAC/E,gBAAgB,IAAI,CAAC;YACrB,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;YAExD,QAAQ,GAAG,CAAC,0CAA0C;YACtD,QAAQ,GAAG,CAAC,2CAA2C,gBAAgB,MAAM;YAE7E,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAa;QAC7C,EAAE,OAAO,eAAe;YACtB,QAAQ,KAAK,CAAC,8CAA8C;YAC5D,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAc;QAChD;IACF;AACF;AAEO,MAAM,iBAAiB,OAAO;IASnC,IAAI;YAEgB;QADlB,qBAAqB;QACrB,MAAM,YAAY,EAAA,UAAA,KAAK,CAAC,EAAE,cAAR,8BAAA,QAAU,WAAW,MAAK,UAAU,uBAAuB;QAC7E,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,WACL,MAAM,CAAC,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACzB,WAAW,KAAK,SAAS;gBACzB,mBAAmB,KAAK,iBAAiB;gBACzC,aAAa,KAAK,WAAW;gBAC7B,UAAU,KAAK,QAAQ;gBACvB,YAAY,KAAK,UAAU;gBAC3B,aAAa,KAAK,WAAW;YAC/B,CAAC,IACA,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,8DAA8D;QAE3E,2BAA2B;QAC3B,IAAI;gBACiB;YAAnB,MAAM,aAAa,EAAA,WAAA,KAAK,CAAC,EAAE,cAAR,+BAAA,SAAU,WAAW,MAAK,UAAU,uBAAuB;YAC9E,MAAM,eAAe,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;oBACtC,GAAG,IAAI;oBACP,IAAI,AAAC,MAAmB,OAAd,KAAK,GAAG,IAAG,KAA2C,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;oBAC7D,YAAY,IAAI,OAAO,WAAW;gBACpC,CAAC;YAED,MAAM,gBAAgB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,eAAe;YACrE,cAAc,IAAI,IAAI;YACtB,aAAa,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC;YAEhD,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAa;QAC7C,EAAE,OAAO,eAAe;YACtB,QAAQ,KAAK,CAAC,yCAAyC;YACvD,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAc;QAChD;IACF;AACF;AAEO,MAAM,gBAAgB,OAC3B,YACA,YACA;IAEA,IAAI;QACF,uBAAuB;QACvB,MAAM,eAAe,eAAe,UAChC,MAAM,kBAAkB,cACxB,MAAM,qBAAqB;QAE/B,IAAI,CAAC,aAAa,OAAO,EAAE,MAAM,IAAI,MAAM;QAE3C,MAAM,WAAW,aAAa,IAAI,CAAC,EAAE;QAErC,mBAAmB;QACnB,MAAM,cAAc,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACrC,WAAW;gBACX,aAAa;gBACb,mBAAmB,KAAK,OAAO;gBAC/B,aAAa,KAAK,UAAU;gBAC5B,UAAU,KAAK,QAAQ;gBACvB,YAAY,KAAK,SAAS;gBAC1B,aAAa,KAAK,UAAU;YAC9B,CAAC;QAED,MAAM,eAAe;QAErB,8DAA8D;QAC9D,IAAI;YACF,yDAAyD;YACzD,IAAI,eAAe,SAAS;gBAC1B,KAAK,MAAM,QAAQ,MAAO;oBACxB,IAAI,KAAK,OAAO,EAAE;wBAChB,IAAI;4BACF,6BAA6B;4BAC7B,MAAM,EAAE,MAAM,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,oBACL,MAAM,CAAC,YACP,EAAE,CAAC,MAAM,KAAK,OAAO,EACrB,MAAM;4BAET,IAAI,OAAO;gCACT,sCAAsC;gCACtC,MAAM,oBAAoB,KAAK,OAAO,EAAE,MAAM,QAAQ,GAAG,KAAK,QAAQ;4BACxE;4BAEA,yBAAyB;4BACzB,MAAM,qBAAqB;gCACzB,mBAAmB,KAAK,OAAO;gCAC/B,eAAe;gCACf,UAAU,KAAK,QAAQ;gCACvB,gBAAgB;gCAChB,cAAc;gCACd,OAAO,AAAC,kBAAmC,OAAlB,WAAW,MAAM;4BAC5C;wBACF,EAAE,OAAO,gBAAgB;4BACvB,QAAQ,IAAI,CAAC,wCAAwC,KAAK,OAAO,EAAE;wBACrE;oBACF;gBACF;YACF;YAEA,4DAA4D;YAC5D,IAAI,eAAe,YAAY;gBAC7B,KAAK,MAAM,QAAQ,MAAO;oBACxB,IAAI,KAAK,OAAO,EAAE;wBAChB,IAAI;4BACF,6BAA6B;4BAC7B,MAAM,EAAE,MAAM,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,oBACL,MAAM,CAAC,YACP,EAAE,CAAC,MAAM,KAAK,OAAO,EACrB,MAAM;4BAET,IAAI,OAAO;gCACT,sCAAsC;gCACtC,MAAM,cAAc,KAAK,GAAG,CAAC,GAAG,MAAM,QAAQ,GAAG,KAAK,QAAQ;gCAC9D,MAAM,oBAAoB,KAAK,OAAO,EAAE;4BAC1C;4BAEA,yBAAyB;4BACzB,MAAM,qBAAqB;gCACzB,mBAAmB,KAAK,OAAO;gCAC/B,eAAe;gCACf,UAAU,KAAK,QAAQ;gCACvB,gBAAgB;gCAChB,cAAc;gCACd,OAAO,AAAC,mBAAoC,OAAlB,WAAW,MAAM;4BAC7C;wBACF,EAAE,OAAO,gBAAgB;4BACvB,QAAQ,IAAI,CAAC,wCAAwC,KAAK,OAAO,EAAE;wBACrE;oBACF;gBACF;YACF;QACF,EAAE,OAAO,gBAAgB;YACvB,QAAQ,IAAI,CAAC,iEAAiE;QAChF;QAEA,OAAO;YAAE,SAAS;YAAM,MAAM;gBAAE;YAAS;QAAE;IAC7C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAEO,MAAM,aAAa;IACxB,oDAAoD;IACpD,IAAI;QACF,MAAM,eAAe,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,oBAAoB;QACzE,MAAM,kBAAkB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,uBAAuB;QAC/E,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;QAClE,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;QAElE,QAAQ,GAAG,CAAC,sCAAsC;YAChD,cAAc,aAAa,MAAM;YACjC,iBAAiB,gBAAgB,MAAM;YACvC,WAAW,UAAU,MAAM;YAC3B,WAAW,UAAU,MAAM;QAC7B;QAEA,0CAA0C;QAC1C,MAAM,uBAAuB,aAAa,GAAG,CAAC,CAAC;gBAI9B;YAHf,MAAM,WAAW,UAAU,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,WAAW,WAAW;YAC3E,QAAQ,GAAG,CAAC,AAAC,0BAAuC,OAAd,WAAW,EAAE,EAAC,MAAI;gBACtD,gBAAgB,WAAW,YAAY;gBACvC,aAAa,EAAA,2BAAA,WAAW,YAAY,cAAvB,+CAAA,yBAAyB,MAAM,KAAI;YAClD;YACA,OAAO;gBACL,GAAG,UAAU;gBACb,aAAa;gBACb,WAAW,WAAW;oBACpB,MAAM,SAAS,IAAI;oBACnB,OAAO,SAAS,KAAK;oBACrB,SAAS,SAAS,OAAO;gBAC3B,IAAI;gBACJ,eAAe,CAAA,qBAAA,+BAAA,SAAU,IAAI,KAAI,WAAW,aAAa,IAAI;gBAC7D,sBAAsB;gBACtB,cAAc,WAAW,YAAY,IAAI,EAAE;YAC7C;QACF;QAEA,6CAA6C;QAC7C,MAAM,0BAA0B,gBAAgB,GAAG,CAAC,CAAC;gBAIpC;YAHf,MAAM,WAAW,UAAU,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,WAAW,WAAW;YAC3E,QAAQ,GAAG,CAAC,AAAC,6BAA0C,OAAd,WAAW,EAAE,EAAC,MAAI;gBACzD,gBAAgB,WAAW,YAAY;gBACvC,aAAa,EAAA,2BAAA,WAAW,YAAY,cAAvB,+CAAA,yBAAyB,MAAM,KAAI;YAClD;YACA,OAAO;gBACL,GAAG,UAAU;gBACb,aAAa;gBACb,WAAW,WAAW;oBACpB,MAAM,SAAS,IAAI;oBACnB,OAAO,SAAS,KAAK;oBACrB,SAAS,SAAS,OAAO;gBAC3B,IAAI;gBACJ,eAAe,CAAA,qBAAA,+BAAA,SAAU,IAAI,KAAI,WAAW,aAAa,IAAI;gBAC7D,sBAAsB;gBACtB,cAAc,WAAW,YAAY,IAAI,EAAE;YAC7C;QACF;QAEA,+CAA+C;QAC/C,IAAI,qBAAqB,MAAM,GAAG,KAAK,wBAAwB,MAAM,GAAG,GAAG;YACzE,MAAM,aAAa;mBACd;mBACA;aACJ,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;YAElF,QAAQ,GAAG,CAAC,iDAAiD,WAAW,KAAK,CAAC,GAAG;YACjF,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAW;QAC3C;IACF,EAAE,OAAO,YAAY;QACnB,QAAQ,IAAI,CAAC,oCAAoC;IACnD;IAEA,iCAAiC;IACjC,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,MAAM,CAAC,cAAc,gBAAgB,GAAG,MAAM,QAAQ,GAAG,CAAC;YACxD,yHAAA,CAAA,WAAQ,CACL,IAAI,CAAC,iBACL,MAAM,CAAE,uOAWR,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE1C,yHAAA,CAAA,WAAQ,CACL,IAAI,CAAC,oBACL,MAAM,CAAE,uKAQR,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;SAC3C;QAED,MAAM,aAAa;eACd,CAAC,aAAa,IAAI,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA;oBAGhB;uBAHyB;oBACxC,GAAG,IAAI;oBACP,aAAa;oBACb,eAAe,EAAA,kBAAA,KAAK,SAAS,cAAd,sCAAA,gBAAgB,IAAI,KAAI;gBACzC;;eACG,CAAC,gBAAgB,IAAI,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA;oBAGnB;uBAH4B;oBAC3C,GAAG,IAAI;oBACP,aAAa;oBACb,eAAe,EAAA,kBAAA,KAAK,SAAS,cAAd,sCAAA,gBAAgB,IAAI,KAAI;gBACzC;;SACD,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;QAElF,QAAQ,GAAG,CAAC,oCAAoC,WAAW,KAAK,CAAC,GAAG;QACpE,OAAO;YAAE,SAAS;YAAM,MAAM;QAAW;IAC3C,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,mDAAmD;QAEhE,4DAA4D;QAC5D,OAAO;YAAE,SAAS;YAAM,MAAM,EAAE;QAAC;IACnC;AACF;AAGO,MAAM,gBAAgB,OAAO;IAClC,IAAI;QACF,yBAAyB;QACzB,MAAM,eAAe,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,oBAAoB;QACzE,MAAM,kBAAkB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,uBAAuB;QAC/E,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;QAClE,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;QAElE,IAAI,cAAc,aAAa,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK;QACzD,IAAI,aAAa;QAEjB,IAAI,CAAC,aAAa;YAChB,cAAc,gBAAgB,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK;YACxD,aAAa;QACf;QAEA,IAAI,aAAa;YACf,qCAAqC;YACrC,IAAI,eAAe,SAAS;gBAC1B,MAAM,WAAW,UAAU,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,YAAY,WAAW;gBAC5E,cAAc;oBACZ,GAAG,WAAW;oBACd,aAAa;oBACb,WAAW,WAAW;wBACpB,MAAM,SAAS,IAAI;wBACnB,OAAO,SAAS,KAAK;wBACrB,SAAS,SAAS,OAAO;oBAC3B,IAAI;oBACJ,eAAe,CAAA,qBAAA,+BAAA,SAAU,IAAI,KAAI,YAAY,aAAa,IAAI;gBAChE;YACF,OAAO;gBACL,MAAM,WAAW,UAAU,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,YAAY,WAAW;gBAC5E,cAAc;oBACZ,GAAG,WAAW;oBACd,aAAa;oBACb,WAAW,WAAW;wBACpB,MAAM,SAAS,IAAI;wBACnB,OAAO,SAAS,KAAK;wBACrB,SAAS,SAAS,OAAO;oBAC3B,IAAI;oBACJ,eAAe,CAAA,qBAAA,+BAAA,SAAU,IAAI,KAAI,YAAY,aAAa,IAAI;gBAChE;YACF;YAEA,QAAQ,GAAG,CAAC,0CAA0C;YACtD,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAY;QAC5C;QAEA,uCAAuC;QACvC,IAAI,CAAC,yHAAA,CAAA,WAAQ,EAAE;YACb,QAAQ,IAAI,CAAC;YACb,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAmB;QACrD;QAEA,0BAA0B;QAC1B,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC5D,IAAI,CAAC,iBACL,MAAM,CAAE,mNAWR,EAAE,CAAC,MAAM,UACT,MAAM;QAET,IAAI,eAAe,CAAC,YAAY;YAC9B,MAAM,aAAa;gBACjB,GAAG,WAAW;gBACd,aAAa;gBACb,cAAc,YAAY,kBAAkB,IAAI,EAAE;YACpD;YACA,QAAQ,GAAG,CAAC,mCAAmC;YAC/C,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAW;QAC3C;QAEA,uBAAuB;QACvB,MAAM,EAAE,MAAM,cAAc,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAClE,IAAI,CAAC,oBACL,MAAM,CAAE,yJAQR,EAAE,CAAC,MAAM,UACT,MAAM;QAET,IAAI,kBAAkB,CAAC,eAAe;YACpC,MAAM,aAAa;gBACjB,GAAG,cAAc;gBACjB,aAAa;gBACb,cAAc,eAAe,qBAAqB,IAAI,EAAE;YAC1D;YACA,QAAQ,GAAG,CAAC,sCAAsC;YAClD,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAW;QAC3C;QAEA,QAAQ,IAAI,CAAC,qBAAqB;QAClC,OAAO;YAAE,SAAS;YAAO,OAAO;QAAmB;IACrD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAGO,MAAM,8BAA8B,OACzC,aACA;IAEA,IAAI;QACF,0BAA0B;QAC1B,MAAM,gBAAgB,MAAM,sBAAsB;QAClD,IAAI,CAAC,cAAc,OAAO,EAAE,MAAM,IAAI,MAAM;QAE5C,MAAM,YAAY,cAAc,IAAI,CAAC,EAAE;QAEvC,oBAAoB;QACpB,KAAK,MAAM,QAAQ,MAAO;YACxB,IAAI,aAAa,KAAK,UAAU;YAEhC,uCAAuC;YACvC,IAAI,CAAC,YAAY;gBACf,MAAM,oBAAoB,MAAM,YAAY;oBAC1C,MAAM,KAAK,YAAY;oBACvB,UAAU,KAAK,QAAQ,IAAI;oBAC3B,cAAc,KAAK,YAAY,IAAI;oBACnC,mBAAmB,KAAK,gBAAgB,IAAI;oBAC5C,UAAU,KAAK,QAAQ,IAAI;oBAC3B,MAAM,KAAK,IAAI,IAAI;oBACnB,YAAY,KAAK,QAAQ;oBACzB,eAAe,KAAK,YAAY,IAAI,KAAK,QAAQ,GAAG;gBACtD;gBAEA,IAAI,kBAAkB,OAAO,EAAE;oBAC7B,aAAa,kBAAkB,IAAI,CAAC,EAAE;gBACxC,OAAO;oBACL,QAAQ,KAAK,CAAC,8BAA8B,kBAAkB,KAAK;oBACnE;gBACF;YACF;YAEA,4BAA4B;YAC5B,MAAM,wBAAwB;gBAAC;oBAC7B,YAAY;oBACZ,aAAa;oBACb,YAAY,KAAK,SAAS;oBAC1B,UAAU,KAAK,QAAQ;oBACvB,WAAW,KAAK,QAAQ;oBACxB,YAAY,KAAK,SAAS;oBAC1B,aAAa,KAAK,UAAU;oBAC5B,eAAe,KAAK,YAAY,IAAI;gBACtC;aAAE;YAEF,kCAAkC;YAClC,MAAM,cAAc,MAAM,iBAAiB;gBACzC,aAAa;gBACb,YAAY,KAAK,SAAS;gBAC1B,aAAa,KAAK,UAAU;gBAC5B,UAAU,KAAK,QAAQ;gBACvB,YAAY,KAAK,QAAQ;gBACzB,eAAe,KAAK,YAAY,IAAI,KAAK,QAAQ,GAAG;gBACpD,aAAa,YAAY,WAAW;YACtC;YAEA,IAAI,YAAY,OAAO,EAAE;gBACvB,yBAAyB;gBACzB,MAAM,qBAAqB;oBACzB,mBAAmB,YAAY,IAAI,CAAC,EAAE;oBACtC,eAAe;oBACf,UAAU,KAAK,QAAQ;oBACvB,gBAAgB;oBAChB,cAAc;gBAChB;YACF;QACF;QAEA,0CAA0C;QAC1C,IAAI,YAAY,cAAc,KAAK,UAAU,YAAY,cAAc,KAAK,QAAQ;YAClF,MAAM,mBAAmB;gBACvB,kBAAkB;gBAClB,UAAU;gBACV,QAAQ,YAAY,YAAY;gBAChC,aAAa,AAAC,sBAAgD,OAA3B,YAAY,cAAc;gBAC7D,gBAAgB;gBAChB,cAAc;gBACd,gBAAgB;gBAChB,OAAO,YAAY,KAAK;YAC1B;QACF;QAEA,OAAO;YAAE,SAAS;YAAM,MAAM;gBAAE;YAAU;QAAE;IAC9C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0CAA0C;QACxD,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAGO,MAAM,qBAAqB,OAAO;IAUvC,IAAI;QACF,qBAAqB;QACrB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,qBACL,MAAM,CAAC;YAAC;SAAgB,EACxB,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,kEAAkE;QAE/E,2BAA2B;QAC3B,IAAI;YACF,MAAM,oBAAoB;gBACxB,GAAG,eAAe;gBAClB,IAAI,AAAC,MAAmB,OAAd,KAAK,GAAG,IAAG,KAA2C,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;gBAC7D,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,MAAM,uBAAuB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,wBAAwB;YACrF,qBAAqB,IAAI,CAAC;YAC1B,aAAa,OAAO,CAAC,qBAAqB,KAAK,SAAS,CAAC;YAEzD,QAAQ,GAAG,CAAC,2CAA2C;YACvD,QAAQ,GAAG,CAAC,4CAA4C,qBAAqB,MAAM;YAEnF,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAkB;QAClD,EAAE,OAAO,eAAe;YACtB,QAAQ,KAAK,CAAC,6CAA6C;YAC3D,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAc;QAChD;IACF;AACF;AAEO,MAAM,sBAAsB,OAAO;IAMxC,oDAAoD;IACpD,IAAI;QACF,MAAM,eAAe,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,wBAAwB;QAE7E,8CAA8C;QAC9C,IAAI,aAAa,MAAM,GAAG,GAAG;YAC3B,QAAQ,GAAG,CAAC,gDAAgD,aAAa,MAAM;YAE/E,IAAI,uBAAuB;YAE3B,IAAI,oBAAA,8BAAA,QAAS,UAAU,EAAE;gBACvB,uBAAuB,qBAAqB,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,IAAI,QAAQ,UAAU;YAC5F;YACA,IAAI,oBAAA,8BAAA,QAAS,QAAQ,EAAE;gBACrB,uBAAuB,qBAAqB,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,IAAI,QAAQ,QAAQ;YAC1F;YACA,IAAI,oBAAA,8BAAA,QAAS,gBAAgB,EAAE;gBAC7B,uBAAuB,qBAAqB,MAAM,CAAC,CAAA,IAAK,EAAE,gBAAgB,KAAK,QAAQ,gBAAgB;YACzG;YACA,IAAI,oBAAA,8BAAA,QAAS,QAAQ,EAAE;gBACrB,uBAAuB,qBAAqB,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,QAAQ,QAAQ;YACzF;YAEA,gCAAgC;YAChC,qBAAqB,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;YAErG,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAqB;QACrD;IACF,EAAE,OAAO,YAAY;QACnB,QAAQ,IAAI,CAAC,sDAAsD;IACrE;IAEA,iCAAiC;IACjC,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,IAAI,QAAQ,yHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,qBACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,oBAAA,8BAAA,QAAS,UAAU,EAAE;YACvB,QAAQ,MAAM,GAAG,CAAC,cAAc,QAAQ,UAAU;QACpD;QACA,IAAI,oBAAA,8BAAA,QAAS,QAAQ,EAAE;YACrB,QAAQ,MAAM,GAAG,CAAC,cAAc,QAAQ,QAAQ;QAClD;QACA,IAAI,oBAAA,8BAAA,QAAS,gBAAgB,EAAE;YAC7B,QAAQ,MAAM,EAAE,CAAC,oBAAoB,QAAQ,gBAAgB;QAC/D;QACA,IAAI,oBAAA,8BAAA,QAAS,QAAQ,EAAE;YACrB,QAAQ,MAAM,EAAE,CAAC,YAAY,QAAQ,QAAQ;QAC/C;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;QAE9B,IAAI,OAAO,MAAM;QACjB,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,6DAA6D;QAC1E,OAAO;YAAE,SAAS;YAAM,MAAM,EAAE;QAAC;IACnC;AACF;AAEO,MAAM,iBAAiB;IAC5B,yBAAyB;IACzB,IAAI;QACF,MAAM,eAAe,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,wBAAwB;QAE7E,IAAI,aAAa,MAAM,GAAG,GAAG;YAC3B,QAAQ,GAAG,CAAC,+CAA+C,aAAa,MAAM,EAAE;YAEhF,MAAM,UAAU,aAAa,MAAM,CAAC,CAAC,OAAe;gBAClD,OAAO,YAAY,gBAAgB,KAAK,WACpC,QAAQ,YAAY,MAAM,GAC1B,QAAQ,YAAY,MAAM;YAChC,GAAG;YAEH,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAQ;QACxC;IACF,EAAE,OAAO,YAAY;QACnB,QAAQ,IAAI,CAAC,gDAAgD;IAC/D;IAEA,iCAAiC;IACjC,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,qBACL,MAAM,CAAC;QAEV,IAAI,OAAO,MAAM;QAEjB,MAAM,UAAU,KAAK,MAAM,CAAC,CAAC,OAAO;YAClC,OAAO,YAAY,gBAAgB,KAAK,WACpC,QAAQ,YAAY,MAAM,GAC1B,QAAQ,YAAY,MAAM;QAChC,GAAG;QAEH,OAAO;YAAE,SAAS;YAAM,MAAM;QAAQ;IACxC,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,8CAA8C;QAC3D,OAAO;YAAE,SAAS;YAAM,MAAM;QAAE;IAClC;AACF;AAGO,MAAM,mBAAmB;IAC9B,yBAAyB;IACzB,IAAI;QACF,MAAM,gBAAgB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,qBAAqB;QAE3E,IAAI,cAAc,MAAM,GAAG,GAAG;YAC5B,QAAQ,GAAG,CAAC,6CAA6C,cAAc,MAAM,EAAE;YAE/E,mCAAmC;YACnC,MAAM,kBAAkB,cAAc,MAAM,CAAC,CAAC,UAC5C,QAAQ,cAAc,KAAK,aAAa,QAAQ,cAAc,KAAK;YAGrE,QAAQ,GAAG,CAAC,yBAAyB,gBAAgB,MAAM;YAC3D,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAgB;QAChD,OAAO;YACL,+CAA+C;YAC/C,QAAQ,GAAG,CAAC;YACZ,MAAM,cAAc;gBAClB;oBACE,IAAI;oBACJ,gBAAgB;oBAChB,aAAa;oBACb,eAAe;oBACf,cAAc;oBACd,gBAAgB;oBAChB,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW;oBACtE,WAAW;wBAAE,MAAM;wBAAiB,OAAO;oBAAc;gBAC3D;gBACA;oBACE,IAAI;oBACJ,gBAAgB;oBAChB,aAAa;oBACb,eAAe;oBACf,cAAc;oBACd,gBAAgB;oBAChB,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW;oBACtE,WAAW;wBAAE,MAAM;wBAAkB,OAAO;oBAAc;gBAC5D;aACD;YACD,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAY;QAC5C;IACF,EAAE,OAAO,YAAY;QACnB,QAAQ,IAAI,CAAC,mDAAmD;IAClE;IAEA,iCAAiC;IACjC,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,kBACL,MAAM,CAAE,sMAUR,EAAE,CAAC,kBAAkB,WACrB,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO,MAAM;QACjB,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,0DAA0D;QACvE,OAAO;YAAE,SAAS;YAAM,MAAM,EAAE;QAAC;IACnC;AACF;AAEO,MAAM,mBAAmB;IAC9B,yBAAyB;IACzB,IAAI;QACF,MAAM,mBAAmB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,wBAAwB;QAEjF,IAAI,iBAAiB,MAAM,GAAG,GAAG;YAC/B,QAAQ,GAAG,CAAC,6CAA6C,iBAAiB,MAAM,EAAE;YAElF,mCAAmC;YACnC,MAAM,kBAAkB,iBAAiB,MAAM,CAAC,CAAC,UAC/C,QAAQ,cAAc,KAAK,aAAa,QAAQ,cAAc,KAAK;YAGrE,QAAQ,GAAG,CAAC,yBAAyB,gBAAgB,MAAM;YAC3D,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAgB;QAChD,OAAO;YACL,+CAA+C;YAC/C,QAAQ,GAAG,CAAC;YACZ,MAAM,cAAc;gBAClB;oBACE,IAAI;oBACJ,gBAAgB;oBAChB,aAAa;oBACb,cAAc;oBACd,gBAAgB;oBAChB,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW;oBACtE,WAAW;wBAAE,MAAM;wBAAyB,gBAAgB;wBAAa,OAAO;oBAAc;gBAChG;gBACA;oBACE,IAAI;oBACJ,gBAAgB;oBAChB,aAAa;oBACb,cAAc;oBACd,gBAAgB;oBAChB,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW;oBACtE,WAAW;wBAAE,MAAM;wBAAsB,gBAAgB;wBAAa,OAAO;oBAAc;gBAC7F;aACD;YACD,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAY;QAC5C;IACF,EAAE,OAAO,YAAY;QACnB,QAAQ,IAAI,CAAC,mDAAmD;IAClE;IAEA,iCAAiC;IACjC,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,qBACL,MAAM,CAAE,8LASR,EAAE,CAAC,kBAAkB,WACrB,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO,MAAM;QACjB,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,0DAA0D;QACvE,OAAO;YAAE,SAAS;YAAM,MAAM,EAAE;QAAC;IACnC;AACF;AAEO,MAAM,sBAAsB,OACjC,aACA,WACA,eACA;IAEA,IAAI;QACF,qBAAqB;QACrB,MAAM,YAAY,gBAAgB,UAAU,mBAAmB;QAE/D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,WACL,MAAM,CAAC;YACN,gBAAgB;YAChB,GAAI,cAAc;gBAAE,aAAa;YAAW,CAAC;QAC/C,GACC,EAAE,CAAC,MAAM,WACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QAEjB,+CAA+C;QAC/C,IAAI,kBAAkB,UAAU,YAAY;YAC1C,MAAM,kBAAkB,gBAAgB,UAAU,WAAW;YAC7D,MAAM,WAAW,gBAAgB,UAAU,WAAW;YAEtD,MAAM,mBAAmB;gBACvB,kBAAkB;gBAClB;gBACA,QAAQ;gBACR,aAAa,AAAC,cAAmE,OAAtD,gBAAgB,UAAU,WAAW,WAAU,SAA2B,OAApB,KAAK,cAAc;gBACpG,gBAAgB;gBAChB,cAAc;gBACd,gBAAgB;YAClB;QACF;QAEA,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,gEAAgE;QAE7E,2BAA2B;QAC3B,IAAI;YACF,MAAM,aAAa,gBAAgB,UAAU,mBAAmB;YAChE,MAAM,WAAW,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,eAAe;YAEhE,MAAM,eAAe,SAAS,SAAS,CAAC,CAAC,MAAa,IAAI,EAAE,KAAK;YACjE,IAAI,iBAAiB,CAAC,GAAG;gBACvB,QAAQ,CAAC,aAAa,CAAC,cAAc,GAAG;gBACxC,IAAI,YAAY;oBACd,QAAQ,CAAC,aAAa,CAAC,WAAW,GAAG;gBACvC;gBAEA,aAAa,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC;gBAEhD,+CAA+C;gBAC/C,IAAI,kBAAkB,UAAU,YAAY;oBAC1C,MAAM,kBAAkB,gBAAgB,UAAU,WAAW;oBAC7D,MAAM,WAAW,gBAAgB,UAAU,WAAW;oBAEtD,MAAM,mBAAmB;wBACvB,kBAAkB;wBAClB;wBACA,QAAQ;wBACR,aAAa,AAAC,cAAmE,OAAtD,gBAAgB,UAAU,WAAW,WAAU,SAA6C,OAAtC,QAAQ,CAAC,aAAa,CAAC,cAAc;wBACtH,gBAAgB;wBAChB,cAAc;wBACd,gBAAgB;oBAClB;gBACF;gBAEA,QAAQ,GAAG,CAAC,2CAA2C,QAAQ,CAAC,aAAa;gBAC7E,OAAO;oBAAE,SAAS;oBAAM,MAAM,QAAQ,CAAC,aAAa;gBAAC;YACvD,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,eAAe;YACtB,QAAQ,KAAK,CAAC,6CAA6C;YAC3D,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAc;QAChD;IACF;AACF;AAGO,MAAM,iBAAiB,OAAO;IAMnC,IAAI;QACF,IAAI,QAAQ,yHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,kBACL,MAAM,CAAE,8NAWR,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,QAAQ,UAAU,EAAE;YACtB,QAAQ,MAAM,GAAG,CAAC,cAAc,QAAQ,UAAU;QACpD;QACA,IAAI,QAAQ,QAAQ,EAAE;YACpB,QAAQ,MAAM,GAAG,CAAC,cAAc,QAAQ,QAAQ;QAClD;QACA,IAAI,QAAQ,WAAW,EAAE;YACvB,QAAQ,MAAM,EAAE,CAAC,eAAe,QAAQ,WAAW;QACrD;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;QAE9B,IAAI,OAAO,MAAM;QAEjB,kCAAkC;QAClC,IAAI,eAAe;QACnB,IAAI,QAAQ,WAAW,EAAE;YACvB,eAAe,KAAK,MAAM,CAAC,CAAA,UACzB,QAAQ,mBAAmB,CAAC,IAAI,CAAC,CAAC;wBAChC,kCAAA;2BAAA,EAAA,yBAAA,KAAK,gBAAgB,cAArB,8CAAA,mCAAA,uBAAuB,SAAS,cAAhC,uDAAA,iCAAkC,EAAE,MAAK,QAAQ,WAAW;;QAGlE;QAEA,OAAO;YAAE,SAAS;YAAM,MAAM;QAAa;IAC7C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAEO,MAAM,qBAAqB,OAAO;IAMvC,IAAI;QACF,IAAI,QAAQ,yHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,qBACL,MAAM,CAAE,2KAQR,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,QAAQ,UAAU,EAAE;YACtB,QAAQ,MAAM,GAAG,CAAC,cAAc,QAAQ,UAAU;QACpD;QACA,IAAI,QAAQ,QAAQ,EAAE;YACpB,QAAQ,MAAM,GAAG,CAAC,cAAc,QAAQ,QAAQ;QAClD;QACA,IAAI,QAAQ,WAAW,EAAE;YACvB,QAAQ,MAAM,EAAE,CAAC,eAAe,QAAQ,WAAW;QACrD;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;QAE9B,IAAI,OAAO,MAAM;QAEjB,kCAAkC;QAClC,IAAI,eAAe;QACnB,IAAI,QAAQ,WAAW,EAAE;YACvB,eAAe,KAAK,MAAM,CAAC,CAAA,UACzB,QAAQ,sBAAsB,CAAC,IAAI,CAAC,CAAC;wBACnC;2BAAA,EAAA,kBAAA,KAAK,SAAS,cAAd,sCAAA,gBAAgB,EAAE,MAAK,QAAQ,WAAW;;QAGhD;QAEA,OAAO;YAAE,SAAS;YAAM,MAAM;QAAa;IAC7C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAEO,MAAM,uBAAuB,OAAO,YAAoB;IAI7D,IAAI;QACF,IAAI,aAAa,yHAAA,CAAA,WAAQ,CACtB,IAAI,CAAC,kBACL,MAAM,CAAE,gNAWR,EAAE,CAAC,eAAe,YAClB,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,eAAe,yHAAA,CAAA,WAAQ,CACxB,IAAI,CAAC,iBACL,MAAM,CAAE,8HAQR,EAAE,CAAC,eAAe,YAClB,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,QAAQ,UAAU,EAAE;YACtB,aAAa,WAAW,GAAG,CAAC,cAAc,QAAQ,UAAU;YAC5D,eAAe,aAAa,GAAG,CAAC,cAAc,QAAQ,UAAU;QAClE;QACA,IAAI,QAAQ,QAAQ,EAAE;YACpB,aAAa,WAAW,GAAG,CAAC,cAAc,QAAQ,QAAQ;YAC1D,eAAe,aAAa,GAAG,CAAC,cAAc,QAAQ,QAAQ;QAChE;QAEA,MAAM,CAAC,aAAa,cAAc,GAAG,MAAM,QAAQ,GAAG,CAAC;YACrD;YACA;SACD;QAED,IAAI,YAAY,KAAK,EAAE,MAAM,YAAY,KAAK;QAC9C,IAAI,cAAc,KAAK,EAAE,MAAM,cAAc,KAAK;QAElD,OAAO;YACL,SAAS;YACT,MAAM;gBACJ,OAAO,YAAY,IAAI;gBACvB,SAAS,cAAc,IAAI;YAC7B;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAEO,MAAM,uBAAuB,OAAO,YAAoB;IAI7D,IAAI;QACF,IAAI,iBAAiB,yHAAA,CAAA,WAAQ,CAC1B,IAAI,CAAC,qBACL,MAAM,CAAE,gNAWR,EAAE,CAAC,eAAe,YAClB,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,eAAe,yHAAA,CAAA,WAAQ,CACxB,IAAI,CAAC,oBACL,MAAM,CAAE,8HAQR,EAAE,CAAC,eAAe,YAClB,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,QAAQ,UAAU,EAAE;YACtB,iBAAiB,eAAe,GAAG,CAAC,cAAc,QAAQ,UAAU;YACpE,eAAe,aAAa,GAAG,CAAC,cAAc,QAAQ,UAAU;QAClE;QACA,IAAI,QAAQ,QAAQ,EAAE;YACpB,iBAAiB,eAAe,GAAG,CAAC,cAAc,QAAQ,QAAQ;YAClE,eAAe,aAAa,GAAG,CAAC,cAAc,QAAQ,QAAQ;QAChE;QAEA,MAAM,CAAC,iBAAiB,cAAc,GAAG,MAAM,QAAQ,GAAG,CAAC;YACzD;YACA;SACD;QAED,IAAI,gBAAgB,KAAK,EAAE,MAAM,gBAAgB,KAAK;QACtD,IAAI,cAAc,KAAK,EAAE,MAAM,cAAc,KAAK;QAElD,OAAO;YACL,SAAS;YACT,MAAM;gBACJ,WAAW,gBAAgB,IAAI;gBAC/B,SAAS,cAAc,IAAI;YAC7B;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAEO,MAAM,4BAA4B,OAAO,YAAoB;IAIlE,IAAI;QACF,IAAI,QAAQ,yHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,uBACL,MAAM,CAAE,oJAQR,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,QAAQ,UAAU,EAAE;YACtB,QAAQ,MAAM,GAAG,CAAC,cAAc,QAAQ,UAAU;QACpD;QACA,IAAI,QAAQ,QAAQ,EAAE;YACpB,QAAQ,MAAM,GAAG,CAAC,cAAc,QAAQ,QAAQ;QAClD;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;QAE9B,IAAI,OAAO,MAAM;QAEjB,qBAAqB;QACrB,MAAM,eAAe,KAAK,MAAM,CAAC,CAAA;gBAC/B,sCAAA;mBAAA,EAAA,6BAAA,SAAS,gBAAgB,cAAzB,kDAAA,uCAAA,2BAA2B,SAAS,cAApC,2DAAA,qCAAsC,EAAE,MAAK;;QAG/C,OAAO;YAAE,SAAS;YAAM,MAAM;QAAa;IAC7C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4CAA4C;QAC1D,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAEO,MAAM,qBAAqB,OAAO;IAMvC,IAAI;QACF,IAAI,QAAQ,yHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,oBACL,MAAM,CAAE,0EAIR,KAAK,CAAC,eAAe;YAAE,WAAW;QAAK;QAE1C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;QAE9B,IAAI,OAAO,MAAM;QAEjB,IAAI,eAAe;QAEnB,qBAAqB;QACrB,IAAI,QAAQ,QAAQ,EAAE;YACpB,eAAe,aAAa,MAAM,CAAC,CAAA;oBACjC;uBAAA,EAAA,mBAAA,MAAM,SAAS,cAAf,uCAAA,iBAAiB,QAAQ,MAAK,QAAQ,QAAQ;;QAElD;QAEA,2CAA2C;QAC3C,IAAI,QAAQ,SAAS,EAAE;YACrB,eAAe,aAAa,MAAM,CAAC,CAAA,QAAS,MAAM,QAAQ,GAAG;QAC/D;QAEA,oBAAoB;QACpB,IAAI,QAAQ,OAAO,EAAE;YACnB,MAAM,QAAQ,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YACpD,eAAe,aAAa,MAAM,CAAC,CAAA,QAAS,MAAM,WAAW,GAAG;QAClE;QAEA,2CAA2C;QAC3C,IAAI,QAAQ,aAAa,EAAE;YACzB,MAAM,oBAAoB,IAAI;YAC9B,kBAAkB,OAAO,CAAC,kBAAkB,OAAO,KAAK;YACxD,MAAM,aAAa,kBAAkB,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAChE,MAAM,QAAQ,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAEpD,eAAe,aAAa,MAAM,CAAC,CAAA,QACjC,MAAM,WAAW,IAAI,SAAS,MAAM,WAAW,IAAI;QAEvD;QAEA,OAAO;YAAE,SAAS;YAAM,MAAM;QAAa;IAC7C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAGO,MAAM,qBAAqB,OAAO,UAAkB,QAAgB;IACzE,MAAM,UAAe;QAAE;IAAO;IAC9B,IAAI,iBAAiB;QACnB,QAAQ,gBAAgB,GAAG;IAC7B;IACA,OAAO,aAAa,UAAU;AAChC;AAGO,MAAM,oBAAoB,OAAO;IACtC,IAAI;QACF,QAAQ,GAAG,CAAC,gCAAgC;QAE5C,2BAA2B;QAC3B,MAAM,eAAe,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,oBAAoB;QACzE,MAAM,kBAAkB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,uBAAuB;QAC/E,MAAM,mBAAmB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,yBAAyB;QAClF,MAAM,sBAAsB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,4BAA4B;QAExF,wBAAwB;QACxB,IAAI,cAAc,aAAa,IAAI,CAAC,CAAC,MAAa,IAAI,EAAE,KAAK;QAC7D,IAAI,aAAa;YACf,MAAM,QAAQ,iBAAiB,MAAM,CAAC,CAAC,OAAc,KAAK,SAAS,KAAK;YAExE,qEAAqE;YACrE,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;YAClE,MAAM,kBAAkB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,uBAAuB;YAE/E,MAAM,gBAAgB,MAAM,GAAG,CAAC,CAAC;gBAC/B,IAAI,eAAe,KAAK,aAAa,IAAI,KAAK,YAAY,IAAI;gBAC9D,IAAI,YAAY,KAAK,UAAU,IAAI,KAAK,SAAS,IAAI;gBACrD,IAAI,aAAa,KAAK,WAAW,IAAI,KAAK,UAAU,IAAI;gBAExD,uDAAuD;gBACvD,IAAI,iBAAiB,cAAc,KAAK,iBAAiB,EAAE;oBACzD,MAAM,QAAQ,gBAAgB,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,KAAK,iBAAiB;oBAC9E,IAAI,OAAO;wBACT,YAAY,MAAM,YAAY,IAAI;wBAClC,aAAa,MAAM,WAAW,IAAI;wBAElC,MAAM,WAAW,UAAU,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,MAAM,WAAW;wBACtE,IAAI,UAAU;4BACZ,eAAe,SAAS,IAAI,IAAI;wBAClC;oBACF;gBACF;gBAEA,2DAA2D;gBAC3D,IAAI,iBAAiB,cAAc,KAAK,WAAW,EAAE;oBACnD,MAAM,WAAW,UAAU,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,KAAK,WAAW;oBACrE,IAAI,UAAU;wBACZ,eAAe,SAAS,IAAI,IAAI;oBAClC;gBACF;gBAEA,OAAO;oBACL,GAAG,IAAI;oBACP,eAAe;oBACf,YAAY;oBACZ,aAAa;oBACb,YAAY,KAAK,UAAU,IAAI,KAAK,SAAS,IAAI;oBACjD,aAAa,KAAK,WAAW,IAAI,KAAK,UAAU,IAAI;gBACtD;YACF;YAEA,MAAM,aAAa;gBACjB,GAAG,WAAW;gBACd,MAAM;gBACN,aAAa;gBACb,sBAAsB;YACxB;YACA,QAAQ,GAAG,CAAC,iCAAiC;YAC7C,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAW;QAC3C;QAEA,2BAA2B;QAC3B,cAAc,gBAAgB,IAAI,CAAC,CAAC,MAAa,IAAI,EAAE,KAAK;QAC5D,IAAI,aAAa;YACf,MAAM,QAAQ,oBAAoB,MAAM,CAAC,CAAC,OAAc,KAAK,SAAS,KAAK;YAE3E,qEAAqE;YACrE,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;YAElE,MAAM,gBAAgB,MAAM,GAAG,CAAC,CAAC;gBAC/B,IAAI,eAAe,KAAK,aAAa,IAAI,KAAK,YAAY,IAAI;gBAE9D,qDAAqD;gBACrD,IAAI,iBAAiB,cAAc,KAAK,WAAW,EAAE;oBACnD,MAAM,WAAW,UAAU,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,KAAK,WAAW;oBACrE,IAAI,UAAU;wBACZ,eAAe,SAAS,IAAI,IAAI;oBAClC;gBACF;gBAEA,OAAO;oBACL,GAAG,IAAI;oBACP,eAAe;oBACf,YAAY,KAAK,UAAU,IAAI,KAAK,SAAS,IAAI;oBACjD,aAAa,KAAK,WAAW,IAAI,KAAK,UAAU,IAAI;oBACpD,WAAW,KAAK,SAAS,IAAI,KAAK,QAAQ,IAAI;oBAC9C,YAAY,KAAK,UAAU,IAAI,KAAK,SAAS,IAAI;gBACnD;YACF;YAEA,MAAM,aAAa;gBACjB,GAAG,WAAW;gBACd,MAAM;gBACN,aAAa;gBACb,sBAAsB;YACxB;YACA,QAAQ,GAAG,CAAC,kCAAkC;YAC9C,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAW;QAC3C;QAEA,6CAA6C;QAC7C,QAAQ,GAAG,CAAC;QAEZ,0BAA0B;QAC1B,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC5D,IAAI,CAAC,iBACL,MAAM,CAAE,8RAYR,EAAE,CAAC,MAAM,UACT,MAAM;QAET,IAAI,eAAe,CAAC,YAAY;YAC9B,MAAM,aAAa;gBACjB,GAAG,WAAW;gBACd,MAAM;gBACN,aAAa;gBACb,sBAAsB,CAAC,YAAY,kBAAkB,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC;wBAEjD,kCAAA;2BAFgE;wBAC/E,GAAG,IAAI;wBACP,eAAe,EAAA,yBAAA,KAAK,gBAAgB,cAArB,8CAAA,mCAAA,uBAAuB,SAAS,cAAhC,uDAAA,iCAAkC,IAAI,KAAI,KAAK,aAAa,IAAI;wBAC/E,YAAY,KAAK,UAAU,IAAI;wBAC/B,aAAa,KAAK,WAAW,IAAI;oBACnC;;YACF;YACA,QAAQ,GAAG,CAAC,6CAA6C;YACzD,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAW;QAC3C;QAEA,uBAAuB;QACvB,MAAM,EAAE,MAAM,cAAc,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAClE,IAAI,CAAC,oBACL,MAAM,CAAE,iNAQR,EAAE,CAAC,MAAM,UACT,MAAM;QAET,IAAI,kBAAkB,CAAC,eAAe;YACpC,MAAM,aAAa;gBACjB,GAAG,cAAc;gBACjB,MAAM;gBACN,aAAa;gBACb,sBAAsB,CAAC,eAAe,qBAAqB,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC;wBAEvD;2BAFsE;wBACrF,GAAG,IAAI;wBACP,eAAe,EAAA,kBAAA,KAAK,SAAS,cAAd,sCAAA,gBAAgB,IAAI,KAAI,KAAK,aAAa,IAAI;wBAC7D,WAAW,KAAK,SAAS,IAAI;wBAC7B,YAAY,KAAK,UAAU,IAAI;oBACjC;;YACF;YACA,QAAQ,GAAG,CAAC,8CAA8C;YAC1D,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAW;QAC3C;QAEA,QAAQ,GAAG,CAAC;QACZ,OAAO;YAAE,SAAS;YAAO,OAAO;QAAmB;IACrD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAGO,MAAM,eAAe,OAAO,UAAkB;IACnD,IAAI;QACF,+BAA+B;QAC/B,MAAM,eAAe,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,oBAAoB;QACzE,MAAM,kBAAkB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,uBAAuB;QAE/E,mCAAmC;QACnC,MAAM,aAAa,aAAa,SAAS,CAAC,CAAC,MAAa,IAAI,EAAE,KAAK;QACnE,IAAI,eAAe,CAAC,GAAG;YACrB,YAAY,CAAC,WAAW,GAAG;gBAAE,GAAG,YAAY,CAAC,WAAW;gBAAE,GAAG,OAAO;gBAAE,YAAY,IAAI,OAAO,WAAW;YAAG;YAC3G,aAAa,OAAO,CAAC,iBAAiB,KAAK,SAAS,CAAC;YAErD,4BAA4B;YAC5B,IAAI;gBACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,iBACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM;gBAEZ,IAAI,OAAO;oBACT,QAAQ,IAAI,CAAC,wCAAwC;gBACvD;YACF,EAAE,OAAO,eAAe;gBACtB,QAAQ,IAAI,CAAC,yDAAyD;YACxE;YAEA,OAAO;gBAAE,SAAS;gBAAM,MAAM,YAAY,CAAC,WAAW;YAAC;QACzD;QAEA,sCAAsC;QACtC,MAAM,gBAAgB,gBAAgB,SAAS,CAAC,CAAC,MAAa,IAAI,EAAE,KAAK;QACzE,IAAI,kBAAkB,CAAC,GAAG;YACxB,eAAe,CAAC,cAAc,GAAG;gBAAE,GAAG,eAAe,CAAC,cAAc;gBAAE,GAAG,OAAO;gBAAE,YAAY,IAAI,OAAO,WAAW;YAAG;YACvH,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;YAExD,4BAA4B;YAC5B,IAAI;gBACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,oBACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM;gBAEZ,IAAI,OAAO;oBACT,QAAQ,IAAI,CAAC,wCAAwC;gBACvD;YACF,EAAE,OAAO,eAAe;gBACtB,QAAQ,IAAI,CAAC,yDAAyD;YACxE;YAEA,OAAO;gBAAE,SAAS;gBAAM,MAAM,eAAe,CAAC,cAAc;YAAC;QAC/D;QAEA,OAAO;YAAE,SAAS;YAAO,OAAO;QAAmB;IACrD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF", "debugId": null}}, {"offset": {"line": 4635, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%287%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/utils/larenPrintTemplate.ts"], "sourcesContent": ["import { PrintSettings } from '@/hooks/usePrintSettings'\n\nexport const generateLarenInvoiceHTML = (invoice: any, type: 'sales' | 'purchase' | 'return', settings: PrintSettings) => {\n  let items, customerSupplier, documentTitle, documentNumber\n\n  if (type === 'return') {\n    items = invoice.return_invoice_items || invoice.sales_return_items || invoice.purchase_return_items || []\n    const returnType = invoice.type || invoice.return_type || 'sales'\n    customerSupplier = returnType === 'sales'\n      ? (invoice.customers?.name || invoice.customer_name || invoice.customerName || 'عميل نقدي')\n      : (invoice.suppliers?.name || invoice.supplier_name || invoice.supplierName || 'غير محدد')\n    documentTitle = returnType === 'sales' ? 'مرتجع مبيعات' : 'مرتجع مشتريات'\n    documentNumber = invoice.return_number || invoice.invoice_number || 'غير محدد'\n  } else {\n    items = type === 'sales' ? invoice.sales_invoice_items : invoice.purchase_invoice_items\n    customerSupplier = type === 'sales'\n      ? (invoice.customers?.name || invoice.customer_name || invoice.customerName || 'عميل نقدي')\n      : (invoice.suppliers?.name || invoice.supplier_name || invoice.supplierName || 'غير محدد')\n    documentTitle = type === 'sales' ? 'فاتورة مبيعات' : 'فاتورة مشتريات'\n    documentNumber = invoice.invoice_number || 'غير محدد'\n  }\n\n  return `\n    <!DOCTYPE html>\n    <html dir=\"rtl\" lang=\"ar\">\n    <head>\n      <meta charset=\"UTF-8\">\n      <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n      <title>${documentTitle} - ${documentNumber}</title>\n      <style>\n        * { margin: 0; padding: 0; box-sizing: border-box; }\n        body { \n          font-family: 'Arial', sans-serif; \n          font-size: 14px;\n          color: #000;\n          background-color: #fff;\n          line-height: 1.4;\n          direction: rtl;\n        }\n        .container { \n          max-width: 210mm; \n          margin: 0 auto; \n          padding: 10mm;\n          border: 2px solid #000;\n          min-height: 297mm;\n          position: relative;\n        }\n        \n        /* Header Section */\n        .header {\n          border-bottom: 2px solid #000;\n          padding-bottom: 15px;\n          margin-bottom: 20px;\n          display: flex;\n          align-items: center;\n          justify-content: space-between;\n        }\n        \n        .company-info {\n          flex: 1;\n          text-align: right;\n        }\n        \n        .company-name-ar {\n          font-size: 24px;\n          font-weight: bold;\n          margin-bottom: 5px;\n        }\n        \n        .company-name-en {\n          font-size: 18px;\n          font-weight: bold;\n          margin-bottom: 10px;\n          direction: ltr;\n          text-align: left;\n        }\n        \n        .company-address {\n          font-size: 14px;\n          margin-bottom: 5px;\n        }\n        \n        .logo-section {\n          width: 120px;\n          height: 120px;\n          border: 2px solid #000;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          margin: 0 20px;\n          background-color: #f8f9fa;\n        }\n        \n        .logo-text {\n          font-size: 16px;\n          font-weight: bold;\n          text-align: center;\n          line-height: 1.2;\n        }\n        \n        /* Invoice Details Section */\n        .invoice-details {\n          display: flex;\n          justify-content: space-between;\n          margin-bottom: 20px;\n          border: 1px solid #000;\n        }\n        \n        .invoice-info, .customer-info {\n          flex: 1;\n          padding: 10px;\n          border-right: 1px solid #000;\n        }\n        \n        .customer-info {\n          border-right: none;\n        }\n        \n        .detail-row {\n          display: flex;\n          justify-content: space-between;\n          margin-bottom: 8px;\n          padding: 2px 0;\n        }\n        \n        .detail-label {\n          font-weight: bold;\n          min-width: 80px;\n        }\n        \n        .detail-value {\n          text-align: left;\n        }\n        \n        /* Items Table */\n        .items-table {\n          width: 100%;\n          border-collapse: collapse;\n          margin-bottom: 20px;\n          border: 2px solid #000;\n        }\n        \n        .items-table th,\n        .items-table td {\n          border: 1px solid #000;\n          padding: 8px;\n          text-align: center;\n          font-size: 12px;\n        }\n        \n        .items-table th {\n          background-color: #f0f0f0;\n          font-weight: bold;\n        }\n        \n        .items-table .item-name {\n          text-align: right;\n          padding-right: 10px;\n        }\n        \n        /* Totals Section */\n        .totals-section {\n          width: 300px;\n          margin-left: auto;\n          border: 2px solid #000;\n          margin-bottom: 20px;\n        }\n        \n        .totals-section table {\n          width: 100%;\n          border-collapse: collapse;\n        }\n        \n        .totals-section td {\n          border: 1px solid #000;\n          padding: 8px;\n          font-size: 14px;\n        }\n        \n        .totals-section .total-label {\n          background-color: #f0f0f0;\n          font-weight: bold;\n          text-align: right;\n        }\n        \n        .totals-section .total-value {\n          text-align: center;\n          font-weight: bold;\n        }\n        \n        /* Footer Section */\n        .footer {\n          position: absolute;\n          bottom: 10mm;\n          left: 10mm;\n          right: 10mm;\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          border-top: 1px solid #000;\n          padding-top: 15px;\n        }\n        \n        .signature-section {\n          text-align: center;\n          flex: 1;\n        }\n        \n        .signature-box {\n          width: 150px;\n          height: 80px;\n          border: 2px solid #000;\n          border-radius: 50%;\n          margin: 0 auto 10px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          font-size: 12px;\n          color: #666;\n        }\n        \n        .notes-section {\n          margin-bottom: 30px;\n          border: 1px solid #000;\n          padding: 10px;\n          min-height: 60px;\n        }\n        \n        .notes-label {\n          font-weight: bold;\n          margin-bottom: 5px;\n        }\n        \n        @media print {\n          body { margin: 0; }\n          .container { \n            border: 2px solid #000;\n            box-shadow: none;\n            margin: 0;\n            padding: 10mm;\n          }\n          @page {\n            size: A4;\n            margin: 0;\n          }\n        }\n      </style>\n    </head>\n    <body>\n      <div class=\"container\">\n        <!-- Header -->\n        <div class=\"header\">\n          <div class=\"company-info\">\n            <div class=\"company-name-ar\">${settings.companyName}</div>\n            <div class=\"company-name-en\">${settings.companyNameEn}</div>\n            <div class=\"company-address\">${settings.companyAddress}</div>\n          </div>\n          \n          <div class=\"logo-section\">\n            <div class=\"logo-text\">\n              LAREN<br>\n              لارين\n            </div>\n          </div>\n        </div>\n\n        <!-- Document Details -->\n        <div class=\"invoice-details\">\n          <div class=\"invoice-info\">\n            <div class=\"detail-row\">\n              <span class=\"detail-label\">${type === 'return' ? 'رقم المرتجع:' : 'رقم الفاتورة:'}</span>\n              <span class=\"detail-value\">${documentNumber}</span>\n            </div>\n            <div class=\"detail-row\">\n              <span class=\"detail-label\">التاريخ:</span>\n              <span class=\"detail-value\">${new Date(invoice.created_at).toLocaleDateString('ar-EG')}</span>\n            </div>\n            ${type !== 'return' ? `\n            <div class=\"detail-row\">\n              <span class=\"detail-label\">طريقة الدفع:</span>\n              <span class=\"detail-value\">${invoice.payment_method === 'cash' ? 'نقداً' : 'آجل'}</span>\n            </div>` : ''}\n            ${type === 'return' ? `\n            <div class=\"detail-row\">\n              <span class=\"detail-label\">سبب المرتجع:</span>\n              <span class=\"detail-value\">${invoice.reason || 'غير محدد'}</span>\n            </div>\n            <div class=\"detail-row\">\n              <span class=\"detail-label\">الحالة:</span>\n              <span class=\"detail-value\">${invoice.status === 'approved' ? 'مقبول' : invoice.status === 'rejected' ? 'مرفوض' : 'قيد المراجعة'}</span>\n            </div>` : ''}\n            </div>\n          </div>\n          \n          <div class=\"customer-info\">\n            <div class=\"detail-row\">\n              <span class=\"detail-label\">${\n                type === 'return'\n                  ? (invoice.type === 'purchase' || invoice.return_type === 'purchase' ? 'المورد:' : 'العميل:')\n                  : (type === 'sales' ? 'العميل:' : 'المورد:')\n              }</span>\n              <span class=\"detail-value\">${customerSupplier}</span>\n            </div>\n            <div class=\"detail-row\">\n              <span class=\"detail-label\">الهاتف:</span>\n              <span class=\"detail-value\">${\n                type === 'return'\n                  ? (invoice.type === 'purchase' || invoice.return_type === 'purchase'\n                      ? (invoice.suppliers?.phone || invoice.supplier_phone || invoice.supplierPhone || '')\n                      : (invoice.customers?.phone || invoice.customer_phone || invoice.customerPhone || ''))\n                  : (type === 'sales'\n                      ? (invoice.customers?.phone || invoice.customer_phone || invoice.customerPhone || '')\n                      : (invoice.suppliers?.phone || invoice.supplier_phone || invoice.supplierPhone || ''))\n              }</span>\n            </div>\n            <div class=\"detail-row\">\n              <span class=\"detail-label\">العنوان:</span>\n              <span class=\"detail-value\">${\n                type === 'return'\n                  ? (invoice.type === 'purchase' || invoice.return_type === 'purchase'\n                      ? (invoice.suppliers?.address || invoice.supplier_address || invoice.supplierAddress || '')\n                      : (invoice.customers?.address || invoice.customer_address || invoice.customerAddress || ''))\n                  : (type === 'sales'\n                      ? (invoice.customers?.address || invoice.customer_address || invoice.customerAddress || '')\n                      : (invoice.suppliers?.address || invoice.supplier_address || invoice.supplierAddress || ''))\n              }</span>\n            </div>\n          </div>\n        </div>\n\n        <!-- Items Table -->\n        <table class=\"items-table\">\n          <thead>\n            <tr>\n              <th style=\"width: 40px;\">ت</th>\n              <th style=\"width: ${type === 'return' ? '150px' : '200px'};\">اسم المادة</th>\n              <th style=\"width: 60px;\">الكمية</th>\n              <th style=\"width: 80px;\">السعر</th>\n              <th style=\"width: 100px;\">المجموع</th>\n              ${type === 'return' ? '<th style=\"width: 100px;\">سبب المرتجع</th>' : ''}\n              <th style=\"width: 60px;\">EXP</th>\n              <th style=\"width: 60px;\">B.N</th>\n            </tr>\n          </thead>\n          <tbody>\n            ${items?.map((item: any, index: number) => `\n              <tr>\n                <td>${index + 1}</td>\n                <td class=\"item-name\">${\n                  type === 'return'\n                    ? (item.medicine_name || item.medicineName || item.medicine_batches?.medicines?.name || item.medicines?.name || 'غير محدد')\n                    : type === 'sales'\n                      ? (item.medicine_name || item.medicineName || item.medicine_batches?.medicines?.name || 'غير محدد')\n                      : (item.medicine_name || item.medicineName || item.medicines?.name || item.name || 'غير محدد')\n                }</td>\n                <td>${item.quantity}</td>\n                <td>${\n                  type === 'return'\n                    ? (invoice.type === 'purchase' || invoice.return_type === 'purchase'\n                        ? (item.unit_cost || item.unitCost || 0).toLocaleString()\n                        : (item.unit_price || item.unitPrice || 0).toLocaleString())\n                    : type === 'sales'\n                      ? (item.unit_price || 0).toLocaleString()\n                      : (item.unit_cost || item.unitCost || 0).toLocaleString()\n                }</td>\n                <td>${\n                  type === 'return'\n                    ? (invoice.type === 'purchase' || invoice.return_type === 'purchase'\n                        ? (item.total_cost || item.totalCost || 0).toLocaleString()\n                        : (item.total_price || item.totalPrice || 0).toLocaleString())\n                    : type === 'sales'\n                      ? (item.total_price || 0).toLocaleString()\n                      : (item.total_cost || item.totalCost || 0).toLocaleString()\n                }</td>\n                ${type === 'return' ? `<td style=\"font-size: 10px;\">${item.return_reason || invoice.reason || 'غير محدد'}</td>` : ''}\n                <td>${\n                  type === 'return'\n                    ? (item.expiry_date || item.expiryDate || item.medicine_batches?.expiry_date\n                        ? new Date(item.expiry_date || item.expiryDate || item.medicine_batches.expiry_date).toLocaleDateString('en-GB').replace(/\\//g, '/')\n                        : '')\n                    : type === 'sales'\n                      ? (item.medicine_batches?.expiry_date ? new Date(item.medicine_batches.expiry_date).toLocaleDateString('en-GB').replace(/\\//g, '/') : '')\n                      : (item.expiry_date || item.expiryDate ? new Date(item.expiry_date || item.expiryDate).toLocaleDateString('en-GB').replace(/\\//g, '/') : '')\n                }</td>\n                <td>${\n                  type === 'return'\n                    ? (item.batch_code || item.batchCode || item.medicine_batches?.batch_number || '')\n                    : type === 'sales'\n                      ? (item.medicine_batches?.batch_number || '')\n                      : (item.batch_code || item.batchCode || '')\n                }</td>\n              </tr>\n            `).join('') || ''}\n            \n            <!-- Empty rows to fill space -->\n            ${Array.from({ length: Math.max(0, 10 - (items?.length || 0)) }, (_, i) => `\n              <tr>\n                <td>&nbsp;</td>\n                <td>&nbsp;</td>\n                <td>&nbsp;</td>\n                <td>&nbsp;</td>\n                <td>&nbsp;</td>\n                ${type === 'return' ? '<td>&nbsp;</td>' : ''}\n                <td>&nbsp;</td>\n                <td>&nbsp;</td>\n              </tr>\n            `).join('')}\n          </tbody>\n        </table>\n\n        <!-- Totals -->\n        <div class=\"totals-section\">\n          <table>\n            ${type !== 'return' ? `\n            <tr>\n              <td class=\"total-label\">المجموع الفرعي:</td>\n              <td class=\"total-value\">${invoice.total_amount?.toLocaleString() || 0}</td>\n            </tr>\n            <tr>\n              <td class=\"total-label\">الخصم:</td>\n              <td class=\"total-value\">${invoice.discount_amount?.toLocaleString() || 0}</td>\n            </tr>\n            <tr style=\"background-color: #f0f0f0;\">\n              <td class=\"total-label\">المجموع النهائي:</td>\n              <td class=\"total-value\">${invoice.final_amount?.toLocaleString() || 0}</td>\n            </tr>` : `\n            <tr style=\"background-color: #f0f0f0;\">\n              <td class=\"total-label\">إجمالي المرتجع:</td>\n              <td class=\"total-value\">${invoice.total_amount?.toLocaleString() || 0}</td>\n            </tr>`}\n          </table>\n        </div>\n\n        <!-- Notes -->\n        <div class=\"notes-section\">\n          <div class=\"notes-label\">ملاحظات: ${invoice.notes || ''}</div>\n          ${type === 'return' && invoice.rejection_reason ? `\n          <div class=\"notes-label\" style=\"margin-top: 10px; color: #dc2626;\">سبب الرفض: ${invoice.rejection_reason}</div>` : ''}\n        </div>\n\n        <!-- Footer -->\n        <div class=\"footer\">\n          <div style=\"font-size: 12px;\">\n            صفحة 1 من 1\n          </div>\n          \n          <div class=\"signature-section\">\n            <div class=\"signature-box\">\n              ختم وتوقيع<br>\n              المسؤول\n            </div>\n          </div>\n          \n          <div style=\"font-size: 12px; text-align: left;\">\n            ${settings.footerText || 'شكراً لتعاملكم معنا'}\n          </div>\n        </div>\n      </div>\n    </body>\n    </html>\n  `\n}\n\n// دالة ترجمة أسماء الأعمدة\nconst translateColumnName = (columnName: string): string => {\n  const translations: { [key: string]: string } = {\n    'id': 'الرقم',\n    'name': 'الاسم',\n    'invoice_number': 'رقم الفاتورة',\n    'customer_name': 'اسم العميل',\n    'supplier_name': 'اسم المورد',\n    'total_amount': 'المبلغ الإجمالي',\n    'final_amount': 'المبلغ النهائي',\n    'payment_status': 'حالة الدفع',\n    'created_at': 'التاريخ',\n    'phone': 'الهاتف',\n    'address': 'العنوان',\n    'category': 'الفئة',\n    'quantity': 'الكمية',\n    'expiry_date': 'تاريخ الانتهاء',\n    'medicine_name': 'اسم الدواء',\n    'batch_code': 'رقم الدفعة',\n    'unit_price': 'سعر الوحدة',\n    'discount': 'الخصم',\n    'notes': 'ملاحظات'\n  }\n  return translations[columnName] || columnName\n}\n\n// دالة تنسيق قيم الخلايا\nconst formatCellValue = (value: any, columnName: string): string => {\n  if (value === null || value === undefined) return 'غير محدد'\n\n  // تنسيق التواريخ\n  if (columnName.includes('date') || columnName.includes('created_at')) {\n    try {\n      return new Date(value).toLocaleDateString('ar-EG')\n    } catch {\n      return value.toString()\n    }\n  }\n\n  // تنسيق المبالغ\n  if (columnName.includes('amount') || columnName.includes('price') || columnName.includes('cost')) {\n    const num = parseFloat(value)\n    return isNaN(num) ? value.toString() : `${num.toLocaleString()} د.ع`\n  }\n\n  // تنسيق حالة الدفع\n  if (columnName === 'payment_status') {\n    return value === 'paid' ? 'مدفوع' : value === 'pending' ? 'معلق' : value.toString()\n  }\n\n  return value.toString()\n}\n\nexport const generateLarenReportHTML = (reportData: any, reportType: string, title: string, settings: PrintSettings) => {\n  return `\n    <!DOCTYPE html>\n    <html dir=\"rtl\" lang=\"ar\">\n    <head>\n      <meta charset=\"UTF-8\">\n      <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n      <title>${title}</title>\n      <style>\n        * { margin: 0; padding: 0; box-sizing: border-box; }\n        body { \n          font-family: 'Arial', sans-serif; \n          font-size: 12px;\n          color: #000;\n          background-color: #fff;\n          line-height: 1.4;\n          direction: rtl;\n        }\n        .container { \n          max-width: 210mm; \n          margin: 0 auto; \n          padding: 10mm;\n          border: 2px solid #000;\n        }\n        \n        .header {\n          border-bottom: 2px solid #000;\n          padding-bottom: 15px;\n          margin-bottom: 20px;\n          display: flex;\n          align-items: center;\n          justify-content: space-between;\n        }\n        \n        .company-info {\n          flex: 1;\n          text-align: right;\n        }\n        \n        .company-name-ar {\n          font-size: 20px;\n          font-weight: bold;\n          margin-bottom: 5px;\n        }\n        \n        .company-name-en {\n          font-size: 16px;\n          font-weight: bold;\n          margin-bottom: 10px;\n          direction: ltr;\n          text-align: left;\n        }\n        \n        .logo-section {\n          width: 100px;\n          height: 100px;\n          border: 2px solid #000;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          margin: 0 20px;\n          background-color: #f8f9fa;\n        }\n        \n        .report-title {\n          text-align: center;\n          font-size: 18px;\n          font-weight: bold;\n          margin: 20px 0;\n          padding: 10px;\n          border: 1px solid #000;\n          background-color: #f0f0f0;\n        }\n        \n        .report-date {\n          text-align: center;\n          margin-bottom: 20px;\n          font-size: 14px;\n        }\n        \n        table {\n          width: 100%;\n          border-collapse: collapse;\n          margin-bottom: 20px;\n          border: 2px solid #000;\n        }\n        \n        th, td {\n          border: 1px solid #000;\n          padding: 4px 6px;\n          text-align: right;\n          font-size: 10px;\n          vertical-align: top;\n          word-wrap: break-word;\n          max-width: 120px;\n        }\n\n        th {\n          background-color: #f0f0f0;\n          font-weight: bold;\n          text-align: center;\n          font-size: 9px;\n        }\n\n        tr:nth-child(even) {\n          background-color: #f9f9f9;\n        }\n\n        .number-cell {\n          text-align: center;\n          font-weight: bold;\n          width: 30px;\n        }\n        \n        .summary-section {\n          display: flex;\n          justify-content: space-around;\n          margin-bottom: 20px;\n          border: 1px solid #000;\n          padding: 15px;\n        }\n        \n        .summary-item {\n          text-align: center;\n        }\n        \n        .summary-label {\n          font-size: 12px;\n          margin-bottom: 5px;\n        }\n        \n        .summary-value {\n          font-size: 16px;\n          font-weight: bold;\n        }\n        \n        @media print {\n          body { margin: 0; }\n          .container { \n            border: 2px solid #000;\n            box-shadow: none;\n            margin: 0;\n            padding: 10mm;\n          }\n          @page {\n            size: A4;\n            margin: 0;\n          }\n        }\n      </style>\n    </head>\n    <body>\n      <div class=\"container\">\n        <div class=\"header\">\n          <div class=\"company-info\">\n            <div class=\"company-name-ar\">${settings.companyName}</div>\n            <div class=\"company-name-en\">${settings.companyNameEn}</div>\n            <div>${settings.companyAddress}</div>\n          </div>\n          \n          <div class=\"logo-section\">\n            <div style=\"font-size: 14px; font-weight: bold; text-align: center;\">\n              LAREN<br>\n              لارين\n            </div>\n          </div>\n        </div>\n\n        <div class=\"report-title\">${title}</div>\n        <div class=\"report-date\">تاريخ الطباعة: ${new Date().toLocaleDateString('ar-EG')}</div>\n\n        ${Array.isArray(reportData) && reportData.length > 0 ? `\n          <div class=\"summary-section\">\n            <div class=\"summary-item\">\n              <div class=\"summary-label\">عدد السجلات</div>\n              <div class=\"summary-value\">${reportData.length}</div>\n            </div>\n            ${reportType.includes('sales') || reportType.includes('purchases') ? `\n              <div class=\"summary-item\">\n                <div class=\"summary-label\">إجمالي المبلغ</div>\n                <div class=\"summary-value\">${reportData.reduce((sum: number, item: any) => sum + (item.final_amount || item.total_amount || 0), 0).toLocaleString()} د.ع</div>\n              </div>\n              <div class=\"summary-item\">\n                <div class=\"summary-label\">متوسط المبلغ</div>\n                <div class=\"summary-value\">${Math.round(reportData.reduce((sum: number, item: any) => sum + (item.final_amount || item.total_amount || 0), 0) / reportData.length).toLocaleString()} د.ع</div>\n              </div>\n              <div class=\"summary-item\">\n                <div class=\"summary-label\">الفواتير المدفوعة</div>\n                <div class=\"summary-value\">${reportData.filter((item: any) => item.payment_status === 'paid').length}</div>\n              </div>\n              <div class=\"summary-item\">\n                <div class=\"summary-label\">الفواتير المعلقة</div>\n                <div class=\"summary-value\">${reportData.filter((item: any) => item.payment_status === 'pending').length}</div>\n              </div>\n            ` : ''}\n            ${reportType === 'inventory' ? `\n              <div class=\"summary-item\">\n                <div class=\"summary-label\">إجمالي الكمية</div>\n                <div class=\"summary-value\">${reportData.reduce((sum: number, item: any) => sum + (item.quantity || 0), 0).toLocaleString()}</div>\n              </div>\n              <div class=\"summary-item\">\n                <div class=\"summary-label\">الأدوية منتهية الصلاحية</div>\n                <div class=\"summary-value\">${reportData.filter((item: any) => new Date(item.expiry_date) < new Date()).length}</div>\n              </div>\n              <div class=\"summary-item\">\n                <div class=\"summary-label\">الأدوية قليلة الكمية</div>\n                <div class=\"summary-value\">${reportData.filter((item: any) => (item.quantity || 0) < 10).length}</div>\n              </div>\n            ` : ''}\n          </div>\n\n          <table>\n            <thead>\n              <tr>\n                <th style=\"width: 30px;\">ت</th>\n                ${reportType.includes('sales') ? `\n                  <th>رقم الفاتورة</th>\n                  <th>العميل</th>\n                  <th>المبلغ النهائي</th>\n                  <th>حالة الدفع</th>\n                  <th>التاريخ</th>\n                ` : ''}\n                ${reportType.includes('purchases') ? `\n                  <th>رقم الفاتورة</th>\n                  <th>المورد</th>\n                  <th>المبلغ النهائي</th>\n                  <th>حالة الدفع</th>\n                  <th>التاريخ</th>\n                ` : ''}\n                ${reportType === 'inventory' ? `\n                  <th>اسم الدواء</th>\n                  <th>الفئة</th>\n                  <th>الكمية</th>\n                  <th>تاريخ الانتهاء</th>\n                  <th>الحالة</th>\n                ` : ''}\n                ${reportType === 'financial' ? `\n                  <th>نوع العملية</th>\n                  <th>المبلغ</th>\n                  <th>الوصف</th>\n                  <th>التاريخ</th>\n                ` : ''}\n                ${reportType === 'customers' ? `\n                  <th>اسم العميل</th>\n                  <th>الهاتف</th>\n                  <th>العنوان</th>\n                  <th>إجمالي المشتريات</th>\n                ` : ''}\n                ${reportType === 'suppliers' ? `\n                  <th>اسم المورد</th>\n                  <th>الهاتف</th>\n                  <th>العنوان</th>\n                  <th>إجمالي المشتريات</th>\n                ` : ''}\n                ${!reportType.includes('sales') && !reportType.includes('purchases') && reportType !== 'inventory' && reportType !== 'financial' && reportType !== 'customers' && reportType !== 'suppliers' ? `\n                  ${Object.keys(reportData[0] || {}).slice(0, 6).map(key => `<th>${translateColumnName(key)}</th>`).join('')}\n                ` : ''}\n              </tr>\n            </thead>\n            <tbody>\n              ${reportData.map((item: any, index: number) => `\n                <tr>\n                  <td class=\"number-cell\">${index + 1}</td>\n                  ${reportType.includes('sales') ? `\n                    <td>${item.invoice_number}</td>\n                    <td>${item.customers?.name || item.customer_name || 'عميل نقدي'}</td>\n                    <td>${item.final_amount?.toLocaleString()} د.ع</td>\n                    <td>${item.payment_status === 'paid' ? 'مدفوع' : 'معلق'}</td>\n                    <td>${new Date(item.created_at).toLocaleDateString('ar-EG')}</td>\n                  ` : ''}\n                  ${reportType.includes('purchases') ? `\n                    <td>${item.invoice_number}</td>\n                    <td>${item.suppliers?.name || 'غير محدد'}</td>\n                    <td>${item.final_amount?.toLocaleString()} د.ع</td>\n                    <td>${item.payment_status === 'paid' ? 'مدفوع' : 'معلق'}</td>\n                    <td>${new Date(item.created_at).toLocaleDateString('ar-EG')}</td>\n                  ` : ''}\n                  ${reportType === 'inventory' ? `\n                    <td>${item.medicines?.name || item.medicine_name || 'غير محدد'}</td>\n                    <td>${item.medicines?.category || item.category || 'غير محدد'}</td>\n                    <td>${item.quantity || 0}</td>\n                    <td>${item.expiry_date ? new Date(item.expiry_date).toLocaleDateString('ar-EG') : 'غير محدد'}</td>\n                    <td>${(item.quantity || 0) < 10 ? 'كمية قليلة' : 'طبيعي'}</td>\n                  ` : ''}\n                  ${reportType === 'financial' ? `\n                    <td>${item.type === 'income' ? 'دخل' : 'مصروف'}</td>\n                    <td>${item.amount?.toLocaleString() || 0} د.ع</td>\n                    <td>${item.description || 'غير محدد'}</td>\n                    <td>${item.created_at ? new Date(item.created_at).toLocaleDateString('ar-EG') : 'غير محدد'}</td>\n                  ` : ''}\n                  ${reportType === 'customers' ? `\n                    <td>${item.name || 'غير محدد'}</td>\n                    <td>${item.phone || 'غير محدد'}</td>\n                    <td>${item.address || 'غير محدد'}</td>\n                    <td>${item.total_purchases?.toLocaleString() || 0} د.ع</td>\n                  ` : ''}\n                  ${reportType === 'suppliers' ? `\n                    <td>${item.name || 'غير محدد'}</td>\n                    <td>${item.phone || 'غير محدد'}</td>\n                    <td>${item.address || 'غير محدد'}</td>\n                    <td>${item.total_purchases?.toLocaleString() || 0} د.ع</td>\n                  ` : ''}\n                  ${!reportType.includes('sales') && !reportType.includes('purchases') && reportType !== 'inventory' && reportType !== 'financial' && reportType !== 'customers' && reportType !== 'suppliers' ? `\n                    ${Object.keys(reportData[0] || {}).slice(0, 6).map(key => `\n                      <td>${formatCellValue(item[key], key)}</td>\n                    `).join('')}\n                  ` : ''}\n                </tr>\n              `).join('')}\n            </tbody>\n          </table>\n        ` : `\n          <div style=\"text-align: center; padding: 50px; border: 1px solid #000;\">\n            لا توجد بيانات للعرض\n          </div>\n        `}\n      </div>\n    </body>\n    </html>\n  `\n}\n\nexport const generateLarenReturnHTML = (returnRecord: any, settings: PrintSettings) => {\n  const items = returnRecord.return_items || []\n  const isSupplierReturn = returnRecord.type === 'purchase_return'\n  const customerSupplier = isSupplierReturn\n    ? (returnRecord.suppliers?.name || 'غير محدد')\n    : (returnRecord.customers?.name || returnRecord.customer_name || 'عميل نقدي')\n\n  return `\n    <!DOCTYPE html>\n    <html dir=\"rtl\" lang=\"ar\">\n    <head>\n      <meta charset=\"UTF-8\">\n      <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n      <title>سند إرجاع - ${returnRecord.return_number}</title>\n      <style>\n        * { margin: 0; padding: 0; box-sizing: border-box; }\n        body {\n          font-family: 'Arial', sans-serif;\n          font-size: 14px;\n          color: #000;\n          background-color: #fff;\n          line-height: 1.4;\n          direction: rtl;\n        }\n        .container {\n          max-width: 210mm;\n          margin: 0 auto;\n          padding: 10mm;\n          border: 2px solid #000;\n          min-height: 297mm;\n          position: relative;\n        }\n\n        .header {\n          border-bottom: 2px solid #000;\n          padding-bottom: 15px;\n          margin-bottom: 20px;\n          display: flex;\n          align-items: center;\n          justify-content: space-between;\n        }\n\n        .company-info {\n          flex: 1;\n          text-align: right;\n        }\n\n        .company-name-ar {\n          font-size: 24px;\n          font-weight: bold;\n          margin-bottom: 5px;\n        }\n\n        .company-name-en {\n          font-size: 18px;\n          font-weight: bold;\n          margin-bottom: 10px;\n          direction: ltr;\n          text-align: left;\n        }\n\n        .logo-section {\n          width: 120px;\n          height: 120px;\n          border: 2px solid #000;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          margin: 0 20px;\n          background-color: #f8f9fa;\n        }\n\n        .return-title {\n          text-align: center;\n          font-size: 20px;\n          font-weight: bold;\n          margin: 20px 0;\n          padding: 10px;\n          border: 2px solid #000;\n          background-color: #f0f0f0;\n        }\n\n        .return-details {\n          display: flex;\n          justify-content: space-between;\n          margin-bottom: 20px;\n          border: 1px solid #000;\n        }\n\n        .return-info, .customer-info {\n          flex: 1;\n          padding: 10px;\n          border-right: 1px solid #000;\n        }\n\n        .customer-info {\n          border-right: none;\n        }\n\n        .detail-row {\n          display: flex;\n          justify-content: space-between;\n          margin-bottom: 8px;\n          padding: 2px 0;\n        }\n\n        .detail-label {\n          font-weight: bold;\n          min-width: 100px;\n        }\n\n        .detail-value {\n          text-align: left;\n        }\n\n        .items-table {\n          width: 100%;\n          border-collapse: collapse;\n          margin-bottom: 20px;\n          border: 2px solid #000;\n        }\n\n        .items-table th,\n        .items-table td {\n          border: 1px solid #000;\n          padding: 8px;\n          text-align: center;\n          font-size: 12px;\n        }\n\n        .items-table th {\n          background-color: #f0f0f0;\n          font-weight: bold;\n        }\n\n        .items-table .item-name {\n          text-align: right;\n          padding-right: 10px;\n        }\n\n        .totals-section {\n          width: 300px;\n          margin-left: auto;\n          border: 2px solid #000;\n          margin-bottom: 20px;\n        }\n\n        .totals-section table {\n          width: 100%;\n          border-collapse: collapse;\n        }\n\n        .totals-section td {\n          border: 1px solid #000;\n          padding: 8px;\n          font-size: 14px;\n        }\n\n        .totals-section .total-label {\n          background-color: #f0f0f0;\n          font-weight: bold;\n          text-align: right;\n        }\n\n        .totals-section .total-value {\n          text-align: center;\n          font-weight: bold;\n        }\n\n        .footer {\n          position: absolute;\n          bottom: 10mm;\n          left: 10mm;\n          right: 10mm;\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          border-top: 1px solid #000;\n          padding-top: 15px;\n        }\n\n        .signature-section {\n          text-align: center;\n          flex: 1;\n        }\n\n        .signature-box {\n          width: 150px;\n          height: 80px;\n          border: 2px solid #000;\n          border-radius: 50%;\n          margin: 0 auto 10px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          font-size: 12px;\n          color: #666;\n        }\n\n        .notes-section {\n          margin-bottom: 30px;\n          border: 1px solid #000;\n          padding: 10px;\n          min-height: 60px;\n        }\n\n        @media print {\n          body { margin: 0; }\n          .container {\n            border: 2px solid #000;\n            box-shadow: none;\n            margin: 0;\n            padding: 10mm;\n          }\n          @page {\n            size: A4;\n            margin: 0;\n          }\n        }\n      </style>\n    </head>\n    <body>\n      <div class=\"container\">\n        <div class=\"header\">\n          <div class=\"company-info\">\n            <div class=\"company-name-ar\">${settings.companyName}</div>\n            <div class=\"company-name-en\">${settings.companyNameEn}</div>\n            <div class=\"company-address\">${settings.companyAddress}</div>\n          </div>\n\n          <div class=\"logo-section\">\n            <div style=\"font-size: 16px; font-weight: bold; text-align: center; line-height: 1.2;\">\n              LAREN<br>\n              لارين\n            </div>\n          </div>\n        </div>\n\n        <div class=\"return-title\">\n          سند إرجاع ${isSupplierReturn ? 'مشتريات' : 'مبيعات'}\n        </div>\n\n        <div class=\"return-details\">\n          <div class=\"return-info\">\n            <div class=\"detail-row\">\n              <span class=\"detail-label\">رقم سند الإرجاع:</span>\n              <span class=\"detail-value\">${returnRecord.return_number}</span>\n            </div>\n            <div class=\"detail-row\">\n              <span class=\"detail-label\">التاريخ:</span>\n              <span class=\"detail-value\">${new Date(returnRecord.created_at).toLocaleDateString('ar-EG')}</span>\n            </div>\n            <div class=\"detail-row\">\n              <span class=\"detail-label\">رقم الفاتورة الأصلية:</span>\n              <span class=\"detail-value\">${returnRecord.original_invoice_number || 'غير محدد'}</span>\n            </div>\n            <div class=\"detail-row\">\n              <span class=\"detail-label\">نوع الإرجاع:</span>\n              <span class=\"detail-value\">${isSupplierReturn ? 'إرجاع للمورد' : 'إرجاع من العميل'}</span>\n            </div>\n          </div>\n\n          <div class=\"customer-info\">\n            <div class=\"detail-row\">\n              <span class=\"detail-label\">${isSupplierReturn ? 'المورد:' : 'العميل:'}</span>\n              <span class=\"detail-value\">${customerSupplier}</span>\n            </div>\n            <div class=\"detail-row\">\n              <span class=\"detail-label\">الهاتف:</span>\n              <span class=\"detail-value\">${isSupplierReturn ? (returnRecord.suppliers?.phone || '') : (returnRecord.customers?.phone || '')}</span>\n            </div>\n            <div class=\"detail-row\">\n              <span class=\"detail-label\">العنوان:</span>\n              <span class=\"detail-value\">${isSupplierReturn ? (returnRecord.suppliers?.address || '') : (returnRecord.customers?.address || '')}</span>\n            </div>\n          </div>\n        </div>\n\n        <table class=\"items-table\">\n          <thead>\n            <tr>\n              <th style=\"width: 40px;\">ت</th>\n              <th style=\"width: 200px;\">اسم المادة</th>\n              <th style=\"width: 60px;\">الكمية</th>\n              <th style=\"width: 80px;\">السعر</th>\n              <th style=\"width: 100px;\">المجموع</th>\n              <th style=\"width: 100px;\">سبب الإرجاع</th>\n              <th style=\"width: 60px;\">EXP</th>\n            </tr>\n          </thead>\n          <tbody>\n            ${items?.map((item: any, index: number) => `\n              <tr>\n                <td>${index + 1}</td>\n                <td class=\"item-name\">${item.medicine_name || item.medicines?.name || item.medicine?.name || 'غير محدد'}</td>\n                <td>${item.quantity}</td>\n                <td>${(item.unit_price || 0).toLocaleString()}</td>\n                <td>${(item.total_amount || 0).toLocaleString()}</td>\n                <td style=\"font-size: 10px;\">${item.return_reason || 'غير محدد'}</td>\n                <td>${item.expiry_date ? new Date(item.expiry_date).toLocaleDateString('en-GB').replace(/\\//g, '/') : ''}</td>\n              </tr>\n            `).join('') || ''}\n\n            ${Array.from({ length: Math.max(0, 8 - (items?.length || 0)) }, (_, i) => `\n              <tr>\n                <td>&nbsp;</td>\n                <td>&nbsp;</td>\n                <td>&nbsp;</td>\n                <td>&nbsp;</td>\n                <td>&nbsp;</td>\n                <td>&nbsp;</td>\n                <td>&nbsp;</td>\n              </tr>\n            `).join('')}\n          </tbody>\n        </table>\n\n        <div class=\"totals-section\">\n          <table>\n            <tr>\n              <td class=\"total-label\">إجمالي المبلغ المرتجع:</td>\n              <td class=\"total-value\">${returnRecord.total_amount?.toLocaleString() || 0} د.ع</td>\n            </tr>\n          </table>\n        </div>\n\n        <div class=\"notes-section\">\n          <div style=\"font-weight: bold; margin-bottom: 5px;\">ملاحظات:</div>\n          <div>${returnRecord.notes || ''}</div>\n        </div>\n\n        <div class=\"footer\">\n          <div style=\"font-size: 12px;\">\n            صفحة 1 من 1\n          </div>\n\n          <div class=\"signature-section\">\n            <div class=\"signature-box\">\n              ختم وتوقيع<br>\n              المسؤول\n            </div>\n          </div>\n\n          <div style=\"font-size: 12px; text-align: left;\">\n            ${settings.footerText || 'شكراً لتعاملكم معنا'}\n          </div>\n        </div>\n      </div>\n    </body>\n    </html>\n  `\n}\n"], "names": [], "mappings": ";;;;;AAEO,MAAM,2BAA2B,CAAC,SAAc,MAAuC;QAkTrE,oBACA,oBAEA,qBACA,qBAQA,qBACA,qBAEA,qBACA,qBA4Fe,uBAIA,0BAIA,uBAIA;IAzatC,IAAI,OAAO,kBAAkB,eAAe;IAE5C,IAAI,SAAS,UAAU;YAIhB,qBACA;QAJL,QAAQ,QAAQ,oBAAoB,IAAI,QAAQ,kBAAkB,IAAI,QAAQ,qBAAqB,IAAI,EAAE;QACzG,MAAM,aAAa,QAAQ,IAAI,IAAI,QAAQ,WAAW,IAAI;QAC1D,mBAAmB,eAAe,UAC7B,EAAA,sBAAA,QAAQ,SAAS,cAAjB,0CAAA,oBAAmB,IAAI,KAAI,QAAQ,aAAa,IAAI,QAAQ,YAAY,IAAI,cAC5E,EAAA,sBAAA,QAAQ,SAAS,cAAjB,0CAAA,oBAAmB,IAAI,KAAI,QAAQ,aAAa,IAAI,QAAQ,YAAY,IAAI;QACjF,gBAAgB,eAAe,UAAU,iBAAiB;QAC1D,iBAAiB,QAAQ,aAAa,IAAI,QAAQ,cAAc,IAAI;IACtE,OAAO;YAGA,qBACA;QAHL,QAAQ,SAAS,UAAU,QAAQ,mBAAmB,GAAG,QAAQ,sBAAsB;QACvF,mBAAmB,SAAS,UACvB,EAAA,sBAAA,QAAQ,SAAS,cAAjB,0CAAA,oBAAmB,IAAI,KAAI,QAAQ,aAAa,IAAI,QAAQ,YAAY,IAAI,cAC5E,EAAA,sBAAA,QAAQ,SAAS,cAAjB,0CAAA,oBAAmB,IAAI,KAAI,QAAQ,aAAa,IAAI,QAAQ,YAAY,IAAI;QACjF,gBAAgB,SAAS,UAAU,kBAAkB;QACrD,iBAAiB,QAAQ,cAAc,IAAI;IAC7C;IAEA,OAAO,AAAC,+LAMwB,OAAnB,eAAc,OAiOc,OAjOT,gBAAe,g6KAkON,OADA,SAAS,WAAW,EAAC,qDAErB,OADA,SAAS,aAAa,EAAC,qDAgBvB,OAfA,SAAS,cAAc,EAAC,0ZAgBxB,OADA,SAAS,WAAW,iBAAiB,iBAAgB,sDAKrD,OAJA,gBAAe,0KAM5C,OAF6B,IAAI,KAAK,QAAQ,UAAU,EAAE,kBAAkB,CAAC,UAAS,6CAOtF,OALA,SAAS,WAAW,AAAC,kJAG4D,OAApD,QAAQ,cAAc,KAAK,SAAS,UAAU,OAAM,iCACzE,IAAG,kBAgBT,OAfF,SAAS,WAAW,AAAC,kJAOQ,OAJA,QAAQ,MAAM,IAAI,YAAW,yKAIsE,OAAnG,QAAQ,MAAM,KAAK,aAAa,UAAU,QAAQ,MAAM,KAAK,aAAa,UAAU,gBAAe,iCACxH,IAAG,8KAWkB,OAJ3B,SAAS,WACJ,QAAQ,IAAI,KAAK,cAAc,QAAQ,WAAW,KAAK,aAAa,YAAY,YAChF,SAAS,UAAU,YAAY,WACrC,sDAMC,OAL2B,kBAAiB,yKAiB5C,OAZA,SAAS,WACJ,QAAQ,IAAI,KAAK,cAAc,QAAQ,WAAW,KAAK,aACnD,EAAA,qBAAA,QAAQ,SAAS,cAAjB,yCAAA,mBAAmB,KAAK,KAAI,QAAQ,cAAc,IAAI,QAAQ,aAAa,IAAI,KAC/E,EAAA,qBAAA,QAAQ,SAAS,cAAjB,yCAAA,mBAAmB,KAAK,KAAI,QAAQ,cAAc,IAAI,QAAQ,aAAa,IAAI,KACnF,SAAS,UACL,EAAA,sBAAA,QAAQ,SAAS,cAAjB,0CAAA,oBAAmB,KAAK,KAAI,QAAQ,cAAc,IAAI,QAAQ,aAAa,IAAI,KAC/E,EAAA,sBAAA,QAAQ,SAAS,cAAjB,0CAAA,oBAAmB,KAAK,KAAI,QAAQ,cAAc,IAAI,QAAQ,aAAa,IAAI,IACzF,0KAsBmB,OAjBlB,SAAS,WACJ,QAAQ,IAAI,KAAK,cAAc,QAAQ,WAAW,KAAK,aACnD,EAAA,sBAAA,QAAQ,SAAS,cAAjB,0CAAA,oBAAmB,OAAO,KAAI,QAAQ,gBAAgB,IAAI,QAAQ,eAAe,IAAI,KACrF,EAAA,sBAAA,QAAQ,SAAS,cAAjB,0CAAA,oBAAmB,OAAO,KAAI,QAAQ,gBAAgB,IAAI,QAAQ,eAAe,IAAI,KACzF,SAAS,UACL,EAAA,sBAAA,QAAQ,SAAS,cAAjB,0CAAA,oBAAmB,OAAO,KAAI,QAAQ,gBAAgB,IAAI,QAAQ,eAAe,IAAI,KACrF,EAAA,sBAAA,QAAQ,SAAS,cAAjB,0CAAA,oBAAmB,OAAO,KAAI,QAAQ,gBAAgB,IAAI,QAAQ,eAAe,IAAI,IAC/F,4PAcC,OAJkB,SAAS,WAAW,UAAU,SAAQ,mMAU1D,OANE,SAAS,WAAW,+CAA+C,IAAG,8KAwDxE,OAlDA,CAAA,kBAAA,4BAAA,MAAO,GAAG,CAAC,CAAC,MAAW;YAK6B,kCAAA,wBAA0C,iBAExC,mCAAA,yBACA,kBAwBN,yBAIrC,yBAKmC,yBAEnC;eA3C8B,AAAC,6CAItC,OAFI,QAAQ,GAAE,iDAQV,OANJ,SAAS,WACJ,KAAK,aAAa,IAAI,KAAK,YAAY,MAAI,yBAAA,KAAK,gBAAgB,cAArB,8CAAA,mCAAA,uBAAuB,SAAS,cAAhC,uDAAA,iCAAkC,IAAI,OAAI,kBAAA,KAAK,SAAS,cAAd,sCAAA,gBAAgB,IAAI,KAAI,aAC9G,SAAS,UACN,KAAK,aAAa,IAAI,KAAK,YAAY,MAAI,0BAAA,KAAK,gBAAgB,cAArB,+CAAA,oCAAA,wBAAuB,SAAS,cAAhC,wDAAA,kCAAkC,IAAI,KAAI,aACrF,KAAK,aAAa,IAAI,KAAK,YAAY,MAAI,mBAAA,KAAK,SAAS,cAAd,uCAAA,iBAAgB,IAAI,KAAI,KAAK,IAAI,IAAI,YACxF,+BAGC,OAFI,KAAK,QAAQ,EAAC,+BAWlB,OATA,SAAS,WACJ,QAAQ,IAAI,KAAK,cAAc,QAAQ,WAAW,KAAK,aACpD,CAAC,KAAK,SAAS,IAAI,KAAK,QAAQ,IAAI,CAAC,EAAE,cAAc,KACrD,CAAC,KAAK,UAAU,IAAI,KAAK,SAAS,IAAI,CAAC,EAAE,cAAc,KAC3D,SAAS,UACP,CAAC,KAAK,UAAU,IAAI,CAAC,EAAE,cAAc,KACrC,CAAC,KAAK,SAAS,IAAI,KAAK,QAAQ,IAAI,CAAC,EAAE,cAAc,IAC5D,+BAUC,OARA,SAAS,WACJ,QAAQ,IAAI,KAAK,cAAc,QAAQ,WAAW,KAAK,aACpD,CAAC,KAAK,UAAU,IAAI,KAAK,SAAS,IAAI,CAAC,EAAE,cAAc,KACvD,CAAC,KAAK,WAAW,IAAI,KAAK,UAAU,IAAI,CAAC,EAAE,cAAc,KAC7D,SAAS,UACP,CAAC,KAAK,WAAW,IAAI,CAAC,EAAE,cAAc,KACtC,CAAC,KAAK,UAAU,IAAI,KAAK,SAAS,IAAI,CAAC,EAAE,cAAc,IAC9D,2BAGC,OAFA,SAAS,WAAW,AAAC,gCAAkF,OAAnD,KAAK,aAAa,IAAI,QAAQ,MAAM,IAAI,YAAW,WAAS,IAAG,0BAWnH,OATA,SAAS,WACJ,KAAK,WAAW,IAAI,KAAK,UAAU,MAAI,0BAAA,KAAK,gBAAgB,cAArB,8CAAA,wBAAuB,WAAW,IACtE,IAAI,KAAK,KAAK,WAAW,IAAI,KAAK,UAAU,IAAI,KAAK,gBAAgB,CAAC,WAAW,EAAE,kBAAkB,CAAC,SAAS,OAAO,CAAC,OAAO,OAC9H,KACJ,SAAS,UACN,EAAA,0BAAA,KAAK,gBAAgB,cAArB,8CAAA,wBAAuB,WAAW,IAAG,IAAI,KAAK,KAAK,gBAAgB,CAAC,WAAW,EAAE,kBAAkB,CAAC,SAAS,OAAO,CAAC,OAAO,OAAO,KACnI,KAAK,WAAW,IAAI,KAAK,UAAU,GAAG,IAAI,KAAK,KAAK,WAAW,IAAI,KAAK,UAAU,EAAE,kBAAkB,CAAC,SAAS,OAAO,CAAC,OAAO,OAAO,IAC9I,+BAOA,OALC,SAAS,WACJ,KAAK,UAAU,IAAI,KAAK,SAAS,MAAI,0BAAA,KAAK,gBAAgB,cAArB,8CAAA,wBAAuB,YAAY,KAAI,KAC7E,SAAS,UACN,EAAA,0BAAA,KAAK,gBAAgB,cAArB,8CAAA,wBAAuB,YAAY,KAAI,KACvC,KAAK,UAAU,IAAI,KAAK,SAAS,IAAI,IAC7C;OAEF,IAAI,CAAC,QAAO,IAAG,+EAqBhB,OAlBA,MAAM,IAAI,CAAC;QAAE,QAAQ,KAAK,GAAG,CAAC,GAAG,KAAK,CAAC,CAAA,kBAAA,4BAAA,MAAO,MAAM,KAAI,CAAC;IAAG,GAAG,CAAC,GAAG,IAAM,AAAC,8MAO3B,OAA3C,SAAS,WAAW,oBAAoB,IAAG,0GAI9C,IAAI,CAAC,KAAI,4IA6BsB,OAtBhC,SAAS,WAAW,AAAC,yHAOK,OAJA,EAAA,wBAAA,QAAQ,YAAY,cAApB,4CAAA,sBAAsB,cAAc,OAAM,GAAE,yIAQ5C,OAJA,EAAA,2BAAA,QAAQ,eAAe,cAAvB,+CAAA,yBAAyB,cAAc,OAAM,GAAE,sLAIH,OAA5C,EAAA,wBAAA,QAAQ,YAAY,cAApB,4CAAA,sBAAsB,cAAc,OAAM,GAAE,8BAC/D,AAAC,4JAG8D,OAA5C,EAAA,yBAAA,QAAQ,YAAY,cAApB,6CAAA,uBAAsB,cAAc,OAAM,GAAE,6BACjE,qJAOP,OADkC,QAAQ,KAAK,IAAI,IAAG,sBAmBpD,OAlBF,SAAS,YAAY,QAAQ,gBAAgB,GAAG,AAAC,6FACsD,OAAzB,QAAQ,gBAAgB,EAAC,YAAU,IAAG,mbAiBrE,OAA7C,SAAS,UAAU,IAAI,uBAAsB;AAO3D;AAEA,2BAA2B;AAC3B,MAAM,sBAAsB,CAAC;IAC3B,MAAM,eAA0C;QAC9C,MAAM;QACN,QAAQ;QACR,kBAAkB;QAClB,iBAAiB;QACjB,iBAAiB;QACjB,gBAAgB;QAChB,gBAAgB;QAChB,kBAAkB;QAClB,cAAc;QACd,SAAS;QACT,WAAW;QACX,YAAY;QACZ,YAAY;QACZ,eAAe;QACf,iBAAiB;QACjB,cAAc;QACd,cAAc;QACd,YAAY;QACZ,SAAS;IACX;IACA,OAAO,YAAY,CAAC,WAAW,IAAI;AACrC;AAEA,yBAAyB;AACzB,MAAM,kBAAkB,CAAC,OAAY;IACnC,IAAI,UAAU,QAAQ,UAAU,WAAW,OAAO;IAElD,iBAAiB;IACjB,IAAI,WAAW,QAAQ,CAAC,WAAW,WAAW,QAAQ,CAAC,eAAe;QACpE,IAAI;YACF,OAAO,IAAI,KAAK,OAAO,kBAAkB,CAAC;QAC5C,EAAE,UAAM;YACN,OAAO,MAAM,QAAQ;QACvB;IACF;IAEA,gBAAgB;IAChB,IAAI,WAAW,QAAQ,CAAC,aAAa,WAAW,QAAQ,CAAC,YAAY,WAAW,QAAQ,CAAC,SAAS;QAChG,MAAM,MAAM,WAAW;QACvB,OAAO,MAAM,OAAO,MAAM,QAAQ,KAAK,AAAC,GAAuB,OAArB,IAAI,cAAc,IAAG;IACjE;IAEA,mBAAmB;IACnB,IAAI,eAAe,kBAAkB;QACnC,OAAO,UAAU,SAAS,UAAU,UAAU,YAAY,SAAS,MAAM,QAAQ;IACnF;IAEA,OAAO,MAAM,QAAQ;AACvB;AAEO,MAAM,0BAA0B,CAAC,YAAiB,YAAoB,OAAe;IAC1F,OAAO,AAAC,+LA0JiC,OApJ5B,OAAM,+gHAqJsB,OADA,SAAS,WAAW,EAAC,qDAE7C,OADwB,SAAS,aAAa,EAAC,6BAY9B,OAXjB,SAAS,cAAc,EAAC,2SAYO,OADd,OAAM,4DAGhC,OAFwC,IAAI,OAAO,kBAAkB,CAAC,UAAS,sBAkJ/E,OAhJA,MAAM,OAAO,CAAC,eAAe,WAAW,MAAM,GAAG,IAAI,AAAC,2LAMlD,OAF6B,WAAW,MAAM,EAAC,4CAoB/C,OAlBA,WAAW,QAAQ,CAAC,YAAY,WAAW,QAAQ,CAAC,eAAe,AAAC,0JAOrC,OAJA,WAAW,MAAM,CAAC,CAAC,KAAa,OAAc,MAAM,CAAC,KAAK,YAAY,IAAI,KAAK,YAAY,IAAI,CAAC,GAAG,GAAG,cAAc,IAAG,0LAQvH,OAJA,KAAK,KAAK,CAAC,WAAW,MAAM,CAAC,CAAC,KAAa,OAAc,MAAM,CAAC,KAAK,YAAY,IAAI,KAAK,YAAY,IAAI,CAAC,GAAG,KAAK,WAAW,MAAM,EAAE,cAAc,IAAG,+LAQvJ,OAJA,WAAW,MAAM,CAAC,CAAC,OAAc,KAAK,cAAc,KAAK,QAAQ,MAAM,EAAC,0LAIG,OAA3E,WAAW,MAAM,CAAC,CAAC,OAAc,KAAK,cAAc,KAAK,WAAW,MAAM,EAAC,gDAExG,IAAG,kBAqBD,OApBJ,eAAe,cAAc,AAAC,0JAOC,OAJA,WAAW,MAAM,CAAC,CAAC,KAAa,OAAc,MAAM,CAAC,KAAK,QAAQ,IAAI,CAAC,GAAG,GAAG,cAAc,IAAG,iMAQ9F,OAJA,WAAW,MAAM,CAAC,CAAC,OAAc,IAAI,KAAK,KAAK,WAAW,IAAI,IAAI,QAAQ,MAAM,EAAC,8LAId,OAAnE,WAAW,MAAM,CAAC,CAAC,OAAc,CAAC,KAAK,QAAQ,IAAI,CAAC,IAAI,IAAI,MAAM,EAAC,gDAEhG,IAAG,uJAcD,OAPA,WAAW,QAAQ,CAAC,WAAY,yNAM9B,IAAG,sBAQL,OAPA,WAAW,QAAQ,CAAC,eAAgB,yNAMlC,IAAG,sBAQL,OAPA,eAAe,cAAe,iNAM5B,IAAG,sBAOL,OANA,eAAe,cAAe,wKAK5B,IAAG,sBAOL,OANA,eAAe,cAAe,kLAK5B,IAAG,sBAOL,OANA,eAAe,cAAe,kLAK5B,IAAG,sBAOP,OANE,CAAC,WAAW,QAAQ,CAAC,YAAY,CAAC,WAAW,QAAQ,CAAC,gBAAgB,eAAe,eAAe,eAAe,eAAe,eAAe,eAAe,eAAe,cAAc,AAAC,uBACnF,OAAzG,OAAO,IAAI,CAAC,UAAU,CAAC,EAAE,IAAI,CAAC,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,MAAO,AAAC,OAA+B,OAAzB,oBAAoB,MAAK,UAAQ,IAAI,CAAC,KAAI,wBACzG,IAAG,oFAoDG,OAhDV,WAAW,GAAG,CAAC,CAAC,MAAW;YAKjB,iBACA,oBAMA,iBACA,qBAKA,iBACA,kBAOA,cAQA,uBAMA;eAxCmC,AAAC,qEAG1C,OADwB,QAAQ,GAAE,6BAQlC,OAPA,WAAW,QAAQ,CAAC,WAAW,AA3wBnD,AA2wBoD,6BAE1B,OADA,KAAK,cAAc,EAAC,0CACpB,EAAA,kBAAA,KAAK,SAAS,cAAd,sCAAA,gBAAgB,IAAI,KAAI,KAAK,aAAa,IAAI,aAAY,mCAE1D,QADA,qBAAA,KAAK,YAAY,cAAjB,yCAAA,mBAAmB,cAAc,IAAG,uCAEpC,OADA,KAAK,cAAc,KAAK,SAAS,UAAU,QAAO,mCACI,OAAtD,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB,CAAC,UAAS,+BAC1D,IAAG,wBAQL,OAPA,WAAW,QAAQ,CAAC,eAAe,AAlxBvD,AAkxBwD,6BAE9B,OADA,KAAK,cAAc,EAAC,0CACpB,EAAA,kBAAA,KAAK,SAAS,cAAd,sCAAA,gBAAgB,IAAI,KAAI,YAAW,mCAEnC,QADA,sBAAA,KAAK,YAAY,cAAjB,0CAAA,oBAAmB,cAAc,IAAG,uCAEpC,OADA,KAAK,cAAc,KAAK,SAAS,UAAU,QAAO,mCACI,OAAtD,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB,CAAC,UAAS,+BAC1D,IAAG,wBAQL,OAPA,eAAe,cAAc,AAAC,6BAExB,OADA,EAAA,kBAAA,KAAK,SAAS,cAAd,sCAAA,gBAAgB,IAAI,KAAI,KAAK,aAAa,IAAI,YAAW,mCAEzD,OADA,EAAA,mBAAA,KAAK,SAAS,cAAd,uCAAA,iBAAgB,QAAQ,KAAI,KAAK,QAAQ,IAAI,YAAW,mCAExD,OADA,KAAK,QAAQ,IAAI,GAAE,mCAEnB,OADA,KAAK,WAAW,GAAG,IAAI,KAAK,KAAK,WAAW,EAAE,kBAAkB,CAAC,WAAW,YAAW,mCACpC,OAAnD,CAAC,KAAK,QAAQ,IAAI,CAAC,IAAI,KAAK,eAAe,SAAQ,+BACvD,IAAG,wBAOL,OANA,eAAe,cAAc,AAAC,6BAExB,OADA,KAAK,IAAI,KAAK,WAAW,QAAQ,SAAQ,mCAEzC,OADA,EAAA,eAAA,KAAK,MAAM,cAAX,mCAAA,aAAa,cAAc,OAAM,GAAE,uCAEnC,OADA,KAAK,WAAW,IAAI,YAAW,mCACsD,OAArF,KAAK,UAAU,GAAG,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB,CAAC,WAAW,YAAW,+BACzF,IAAG,wBAOL,OANA,eAAe,cAAc,AAAC,6BAExB,OADA,KAAK,IAAI,IAAI,YAAW,mCAExB,OADA,KAAK,KAAK,IAAI,YAAW,mCAEzB,OADA,KAAK,OAAO,IAAI,YAAW,mCACiB,OAA5C,EAAA,wBAAA,KAAK,eAAe,cAApB,4CAAA,sBAAsB,cAAc,OAAM,GAAE,mCAChD,IAAG,wBAOL,OANA,eAAe,cAAc,AAAC,6BAExB,OADA,KAAK,IAAI,IAAI,YAAW,mCAExB,OADA,KAAK,KAAK,IAAI,YAAW,mCAEzB,OADA,KAAK,OAAO,IAAI,YAAW,mCACiB,OAA5C,EAAA,yBAAA,KAAK,eAAe,cAApB,6CAAA,uBAAsB,cAAc,OAAM,GAAE,mCAChD,IAAG,wBAKA,OAJL,CAAC,WAAW,QAAQ,CAAC,YAAY,CAAC,WAAW,QAAQ,CAAC,gBAAgB,eAAe,eAAe,eAAe,eAAe,eAAe,eAAe,eAAe,cAAc,AAAC,yBAGlL,OAFV,OAAO,IAAI,CAAC,UAAU,CAAC,EAAE,IAAI,CAAC,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,MAAO,AAAC,+BACnB,OAAhC,gBAAgB,IAAI,CAAC,IAAI,EAAE,MAAK,gCACrC,IAAI,CAAC,KAAI,0BACV,IAAG;OAER,IAAI,CAAC,KAAI,0DAGb,sJAIH;AAKV;AAEO,MAAM,0BAA0B,CAAC,cAAmB;QAIpD,yBACA,yBAwQwD,0BAAwC,0BAIxC,0BAA0C,0BAgDjE;IAhUtC,MAAM,QAAQ,aAAa,YAAY,IAAI,EAAE;IAC7C,MAAM,mBAAmB,aAAa,IAAI,KAAK;IAC/C,MAAM,mBAAmB,mBACpB,EAAA,0BAAA,aAAa,SAAS,cAAtB,8CAAA,wBAAwB,IAAI,KAAI,aAChC,EAAA,0BAAA,aAAa,SAAS,cAAtB,8CAAA,wBAAwB,IAAI,KAAI,aAAa,aAAa,IAAI;IAEnE,OAAO,AAAC,2MA0NiC,OApNhB,aAAa,aAAa,EAAC,k6JAqNX,OADA,SAAS,WAAW,EAAC,qDAErB,OADA,SAAS,aAAa,EAAC,qDAa5C,OAZqB,SAAS,cAAc,EAAC,yUAmBxB,OAPrB,mBAAmB,YAAY,UAAS,oPAWnB,OAJA,aAAa,aAAa,EAAC,0KAQ3B,OAJA,IAAI,KAAK,aAAa,UAAU,EAAE,kBAAkB,CAAC,UAAS,uLAQ9D,OAJA,aAAa,uBAAuB,IAAI,YAAW,8KAUnD,OANA,mBAAmB,iBAAiB,mBAAkB,2KAOtD,OADA,mBAAmB,YAAY,WAAU,sDAKzC,OAJA,kBAAiB,yKAQjB,OAJA,mBAAoB,EAAA,2BAAA,aAAa,SAAS,cAAtB,+CAAA,yBAAwB,KAAK,KAAI,KAAO,EAAA,2BAAA,aAAa,SAAS,cAAtB,+CAAA,yBAAwB,KAAK,KAAI,IAAI,0KAsB9H,OAlB6B,mBAAoB,EAAA,2BAAA,aAAa,SAAS,cAAtB,+CAAA,yBAAwB,OAAO,KAAI,KAAO,EAAA,2BAAA,aAAa,SAAS,cAAtB,+CAAA,yBAAwB,OAAO,KAAI,IAAI,qkBA8BlI,OAZA,CAAA,kBAAA,4BAAA,MAAO,GAAG,CAAC,CAAC,MAAW;YAGyB,iBAAwB;eAH/B,AAAC,6CAGhB,OADlB,QAAQ,GAAE,iDAEV,OADkB,KAAK,aAAa,MAAI,kBAAA,KAAK,SAAS,cAAd,sCAAA,gBAAgB,IAAI,OAAI,iBAAA,KAAK,QAAQ,cAAb,qCAAA,eAAe,IAAI,KAAI,YAAW,+BAElG,OADA,KAAK,QAAQ,EAAC,+BAEd,OADA,CAAC,KAAK,UAAU,IAAI,CAAC,EAAE,cAAc,IAAG,+BAEf,OADzB,CAAC,KAAK,YAAY,IAAI,CAAC,EAAE,cAAc,IAAG,wDAE1C,OADyB,KAAK,aAAa,IAAI,YAAW,+BACyC,OAAnG,KAAK,WAAW,GAAG,IAAI,KAAK,KAAK,WAAW,EAAE,kBAAkB,CAAC,SAAS,OAAO,CAAC,OAAO,OAAO,IAAG;OAE1G,IAAI,CAAC,QAAO,IAAG,oBAoBU,OAlB1B,MAAM,IAAI,CAAC;QAAE,QAAQ,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,CAAA,kBAAA,4BAAA,MAAO,MAAM,KAAI,CAAC;IAAG,GAAG,CAAC,GAAG,IAAO,kSAUxE,IAAI,CAAC,KAAI,kOAeP,OAPuB,EAAA,6BAAA,aAAa,YAAY,cAAzB,iDAAA,2BAA2B,cAAc,OAAM,GAAE,0MAuB3E,OAhBG,aAAa,KAAK,IAAI,IAAG,4YAgBiB,OAA7C,SAAS,UAAU,IAAI,uBAAsB;AAO3D", "debugId": null}}, {"offset": {"line": 4738, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%287%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/hooks/usePrintSettings.ts"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\n\nexport interface PrintSettings {\n  companyName: string\n  companyNameEn: string\n  companyAddress: string\n  companyPhone: string\n  companyEmail: string\n  logo?: string\n  showLogo: boolean\n  showHeader: boolean\n  showFooter: boolean\n  footerText: string\n  fontSize: 'small' | 'medium' | 'large'\n  paperSize: 'A4' | 'A5' | 'thermal'\n  showBorders: boolean\n  showColors: boolean\n  includeBarcode: boolean\n  includeQRCode: boolean\n  watermark?: string\n  showWatermark: boolean\n  headerColor: string\n  accentColor: string\n  textColor: string\n  backgroundColor: string\n}\n\nconst defaultSettings: PrintSettings = {\n  companyName: 'مكتب لارين العلمي',\n  companyNameEn: 'LAREN SCIENTIFIC BUREAU',\n  companyAddress: 'بغداد - شارع فلسطين',\n  companyPhone: '+964 ************',\n  companyEmail: '<EMAIL>',\n  showLogo: true,\n  showHeader: true,\n  showFooter: true,\n  footerText: 'شكراً لتعاملكم معنا',\n  fontSize: 'medium',\n  paperSize: 'A4',\n  showBorders: true,\n  showColors: false,\n  includeBarcode: false,\n  includeQRCode: false,\n  showWatermark: false,\n  headerColor: '#1f2937',\n  accentColor: '#3b82f6',\n  textColor: '#374151',\n  backgroundColor: '#ffffff'\n}\n\nexport function usePrintSettings() {\n  const [settings, setSettings] = useState<PrintSettings>(defaultSettings)\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    loadSettings()\n  }, [])\n\n  const loadSettings = () => {\n    try {\n      const savedSettings = localStorage.getItem('printSettings')\n      if (savedSettings) {\n        const parsed = JSON.parse(savedSettings)\n        setSettings({ ...defaultSettings, ...parsed })\n      }\n    } catch (error) {\n      console.error('Error loading print settings:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const updateSettings = (newSettings: Partial<PrintSettings>) => {\n    const updatedSettings = { ...settings, ...newSettings }\n    setSettings(updatedSettings)\n    \n    try {\n      localStorage.setItem('printSettings', JSON.stringify(updatedSettings))\n    } catch (error) {\n      console.error('Error saving print settings:', error)\n    }\n  }\n\n  const resetSettings = () => {\n    setSettings(defaultSettings)\n    try {\n      localStorage.removeItem('printSettings')\n    } catch (error) {\n      console.error('Error resetting print settings:', error)\n    }\n  }\n\n  const exportSettings = () => {\n    const dataStr = JSON.stringify(settings, null, 2)\n    const dataBlob = new Blob([dataStr], { type: 'application/json' })\n    const url = URL.createObjectURL(dataBlob)\n    const link = document.createElement('a')\n    link.href = url\n    link.download = 'print-settings.json'\n    link.click()\n    URL.revokeObjectURL(url)\n  }\n\n  const importSettings = (file: File) => {\n    return new Promise<void>((resolve, reject) => {\n      const reader = new FileReader()\n      reader.onload = (e) => {\n        try {\n          const importedSettings = JSON.parse(e.target?.result as string)\n          updateSettings(importedSettings)\n          resolve()\n        } catch (error) {\n          reject(error)\n        }\n      }\n      reader.onerror = () => reject(new Error('Failed to read file'))\n      reader.readAsText(file)\n    })\n  }\n\n  return {\n    settings,\n    printSettings: settings, // alias for compatibility\n    loading,\n    updateSettings,\n    resetSettings,\n    exportSettings,\n    importSettings\n  }\n}\n\n// Print utility functions\nexport const printInvoice = async (invoice: any, type: 'sales' | 'purchase' | 'return', settings: PrintSettings) => {\n  try {\n    console.log('🖨️ بدء طباعة الفاتورة:', invoice)\n\n    // If we have an invoice ID, fetch fresh data from database for accurate printing\n    let printData = invoice\n    if (invoice.id && type === 'sales') {\n      const { getSalesInvoiceForPrint } = require('@/lib/database')\n      const result = await getSalesInvoiceForPrint(invoice.id)\n      if (result.success && result.data) {\n        printData = {\n          ...result.data,\n          // Preserve any additional data from the original invoice\n          customerName: invoice.customerName || result.data.customer_name,\n          customerPhone: invoice.customerPhone || result.data.customers?.phone,\n          customerAddress: invoice.customerAddress || result.data.customers?.address,\n        }\n        console.log('✅ تم استرجاع بيانات فاتورة المبيعات من قاعدة البيانات:', printData)\n      }\n    } else if (invoice.id && type === 'purchase') {\n      const { getPurchaseInvoiceForPrint } = require('@/lib/database')\n      const result = await getPurchaseInvoiceForPrint(invoice.id)\n      if (result.success && result.data) {\n        printData = {\n          ...result.data,\n          // Preserve any additional data from the original invoice\n          supplierName: invoice.supplierName || result.data.suppliers?.name,\n          supplierPhone: invoice.supplierPhone || result.data.suppliers?.phone,\n          supplierAddress: invoice.supplierAddress || result.data.suppliers?.address,\n        }\n        console.log('✅ تم استرجاع بيانات فاتورة المشتريات من قاعدة البيانات:', printData)\n      }\n    } else if (invoice.id && type === 'return') {\n      const { getReturnForPrint } = require('@/lib/database')\n      const result = await getReturnForPrint(invoice.id)\n      if (result.success && result.data) {\n        printData = {\n          ...result.data,\n          // Preserve any additional data from the original return\n          customerName: invoice.customerName || result.data.customer_name,\n          supplierName: invoice.supplierName || result.data.supplier_name,\n        }\n        console.log('✅ تم استرجاع بيانات المرتجع من قاعدة البيانات:', printData)\n      }\n    }\n\n    const printWindow = window.open('', '_blank')\n    if (!printWindow) {\n      console.error('❌ فشل في فتح نافذة الطباعة')\n      return\n    }\n\n    // Use Laren template by default\n    const { generateLarenInvoiceHTML } = require('@/utils/larenPrintTemplate')\n    const invoiceHTML = generateLarenInvoiceHTML(printData, type, settings)\n\n    printWindow.document.write(invoiceHTML)\n    printWindow.document.close()\n    printWindow.focus()\n    printWindow.print()\n    printWindow.close()\n\n    console.log('✅ تمت الطباعة بنجاح')\n  } catch (error) {\n    console.error('❌ خطأ في الطباعة:', error)\n\n    // Fallback to original method\n    const printWindow = window.open('', '_blank')\n    if (!printWindow) return\n\n    const { generateLarenInvoiceHTML } = require('@/utils/larenPrintTemplate')\n    const invoiceHTML = generateLarenInvoiceHTML(invoice, type, settings)\n\n    printWindow.document.write(invoiceHTML)\n    printWindow.document.close()\n    printWindow.focus()\n    printWindow.print()\n    printWindow.close()\n  }\n}\n\nexport const printReport = (reportData: any, reportType: string, title: string, settings: PrintSettings) => {\n  const printWindow = window.open('', '_blank')\n  if (!printWindow) return\n\n  // Use Laren template by default\n  const { generateLarenReportHTML } = require('@/utils/larenPrintTemplate')\n  const reportHTML = generateLarenReportHTML(reportData, reportType, title, settings)\n\n  printWindow.document.write(reportHTML)\n  printWindow.document.close()\n  printWindow.focus()\n  printWindow.print()\n  printWindow.close()\n}\n\nconst generateInvoiceHTML = (invoice: any, type: 'sales' | 'purchase', settings: PrintSettings) => {\n  const items = type === 'sales' ? invoice.sales_invoice_items : invoice.purchase_invoice_items\n  const customerSupplier = type === 'sales' \n    ? (invoice.customers?.name || invoice.customer_name || 'عميل نقدي')\n    : (invoice.suppliers?.name || 'غير محدد')\n\n  return `\n    <!DOCTYPE html>\n    <html dir=\"rtl\" lang=\"ar\">\n    <head>\n      <meta charset=\"UTF-8\">\n      <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n      <title>${type === 'sales' ? 'فاتورة مبيعات' : 'فاتورة مشتريات'} - ${invoice.invoice_number}</title>\n      <style>\n        * { margin: 0; padding: 0; box-sizing: border-box; }\n        body { \n          font-family: 'Arial', sans-serif; \n          font-size: ${settings.fontSize === 'small' ? '12px' : settings.fontSize === 'large' ? '16px' : '14px'};\n          color: ${settings.textColor};\n          background-color: ${settings.backgroundColor};\n          line-height: 1.6;\n        }\n        .container { \n          max-width: ${settings.paperSize === 'A5' ? '600px' : settings.paperSize === 'thermal' ? '300px' : '800px'}; \n          margin: 0 auto; \n          padding: 20px;\n          ${settings.showBorders ? 'border: 1px solid #ddd;' : ''}\n        }\n        .header { \n          text-align: center; \n          margin-bottom: 30px; \n          ${settings.showHeader ? '' : 'display: none;'}\n          border-bottom: 2px solid ${settings.accentColor};\n          padding-bottom: 20px;\n        }\n        .logo { \n          width: 80px; \n          height: 80px; \n          background: ${settings.accentColor}; \n          border-radius: 50%; \n          margin: 0 auto 15px; \n          display: flex; \n          align-items: center; \n          justify-content: center; \n          color: white; \n          font-size: 24px; \n          font-weight: bold;\n          ${settings.showLogo ? '' : 'display: none;'}\n        }\n        .company-name { \n          font-size: 24px; \n          font-weight: bold; \n          color: ${settings.headerColor}; \n          margin-bottom: 10px; \n        }\n        .company-info { color: #666; margin-bottom: 5px; }\n        .invoice-title { \n          text-align: center; \n          font-size: 20px; \n          font-weight: bold; \n          margin: 20px 0; \n          color: ${settings.headerColor};\n        }\n        .invoice-details { \n          display: flex; \n          justify-content: space-between; \n          margin-bottom: 30px; \n        }\n        .invoice-info, .customer-info { flex: 1; }\n        .customer-info { text-align: left; }\n        table { \n          width: 100%; \n          border-collapse: collapse; \n          margin-bottom: 20px; \n        }\n        th, td { \n          border: 1px solid #ddd; \n          padding: 10px; \n          text-align: center; \n        }\n        th { \n          background-color: ${settings.showColors ? settings.accentColor : '#f5f5f5'}; \n          color: ${settings.showColors ? 'white' : settings.textColor};\n          font-weight: bold;\n        }\n        .totals { \n          width: 300px; \n          margin-left: auto; \n          border: 1px solid #ddd; \n        }\n        .totals tr:last-child { \n          font-weight: bold; \n          background-color: ${settings.showColors ? settings.accentColor : '#f5f5f5'};\n          color: ${settings.showColors ? 'white' : settings.textColor};\n        }\n        .footer { \n          text-align: center; \n          margin-top: 30px; \n          padding-top: 20px; \n          border-top: 1px solid #ddd; \n          color: #666;\n          ${settings.showFooter ? '' : 'display: none;'}\n        }\n        .notes { \n          margin-top: 20px; \n          padding: 15px; \n          background-color: #f9f9f9; \n          border-left: 4px solid ${settings.accentColor};\n        }\n        @media print {\n          body { margin: 0; }\n          .container { box-shadow: none; border: none; }\n        }\n      </style>\n    </head>\n    <body>\n      <div class=\"container\">\n        <div class=\"header\">\n          <div class=\"logo\">${settings.companyName.charAt(0)}</div>\n          <div class=\"company-name\">${settings.companyName}</div>\n          <div class=\"company-info\">${settings.companyAddress}</div>\n          <div class=\"company-info\">${settings.companyPhone}</div>\n          <div class=\"company-info\">${settings.companyEmail}</div>\n        </div>\n\n        <div class=\"invoice-title\">${type === 'sales' ? 'فاتورة مبيعات' : 'فاتورة مشتريات'}</div>\n\n        <div class=\"invoice-details\">\n          <div class=\"invoice-info\">\n            <div><strong>رقم الفاتورة:</strong> ${invoice.invoice_number}</div>\n            <div><strong>التاريخ:</strong> ${new Date(invoice.created_at).toLocaleDateString('ar-EG')}</div>\n            <div><strong>طريقة الدفع:</strong> ${invoice.payment_method === 'cash' ? 'نقداً' : 'آجل'}</div>\n            <div><strong>حالة الدفع:</strong> ${invoice.payment_status === 'paid' ? 'مدفوع' : 'معلق'}</div>\n          </div>\n          <div class=\"customer-info\">\n            <div><strong>${type === 'sales' ? 'بيانات العميل' : 'بيانات المورد'}:</strong></div>\n            <div>${customerSupplier}</div>\n            ${type === 'sales' && invoice.customers?.phone ? `<div>الهاتف: ${invoice.customers.phone}</div>` : ''}\n            ${type === 'purchase' && invoice.suppliers?.phone ? `<div>الهاتف: ${invoice.suppliers.phone}</div>` : ''}\n          </div>\n        </div>\n\n        <table>\n          <thead>\n            <tr>\n              <th>اسم الدواء</th>\n              <th>الكمية</th>\n              <th>سعر الوحدة</th>\n              <th>المجموع</th>\n            </tr>\n          </thead>\n          <tbody>\n            ${items?.map((item: any) => `\n              <tr>\n                <td>${type === 'sales'\n                  ? (item.medicine_name || item.medicineName || item.medicine_batches?.medicines?.name || 'غير محدد')\n                  : (item.medicine_name || item.medicineName || item.medicines?.name || 'غير محدد')\n                }</td>\n                <td>${item.quantity}</td>\n                <td>${type === 'sales'\n                  ? (item.unit_price || 0).toLocaleString()\n                  : (item.unit_cost || item.unitCost || 0).toLocaleString()\n                } د.ع</td>\n                <td>${type === 'sales'\n                  ? (item.total_price || 0).toLocaleString()\n                  : (item.total_cost || item.totalCost || 0).toLocaleString()\n                } د.ع</td>\n              </tr>\n            `).join('') || ''}\n          </tbody>\n        </table>\n\n        <table class=\"totals\">\n          <tr>\n            <td>المجموع الفرعي:</td>\n            <td>${invoice.total_amount?.toLocaleString()} د.ع</td>\n          </tr>\n          <tr>\n            <td>الخصم:</td>\n            <td>${invoice.discount_amount?.toLocaleString() || 0} د.ع</td>\n          </tr>\n          <tr>\n            <td>المجموع النهائي:</td>\n            <td>${invoice.final_amount?.toLocaleString()} د.ع</td>\n          </tr>\n        </table>\n\n        ${invoice.notes ? `\n          <div class=\"notes\">\n            <strong>ملاحظات:</strong><br>\n            ${invoice.notes}\n          </div>\n        ` : ''}\n\n        <div class=\"footer\">\n          <div>${settings.footerText}</div>\n          <div style=\"margin-top: 10px; font-size: 12px;\">\n            تم إنشاء هذا المستند بواسطة نظام إدارة الصيدلية - ${new Date().toLocaleDateString('ar-EG')}\n          </div>\n        </div>\n      </div>\n    </body>\n    </html>\n  `\n}\n\nconst generateReportHTML = (reportData: any, reportType: string, title: string, settings: PrintSettings) => {\n  return `\n    <!DOCTYPE html>\n    <html dir=\"rtl\" lang=\"ar\">\n    <head>\n      <meta charset=\"UTF-8\">\n      <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n      <title>${title}</title>\n      <style>\n        * { margin: 0; padding: 0; box-sizing: border-box; }\n        body { \n          font-family: 'Arial', sans-serif; \n          font-size: ${settings.fontSize === 'small' ? '11px' : settings.fontSize === 'large' ? '15px' : '13px'};\n          color: ${settings.textColor};\n          background-color: ${settings.backgroundColor};\n          line-height: 1.5;\n        }\n        .container { \n          max-width: 100%; \n          margin: 0 auto; \n          padding: 20px;\n        }\n        .header { \n          text-align: center; \n          margin-bottom: 30px; \n          ${settings.showHeader ? '' : 'display: none;'}\n          border-bottom: 2px solid ${settings.accentColor};\n          padding-bottom: 20px;\n        }\n        .company-name { \n          font-size: 20px; \n          font-weight: bold; \n          color: ${settings.headerColor}; \n          margin-bottom: 10px; \n        }\n        .report-title { \n          text-align: center; \n          font-size: 18px; \n          font-weight: bold; \n          margin: 20px 0; \n          color: ${settings.headerColor};\n        }\n        .summary { \n          display: flex; \n          justify-content: space-around; \n          margin-bottom: 30px; \n          flex-wrap: wrap;\n        }\n        .summary-item { \n          text-align: center; \n          padding: 15px; \n          border: 1px solid #ddd; \n          border-radius: 5px; \n          margin: 5px;\n          min-width: 150px;\n          background-color: ${settings.showColors ? '#f8f9fa' : 'transparent'};\n        }\n        .summary-label { \n          font-size: 12px; \n          color: #666; \n          margin-bottom: 5px; \n        }\n        .summary-value { \n          font-size: 18px; \n          font-weight: bold; \n          color: ${settings.accentColor};\n        }\n        table { \n          width: 100%; \n          border-collapse: collapse; \n          margin-bottom: 20px; \n          font-size: 11px;\n        }\n        th, td { \n          border: 1px solid #ddd; \n          padding: 8px; \n          text-align: center; \n        }\n        th { \n          background-color: ${settings.showColors ? settings.accentColor : '#f5f5f5'}; \n          color: ${settings.showColors ? 'white' : settings.textColor};\n          font-weight: bold;\n        }\n        .footer { \n          text-align: center; \n          margin-top: 30px; \n          padding-top: 20px; \n          border-top: 1px solid #ddd; \n          color: #666;\n          ${settings.showFooter ? '' : 'display: none;'}\n        }\n        @media print {\n          body { margin: 0; }\n          .container { box-shadow: none; }\n          table { page-break-inside: auto; }\n          tr { page-break-inside: avoid; page-break-after: auto; }\n        }\n      </style>\n    </head>\n    <body>\n      <div class=\"container\">\n        <div class=\"header\">\n          <div class=\"company-name\">${settings.companyName}</div>\n          <div>${settings.companyAddress}</div>\n        </div>\n\n        <div class=\"report-title\">${title}</div>\n        <div style=\"text-align: center; margin-bottom: 20px; color: #666;\">\n          تاريخ الطباعة: ${new Date().toLocaleDateString('ar-EG')}\n        </div>\n\n        ${Array.isArray(reportData) && reportData.length > 0 ? `\n          <div class=\"summary\">\n            <div class=\"summary-item\">\n              <div class=\"summary-label\">عدد السجلات</div>\n              <div class=\"summary-value\">${reportData.length}</div>\n            </div>\n            ${reportType.includes('sales') || reportType.includes('purchases') ? `\n              <div class=\"summary-item\">\n                <div class=\"summary-label\">إجمالي المبلغ</div>\n                <div class=\"summary-value\">${reportData.reduce((sum: number, item: any) => sum + (item.final_amount || 0), 0).toLocaleString()} د.ع</div>\n              </div>\n            ` : ''}\n          </div>\n\n          <table>\n            <thead>\n              <tr>\n                ${reportType.includes('sales') ? `\n                  <th>رقم الفاتورة</th>\n                  <th>العميل</th>\n                  <th>المبلغ النهائي</th>\n                  <th>حالة الدفع</th>\n                  <th>التاريخ</th>\n                ` : ''}\n                ${reportType.includes('purchases') ? `\n                  <th>رقم الفاتورة</th>\n                  <th>المورد</th>\n                  <th>المبلغ النهائي</th>\n                  <th>حالة الدفع</th>\n                  <th>التاريخ</th>\n                ` : ''}\n                ${reportType === 'inventory' ? `\n                  <th>اسم الدواء</th>\n                  <th>الفئة</th>\n                  <th>الكمية</th>\n                  <th>تاريخ الانتهاء</th>\n                  <th>الحالة</th>\n                ` : ''}\n                ${reportType === 'cashbox' ? `\n                  <th>النوع</th>\n                  <th>الفئة</th>\n                  <th>الوصف</th>\n                  <th>المبلغ</th>\n                  <th>التاريخ</th>\n                ` : ''}\n              </tr>\n            </thead>\n            <tbody>\n              ${reportData.slice(0, 100).map((item: any) => `\n                <tr>\n                  ${reportType.includes('sales') ? `\n                    <td>${item.invoice_number}</td>\n                    <td>${item.customers?.name || item.customer_name || 'عميل نقدي'}</td>\n                    <td>${item.final_amount?.toLocaleString()} د.ع</td>\n                    <td>${item.payment_status === 'paid' ? 'مدفوع' : 'معلق'}</td>\n                    <td>${new Date(item.created_at).toLocaleDateString('ar-EG')}</td>\n                  ` : ''}\n                  ${reportType.includes('purchases') ? `\n                    <td>${item.invoice_number}</td>\n                    <td>${item.suppliers?.name || 'غير محدد'}</td>\n                    <td>${item.final_amount?.toLocaleString()} د.ع</td>\n                    <td>${item.payment_status === 'paid' ? 'مدفوع' : 'معلق'}</td>\n                    <td>${new Date(item.created_at).toLocaleDateString('ar-EG')}</td>\n                  ` : ''}\n                  ${reportType === 'inventory' ? `\n                    <td>${item.medicines?.name || 'غير محدد'}</td>\n                    <td>${item.medicines?.category || 'غير محدد'}</td>\n                    <td>${item.quantity}</td>\n                    <td>${new Date(item.expiry_date).toLocaleDateString('ar-EG')}</td>\n                    <td>${item.quantity < 10 ? 'كمية قليلة' : 'طبيعي'}</td>\n                  ` : ''}\n                  ${reportType === 'cashbox' ? `\n                    <td>${item.transaction_type === 'income' ? 'وارد' : 'مصروف'}</td>\n                    <td>${item.category}</td>\n                    <td>${item.description}</td>\n                    <td>${item.transaction_type === 'income' ? '+' : '-'}${item.amount?.toLocaleString()} د.ع</td>\n                    <td>${new Date(item.created_at).toLocaleDateString('ar-EG')}</td>\n                  ` : ''}\n                </tr>\n              `).join('')}\n            </tbody>\n          </table>\n\n          ${reportData.length > 100 ? `\n            <div style=\"text-align: center; color: #666; margin-top: 20px;\">\n              تم عرض أول 100 سجل من أصل ${reportData.length} سجل\n            </div>\n          ` : ''}\n        ` : `\n          <div style=\"text-align: center; padding: 50px; color: #666;\">\n            لا توجد بيانات للعرض\n          </div>\n        `}\n\n        <div class=\"footer\">\n          <div>${settings.footerText}</div>\n          <div style=\"margin-top: 10px; font-size: 11px;\">\n            تم إنشاء هذا التقرير بواسطة نظام إدارة الصيدلية\n          </div>\n        </div>\n      </div>\n    </body>\n    </html>\n  `\n}\n"], "names": [], "mappings": ";;;;;AAEA;;AAFA;;AA6BA,MAAM,kBAAiC;IACrC,aAAa;IACb,eAAe;IACf,gBAAgB;IAChB,cAAc;IACd,cAAc;IACd,UAAU;IACV,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,UAAU;IACV,WAAW;IACX,aAAa;IACb,YAAY;IACZ,gBAAgB;IAChB,eAAe;IACf,eAAe;IACf,aAAa;IACb,aAAa;IACb,WAAW;IACX,iBAAiB;AACnB;AAEO,SAAS;;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACxD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR;QACF;qCAAG,EAAE;IAEL,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,gBAAgB,aAAa,OAAO,CAAC;YAC3C,IAAI,eAAe;gBACjB,MAAM,SAAS,KAAK,KAAK,CAAC;gBAC1B,YAAY;oBAAE,GAAG,eAAe;oBAAE,GAAG,MAAM;gBAAC;YAC9C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QACjD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,kBAAkB;YAAE,GAAG,QAAQ;YAAE,GAAG,WAAW;QAAC;QACtD,YAAY;QAEZ,IAAI;YACF,aAAa,OAAO,CAAC,iBAAiB,KAAK,SAAS,CAAC;QACvD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF;IAEA,MAAM,gBAAgB;QACpB,YAAY;QACZ,IAAI;YACF,aAAa,UAAU,CAAC;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;QACnD;IACF;IAEA,MAAM,iBAAiB;QACrB,MAAM,UAAU,KAAK,SAAS,CAAC,UAAU,MAAM;QAC/C,MAAM,WAAW,IAAI,KAAK;YAAC;SAAQ,EAAE;YAAE,MAAM;QAAmB;QAChE,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,IAAI,GAAG;QACZ,KAAK,QAAQ,GAAG;QAChB,KAAK,KAAK;QACV,IAAI,eAAe,CAAC;IACtB;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,IAAI,QAAc,CAAC,SAAS;YACjC,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,GAAG,CAAC;gBACf,IAAI;wBACkC;oBAApC,MAAM,mBAAmB,KAAK,KAAK,EAAC,YAAA,EAAE,MAAM,cAAR,gCAAA,UAAU,MAAM;oBACpD,eAAe;oBACf;gBACF,EAAE,OAAO,OAAO;oBACd,OAAO;gBACT;YACF;YACA,OAAO,OAAO,GAAG,IAAM,OAAO,IAAI,MAAM;YACxC,OAAO,UAAU,CAAC;QACpB;IACF;IAEA,OAAO;QACL;QACA,eAAe;QACf;QACA;QACA;QACA;QACA;IACF;AACF;GA/EgB;AAkFT,MAAM,eAAe,OAAO,SAAc,MAAuC;IACtF,IAAI;QACF,QAAQ,GAAG,CAAC,2BAA2B;QAEvC,iFAAiF;QACjF,IAAI,YAAY;QAChB,IAAI,QAAQ,EAAE,IAAI,SAAS,SAAS;YAClC,MAAM,EAAE,uBAAuB,EAAE;YACjC,MAAM,SAAS,MAAM,wBAAwB,QAAQ,EAAE;YACvD,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;oBAKS,wBACI;gBAL9C,YAAY;oBACV,GAAG,OAAO,IAAI;oBACd,yDAAyD;oBACzD,cAAc,QAAQ,YAAY,IAAI,OAAO,IAAI,CAAC,aAAa;oBAC/D,eAAe,QAAQ,aAAa,MAAI,yBAAA,OAAO,IAAI,CAAC,SAAS,cAArB,6CAAA,uBAAuB,KAAK;oBACpE,iBAAiB,QAAQ,eAAe,MAAI,0BAAA,OAAO,IAAI,CAAC,SAAS,cAArB,8CAAA,wBAAuB,OAAO;gBAC5E;gBACA,QAAQ,GAAG,CAAC,0DAA0D;YACxE;QACF,OAAO,IAAI,QAAQ,EAAE,IAAI,SAAS,YAAY;YAC5C,MAAM,EAAE,0BAA0B,EAAE;YACpC,MAAM,SAAS,MAAM,2BAA2B,QAAQ,EAAE;YAC1D,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;oBAIO,wBACE,yBACI;gBAL9C,YAAY;oBACV,GAAG,OAAO,IAAI;oBACd,yDAAyD;oBACzD,cAAc,QAAQ,YAAY,MAAI,yBAAA,OAAO,IAAI,CAAC,SAAS,cAArB,6CAAA,uBAAuB,IAAI;oBACjE,eAAe,QAAQ,aAAa,MAAI,0BAAA,OAAO,IAAI,CAAC,SAAS,cAArB,8CAAA,wBAAuB,KAAK;oBACpE,iBAAiB,QAAQ,eAAe,MAAI,0BAAA,OAAO,IAAI,CAAC,SAAS,cAArB,8CAAA,wBAAuB,OAAO;gBAC5E;gBACA,QAAQ,GAAG,CAAC,2DAA2D;YACzE;QACF,OAAO,IAAI,QAAQ,EAAE,IAAI,SAAS,UAAU;YAC1C,MAAM,EAAE,iBAAiB,EAAE;YAC3B,MAAM,SAAS,MAAM,kBAAkB,QAAQ,EAAE;YACjD,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;gBACjC,YAAY;oBACV,GAAG,OAAO,IAAI;oBACd,wDAAwD;oBACxD,cAAc,QAAQ,YAAY,IAAI,OAAO,IAAI,CAAC,aAAa;oBAC/D,cAAc,QAAQ,YAAY,IAAI,OAAO,IAAI,CAAC,aAAa;gBACjE;gBACA,QAAQ,GAAG,CAAC,kDAAkD;YAChE;QACF;QAEA,MAAM,cAAc,OAAO,IAAI,CAAC,IAAI;QACpC,IAAI,CAAC,aAAa;YAChB,QAAQ,KAAK,CAAC;YACd;QACF;QAEA,gCAAgC;QAChC,MAAM,EAAE,wBAAwB,EAAE;QAClC,MAAM,cAAc,yBAAyB,WAAW,MAAM;QAE9D,YAAY,QAAQ,CAAC,KAAK,CAAC;QAC3B,YAAY,QAAQ,CAAC,KAAK;QAC1B,YAAY,KAAK;QACjB,YAAY,KAAK;QACjB,YAAY,KAAK;QAEjB,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QAEnC,8BAA8B;QAC9B,MAAM,cAAc,OAAO,IAAI,CAAC,IAAI;QACpC,IAAI,CAAC,aAAa;QAElB,MAAM,EAAE,wBAAwB,EAAE;QAClC,MAAM,cAAc,yBAAyB,SAAS,MAAM;QAE5D,YAAY,QAAQ,CAAC,KAAK,CAAC;QAC3B,YAAY,QAAQ,CAAC,KAAK;QAC1B,YAAY,KAAK;QACjB,YAAY,KAAK;QACjB,YAAY,KAAK;IACnB;AACF;AAEO,MAAM,cAAc,CAAC,YAAiB,YAAoB,OAAe;IAC9E,MAAM,cAAc,OAAO,IAAI,CAAC,IAAI;IACpC,IAAI,CAAC,aAAa;IAElB,gCAAgC;IAChC,MAAM,EAAE,uBAAuB,EAAE;IACjC,MAAM,aAAa,wBAAwB,YAAY,YAAY,OAAO;IAE1E,YAAY,QAAQ,CAAC,KAAK,CAAC;IAC3B,YAAY,QAAQ,CAAC,KAAK;IAC1B,YAAY,KAAK;IACjB,YAAY,KAAK;IACjB,YAAY,KAAK;AACnB;AAEA,MAAM,sBAAsB,CAAC,SAAc,MAA4B;QAGhE,oBACA,oBAqI2B,qBACG,qBAqCnB,uBAIA,0BAIA;IAtLhB,MAAM,QAAQ,SAAS,UAAU,QAAQ,mBAAmB,GAAG,QAAQ,sBAAsB;IAC7F,MAAM,mBAAmB,SAAS,UAC7B,EAAA,qBAAA,QAAQ,SAAS,cAAjB,yCAAA,mBAAmB,IAAI,KAAI,QAAQ,aAAa,IAAI,cACpD,EAAA,qBAAA,QAAQ,SAAS,cAAjB,yCAAA,mBAAmB,IAAI,KAAI;IAEhC,OAAO,AA5OT,AA4OU,+LAMgE,OAA3D,SAAS,UAAU,kBAAkB,kBAAiB,OAK9C,OALmD,QAAQ,cAAc,EAAC,+KAM9E,OADI,SAAS,QAAQ,KAAK,UAAU,SAAS,SAAS,QAAQ,KAAK,UAAU,SAAS,QAAO,wBAElF,OADX,SAAS,SAAS,EAAC,mCAKf,OAJO,SAAS,eAAe,EAAC,2FAO3C,OAHW,SAAS,SAAS,KAAK,OAAO,UAAU,SAAS,SAAS,KAAK,YAAY,UAAU,SAAQ,wEAQxG,OALA,SAAS,WAAW,GAAG,4BAA4B,IAAG,gHAM7B,OADzB,SAAS,UAAU,GAAG,KAAK,kBAAiB,yCAOhC,OANa,SAAS,WAAW,EAAC,8IAe9C,OATY,SAAS,WAAW,EAAC,yQAc1B,OALP,SAAS,QAAQ,GAAG,KAAK,kBAAiB,wHAcnC,OATA,SAAS,WAAW,EAAC,oRA6BV,OApBX,SAAS,WAAW,EAAC,0iBAqBrB,OADW,SAAS,UAAU,GAAG,SAAS,WAAW,GAAG,WAAU,yBAWvD,OAVX,SAAS,UAAU,GAAG,UAAU,SAAS,SAAS,EAAC,yQAWnD,OADW,SAAS,UAAU,GAAG,SAAS,WAAW,GAAG,WAAU,wBASzE,OARO,SAAS,UAAU,GAAG,UAAU,SAAS,SAAS,EAAC,6MAcnC,OANvB,SAAS,UAAU,GAAG,KAAK,kBAAiB,qKAiB1B,OAXK,SAAS,WAAW,EAAC,gRAYlB,OADR,SAAS,WAAW,CAAC,MAAM,CAAC,IAAG,gDAEvB,OADA,SAAS,WAAW,EAAC,gDAErB,OADA,SAAS,cAAc,EAAC,gDAExB,OADA,SAAS,YAAY,EAAC,gDAIvB,OAHC,SAAS,YAAY,EAAC,iEAOV,OAJb,SAAS,UAAU,kBAAkB,kBAAiB,2IAK9C,OADK,QAAQ,cAAc,EAAC,uDAExB,OADJ,IAAI,KAAK,QAAQ,UAAU,EAAE,kBAAkB,CAAC,UAAS,2DAEtD,OADC,QAAQ,cAAc,KAAK,SAAS,UAAU,OAAM,0DAI1E,OAHqB,QAAQ,cAAc,KAAK,SAAS,UAAU,QAAO,8FAIlF,OADQ,SAAS,UAAU,kBAAkB,iBAAgB,uCAElE,OADK,kBAAiB,wBAEtB,OADA,SAAS,aAAW,sBAAA,QAAQ,SAAS,cAAjB,0CAAA,oBAAmB,KAAK,IAAG,AAAC,gBAAuC,OAAxB,QAAQ,SAAS,CAAC,KAAK,EAAC,YAAU,IAAG,kBAepG,OAdA,SAAS,gBAAc,sBAAA,QAAQ,SAAS,cAAjB,0CAAA,oBAAmB,KAAK,IAAG,AAAC,gBAAuC,OAAxB,QAAQ,SAAS,CAAC,KAAK,EAAC,YAAU,IAAG,kTAcvG,CAAA,kBAAA,4BAAA,MAAO,GAAG,CAAC,CAAC;YAGsC,kCAAA,wBACA;eAJxB,AAAC,6CAMnB,OAJA,SAAS,UACV,KAAK,aAAa,IAAI,KAAK,YAAY,MAAI,yBAAA,KAAK,gBAAgB,cAArB,8CAAA,mCAAA,uBAAuB,SAAS,cAAhC,uDAAA,iCAAkC,IAAI,KAAI,aACrF,KAAK,aAAa,IAAI,KAAK,YAAY,MAAI,kBAAA,KAAK,SAAS,cAAd,sCAAA,gBAAgB,IAAI,KAAI,YACvE,+BAEK,OADA,KAAK,QAAQ,EAAC,+BAKd,OAJA,SAAS,UACX,CAAC,KAAK,UAAU,IAAI,CAAC,EAAE,cAAc,KACrC,CAAC,KAAK,SAAS,IAAI,KAAK,QAAQ,IAAI,CAAC,EAAE,cAAc,IACxD,mCAIA,OAHK,SAAS,UACX,CAAC,KAAK,WAAW,IAAI,CAAC,EAAE,cAAc,KACtC,CAAC,KAAK,UAAU,IAAI,KAAK,SAAS,IAAI,CAAC,EAAE,cAAc,IAC1D;OAEF,IAAI,CAAC,QAAO,IAAG,oJAWZ,QAJA,wBAAA,QAAQ,YAAY,cAApB,4CAAA,sBAAsB,cAAc,IAAG,oGAIvC,EAAA,2BAAA,QAAQ,eAAe,cAAvB,+CAAA,yBAAyB,cAAc,OAAM,GAAE,uGAQvD,QAJQ,wBAAA,QAAQ,YAAY,cAApB,4CAAA,sBAAsB,cAAc,IAAG,4DAYxC,OARP,QAAQ,KAAK,GAAG,AAAC,2FAGC,OAAd,QAAQ,KAAK,EAAC,kCAEhB,IAAG,qDAKiD,OAF/C,SAAS,UAAU,EAAC,sIAEkE,OAAvC,IAAI,OAAO,kBAAkB,CAAC,UAAS;AAOvG;AAEA,MAAM,qBAAqB,CAAC,YAAiB,YAAoB,OAAe;IAC9E,OAAO,AAAC,+LAWa,OALR,OAAM,+KAMF,OADI,SAAS,QAAQ,KAAK,UAAU,SAAS,SAAS,QAAQ,KAAK,UAAU,SAAS,QAAO,wBAElF,OADX,SAAS,SAAS,EAAC,mCAY1B,OAXkB,SAAS,eAAe,EAAC,mQAYlB,OADzB,SAAS,UAAU,GAAG,KAAK,kBAAiB,yCAOrC,OANkB,SAAS,WAAW,EAAC,0JAcvC,OARA,SAAS,WAAW,EAAC,uNAuBV,OAfX,SAAS,WAAW,EAAC,+ZAyBrB,OAVW,SAAS,UAAU,GAAG,YAAY,eAAc,sPAwBhD,OAdX,SAAS,WAAW,EAAC,mVAerB,OADW,SAAS,UAAU,GAAG,SAAS,WAAW,GAAG,WAAU,yBAUzE,OATO,SAAS,UAAU,GAAG,UAAU,SAAS,SAAS,EAAC,2OAsBhC,OAb1B,SAAS,UAAU,GAAG,KAAK,kBAAiB,2XAcvC,OADqB,SAAS,WAAW,EAAC,2BAIvB,OAHnB,SAAS,cAAc,EAAC,gEAKd,OAFS,OAAM,kHAKhC,OAHiB,IAAI,OAAO,kBAAkB,CAAC,UAAS,gCAkGjD,OA/FP,MAAM,OAAO,CAAC,eAAe,WAAW,MAAM,GAAG,IAAI,AAAC,mLAMlD,OAF6B,WAAW,MAAM,EAAC,4CAa3C,OAXJ,WAAW,QAAQ,CAAC,YAAY,WAAW,QAAQ,CAAC,eAAe,AAAC,0JAG6D,OAAlG,WAAW,MAAM,CAAC,CAAC,KAAa,OAAc,MAAM,CAAC,KAAK,YAAY,IAAI,CAAC,GAAG,GAAG,cAAc,IAAG,oDAE/H,IAAG,sGAaD,OAPA,WAAW,QAAQ,CAAC,WAAY,yNAM9B,IAAG,sBAQL,OAPA,WAAW,QAAQ,CAAC,eAAgB,yNAMlC,IAAG,sBAQL,OAPA,eAAe,cAAe,iNAM5B,IAAG,sBAWP,OAVE,eAAe,YAAa,oMAM1B,IAAG,oFAuCX,OAnCI,WAAW,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC;YAIpB,iBACA,oBAMA,iBACA,qBAKA,iBACA,kBASiD;eA3Bf,AAAC,6CASzC,OAPA,WAAW,QAAQ,CAAC,WAAW,AArlBnD,AAqlBoD,6BAE1B,OADA,KAAK,cAAc,EAAC,0CACpB,EAAA,kBAAA,KAAK,SAAS,cAAd,sCAAA,gBAAgB,IAAI,KAAI,KAAK,aAAa,IAAI,aAAY,mCAE1D,QADA,qBAAA,KAAK,YAAY,cAAjB,yCAAA,mBAAmB,cAAc,IAAG,uCAEpC,OADA,KAAK,cAAc,KAAK,SAAS,UAAU,QAAO,mCACI,OAAtD,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB,CAAC,UAAS,+BAC1D,IAAG,wBAQL,OAPA,WAAW,QAAQ,CAAC,eAAe,AA5lBvD,AA4lBwD,6BAE9B,OADA,KAAK,cAAc,EAAC,0CACpB,EAAA,kBAAA,KAAK,SAAS,cAAd,sCAAA,gBAAgB,IAAI,KAAI,YAAW,mCAEnC,QADA,sBAAA,KAAK,YAAY,cAAjB,0CAAA,oBAAmB,cAAc,IAAG,uCAEpC,OADA,KAAK,cAAc,KAAK,SAAS,UAAU,QAAO,mCACI,OAAtD,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB,CAAC,UAAS,+BAC1D,IAAG,wBAQL,OAPA,eAAe,cAAc,AAAC,6BAExB,OADA,EAAA,kBAAA,KAAK,SAAS,cAAd,sCAAA,gBAAgB,IAAI,KAAI,YAAW,mCAEnC,OADA,EAAA,mBAAA,KAAK,SAAS,cAAd,uCAAA,iBAAgB,QAAQ,KAAI,YAAW,mCAEvC,OADA,KAAK,QAAQ,EAAC,mCAEd,OADA,IAAI,KAAK,KAAK,WAAW,EAAE,kBAAkB,CAAC,UAAS,mCACX,OAA5C,KAAK,QAAQ,GAAG,KAAK,eAAe,SAAQ,+BAChD,IAAG,wBAOA,OANL,eAAe,YAAY,AA1mB/C,AA0mBgD,6BAEtB,OADA,KAAK,gBAAgB,KAAK,WAAW,SAAS,SAAQ,mCAEtD,OADA,KAAK,QAAQ,EAAC,mCAEd,OADA,KAAK,WAAW,EAAC,0CACjB,KAAK,gBAAgB,KAAK,WAAW,MAAM,KAC3C,QADiD,eAAA,KAAK,MAAM,cAAX,mCAAA,aAAa,cAAc,IAAG,uCACzB,OAAtD,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB,CAAC,UAAS,+BAC1D,IAAG;OAER,IAAI,CAAC,KAAI,4DAQT,OAJL,WAAW,MAAM,GAAG,MAAM,AAAC,2HAEqB,OAAlB,WAAW,MAAM,EAAC,0CAE9C,IAAG,gBACJ,2IAIH,qDAG2B,OAApB,SAAS,UAAU,EAAC;AASrC", "debugId": null}}, {"offset": {"line": 4966, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%287%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/app/reports/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport AppLayout from '@/components/AppLayout'\nimport {\n  BarChart3,\n  FileText,\n  Calendar,\n  Users,\n  Building2,\n  Package,\n  TrendingUp,\n  TrendingDown,\n  Search,\n  Filter,\n  Download,\n  Printer,\n  Eye,\n  DollarSign,\n  ShoppingCart,\n  Wallet,\n  AlertTriangle,\n  CheckCircle,\n  XCircle,\n  Clock,\n  RotateCcw,\n  PieChart,\n  LineChart,\n  Activity,\n  Target,\n  Layers,\n  Database,\n  RefreshCw,\n  Settings,\n  ChevronDown,\n  ChevronRight,\n  Plus,\n  Minus,\n  CalendarDays,\n  FileSpreadsheet,\n  X\n} from 'lucide-react'\nimport {\n  getSalesInvoices,\n  getPurchaseInvoices,\n  getCashTransactions,\n  getReturns,\n  getCustomers,\n  getSuppliers,\n  getMedicines\n} from '@/lib/database'\n\n// تعريف أنواع التقارير\ntype ReportType = \n  | 'sales_summary'\n  | 'purchases_summary'\n  | 'financial_summary'\n  | 'inventory_report'\n  | 'customer_statement'\n  | 'supplier_statement'\n  | 'profit_loss'\n  | 'cash_flow'\n  | 'top_products'\n  | 'customer_analysis'\n\n// تعريف فلاتر التقارير\ninterface ReportFilters {\n  dateRange: {\n    start: string\n    end: string\n    preset: 'today' | 'week' | 'month' | 'quarter' | 'year' | 'custom'\n  }\n  customer?: string\n  supplier?: string\n  product?: string\n  paymentStatus?: 'all' | 'paid' | 'partial' | 'pending'\n  paymentMethod?: 'all' | 'cash' | 'credit'\n  includeReturns?: boolean\n}\n\n// تعريف بيانات التقرير\ninterface ReportData {\n  title: string\n  summary: {\n    totalRecords: number\n    totalAmount: number\n    averageAmount: number\n    [key: string]: any\n  }\n  data: any[]\n  charts?: {\n    type: 'bar' | 'pie' | 'line'\n    data: any[]\n    labels: string[]\n  }[]\n}\n\n// قائمة التقارير المتاحة\nconst AVAILABLE_REPORTS = [\n  {\n    id: 'sales_summary' as ReportType,\n    title: 'تقرير المبيعات الشامل',\n    description: 'تقرير مفصل عن جميع عمليات المبيعات مع التحليلات والرسوم البيانية',\n    icon: ShoppingCart,\n    category: 'sales',\n    color: 'bg-blue-500'\n  },\n  {\n    id: 'purchases_summary' as ReportType,\n    title: 'تقرير المشتريات الشامل',\n    description: 'تقرير مفصل عن جميع عمليات المشتريات مع تحليل الموردين',\n    icon: Package,\n    category: 'purchases',\n    color: 'bg-green-500'\n  },\n  {\n    id: 'financial_summary' as ReportType,\n    title: 'التقرير المالي الشامل',\n    description: 'ملخص شامل للوضع المالي مع الأرباح والخسائر',\n    icon: DollarSign,\n    category: 'financial',\n    color: 'bg-purple-500'\n  },\n  {\n    id: 'inventory_report' as ReportType,\n    title: 'تقرير المخزون',\n    description: 'حالة المخزون والكميات المتاحة مع تحليل الحركة',\n    icon: Database,\n    category: 'inventory',\n    color: 'bg-orange-500'\n  },\n  {\n    id: 'customer_statement' as ReportType,\n    title: 'كشف حساب العملاء',\n    description: 'تفاصيل حسابات العملاء والمديونيات مع الجدول الزمني',\n    icon: Users,\n    category: 'customers',\n    color: 'bg-indigo-500'\n  },\n  {\n    id: 'supplier_statement' as ReportType,\n    title: 'كشف حساب الموردين',\n    description: 'تفاصيل حسابات الموردين والمستحقات مع تحليل الأداء',\n    icon: Building2,\n    category: 'suppliers',\n    color: 'bg-teal-500'\n  },\n  {\n    id: 'profit_loss' as ReportType,\n    title: 'تقرير الأرباح والخسائر',\n    description: 'تحليل مفصل للأرباح والخسائر مع المقارنات الزمنية',\n    icon: TrendingUp,\n    category: 'financial',\n    color: 'bg-emerald-500'\n  },\n  {\n    id: 'cash_flow' as ReportType,\n    title: 'تقرير التدفق النقدي',\n    description: 'حركة النقد الداخل والخارج مع التوقعات المستقبلية',\n    icon: Wallet,\n    category: 'financial',\n    color: 'bg-cyan-500'\n  },\n  {\n    id: 'top_products' as ReportType,\n    title: 'أفضل المنتجات مبيعاً',\n    description: 'تحليل أداء المنتجات والأدوية الأكثر ربحية',\n    icon: Target,\n    category: 'analytics',\n    color: 'bg-rose-500'\n  },\n  {\n    id: 'customer_analysis' as ReportType,\n    title: 'تحليل العملاء',\n    description: 'تحليل سلوك العملاء وأنماط الشراء مع التوصيات',\n    icon: Activity,\n    category: 'analytics',\n    color: 'bg-violet-500'\n  }\n]\n\n// الفترات الزمنية المحددة مسبقاً\nconst DATE_PRESETS = [\n  { id: 'today', label: 'اليوم', days: 0 },\n  { id: 'week', label: 'هذا الأسبوع', days: 7 },\n  { id: 'month', label: 'هذا الشهر', days: 30 },\n  { id: 'quarter', label: 'هذا الربع', days: 90 },\n  { id: 'year', label: 'هذا العام', days: 365 },\n  { id: 'custom', label: 'فترة مخصصة', days: -1 }\n]\n\nexport default function ProfessionalReportsPage() {\n  const [selectedReport, setSelectedReport] = useState<ReportType | null>(null)\n  const [reportData, setReportData] = useState<ReportData | null>(null)\n  const [loading, setLoading] = useState(false)\n  const [showFilters, setShowFilters] = useState(false)\n  const [showReportModal, setShowReportModal] = useState(false)\n  const [customers, setCustomers] = useState<any[]>([])\n  const [suppliers, setSuppliers] = useState<any[]>([])\n  const [medicines, setMedicines] = useState<any[]>([])\n\n  // الفلاتر الافتراضية\n  const [filters, setFilters] = useState<ReportFilters>({\n    dateRange: {\n      start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],\n      end: new Date().toISOString().split('T')[0],\n      preset: 'month'\n    },\n    paymentStatus: 'all',\n    paymentMethod: 'all',\n    includeReturns: true\n  })\n\n  // تحميل البيانات الأساسية\n  useEffect(() => {\n    loadBasicData()\n  }, [])\n\n  const loadBasicData = async () => {\n    try {\n      const [customersResult, suppliersResult, medicinesResult] = await Promise.all([\n        getCustomers(),\n        getSuppliers(),\n        getMedicines()\n      ])\n      \n      setCustomers(customersResult.data || [])\n      setSuppliers(suppliersResult.data || [])\n      setMedicines(medicinesResult.data || [])\n    } catch (error) {\n      console.error('Error loading basic data:', error)\n    }\n  }\n\n  // تحديث الفترة الزمنية حسب الاختيار المحدد مسبقاً\n  const updateDatePreset = (preset: string) => {\n    const today = new Date()\n    let startDate = new Date()\n    \n    switch (preset) {\n      case 'today':\n        startDate = new Date(today)\n        break\n      case 'week':\n        startDate = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)\n        break\n      case 'month':\n        startDate = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)\n        break\n      case 'quarter':\n        startDate = new Date(today.getTime() - 90 * 24 * 60 * 60 * 1000)\n        break\n      case 'year':\n        startDate = new Date(today.getTime() - 365 * 24 * 60 * 60 * 1000)\n        break\n      default:\n        return\n    }\n\n    setFilters(prev => ({\n      ...prev,\n      dateRange: {\n        ...prev.dateRange,\n        start: startDate.toISOString().split('T')[0],\n        end: today.toISOString().split('T')[0],\n        preset: preset as any\n      }\n    }))\n  }\n\n  // توليد التقرير\n  const generateReport = async (reportType: ReportType) => {\n    setLoading(true)\n    setSelectedReport(reportType)\n\n    try {\n      const data = await generateReportData(reportType)\n      setReportData(data)\n      setShowReportModal(true)\n    } catch (error) {\n      console.error('Error generating report:', error)\n      alert('حدث خطأ أثناء توليد التقرير')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  // توليد بيانات التقرير\n  const generateReportData = async (reportType: ReportType): Promise<ReportData> => {\n    switch (reportType) {\n      case 'sales_summary':\n        return await generateSalesReport()\n      case 'purchases_summary':\n        return await generatePurchasesReport()\n      case 'financial_summary':\n        return await generateFinancialReport()\n      case 'inventory_report':\n        return await generateInventoryReport()\n      case 'customer_statement':\n        return await generateCustomerStatement()\n      case 'supplier_statement':\n        return await generateSupplierStatement()\n      case 'profit_loss':\n        return await generateProfitLossReport()\n      case 'cash_flow':\n        return await generateCashFlowReport()\n      case 'top_products':\n        return await generateTopProductsReport()\n      case 'customer_analysis':\n        return await generateCustomerAnalysisReport()\n      default:\n        return {\n          title: AVAILABLE_REPORTS.find(r => r.id === reportType)?.title || 'تقرير',\n          summary: {\n            totalRecords: 0,\n            totalAmount: 0,\n            averageAmount: 0\n          },\n          data: []\n        }\n    }\n  }\n\n  // تقرير المبيعات\n  const generateSalesReport = async (): Promise<ReportData> => {\n    const result = await getSalesInvoices()\n    const sales = result.data || []\n\n    // فلترة البيانات حسب التاريخ والفلاتر\n    const filteredSales = sales.filter(sale => {\n      const saleDate = new Date(sale.created_at)\n      const startDate = new Date(filters.dateRange.start)\n      const endDate = new Date(filters.dateRange.end + 'T23:59:59')\n\n      if (saleDate < startDate || saleDate > endDate) return false\n      if (filters.paymentStatus && filters.paymentStatus !== 'all' && sale.payment_status !== filters.paymentStatus) return false\n      if (filters.paymentMethod && filters.paymentMethod !== 'all' && sale.payment_method !== filters.paymentMethod) return false\n      if (filters.customer && sale.customer_id !== filters.customer) return false\n\n      return true\n    })\n\n    const totalAmount = filteredSales.reduce((sum, sale) => sum + (sale.final_amount || 0), 0)\n    const averageAmount = filteredSales.length > 0 ? totalAmount / filteredSales.length : 0\n    const paidAmount = filteredSales.reduce((sum, sale) => sum + (sale.paid_amount || 0), 0)\n    const pendingAmount = totalAmount - paidAmount\n\n    // إضافة تفاصيل المواد لكل فاتورة\n    const salesWithItems = filteredSales.map(sale => {\n      const items = sale.sales_invoice_items || []\n      const itemsText = items.length > 0\n        ? items.map((item: any) => {\n            const medicineName = item.medicine_batches?.medicines?.name || item.medicine_name || 'غير محدد'\n            return `${medicineName} (${item.quantity})`\n          }).join(', ')\n        : 'لا توجد مواد'\n\n      return {\n        ...sale,\n        itemsText\n      }\n    })\n\n    return {\n      title: 'تقرير المبيعات الشامل',\n      summary: {\n        totalRecords: filteredSales.length,\n        totalAmount,\n        averageAmount,\n        paidAmount,\n        pendingAmount\n      },\n      data: salesWithItems.map(sale => ({\n        'رقم الفاتورة': sale.invoice_number,\n        'العميل': sale.customer_name || 'عميل نقدي',\n        'التاريخ': new Date(sale.created_at).toLocaleDateString('ar-EG'),\n        'المبلغ الإجمالي': (sale.final_amount || 0).toLocaleString() + ' د.ع',\n        'المبلغ المدفوع': (sale.paid_amount || 0).toLocaleString() + ' د.ع',\n        'المبلغ المتبقي': ((sale.final_amount || 0) - (sale.paid_amount || 0)).toLocaleString() + ' د.ع',\n        'حالة الدفع': sale.payment_status === 'paid' ? 'مدفوع' : sale.payment_status === 'partial' ? 'جزئي' : 'معلق',\n        'طريقة الدفع': sale.payment_method === 'cash' ? 'نقداً' : 'آجل',\n        'المواد': sale.itemsText\n      })),\n      charts: [\n        {\n          type: 'pie' as const,\n          data: [\n            { name: 'مدفوع', value: filteredSales.filter(s => s.payment_status === 'paid').length },\n            { name: 'جزئي', value: filteredSales.filter(s => s.payment_status === 'partial').length },\n            { name: 'معلق', value: filteredSales.filter(s => s.payment_status === 'pending').length }\n          ],\n          labels: ['مدفوع', 'جزئي', 'معلق']\n        }\n      ]\n    }\n  }\n\n  // تقرير المشتريات\n  const generatePurchasesReport = async (): Promise<ReportData> => {\n    const result = await getPurchaseInvoices()\n    const purchases = result.data || []\n\n    const filteredPurchases = purchases.filter(purchase => {\n      const purchaseDate = new Date(purchase.created_at)\n      const startDate = new Date(filters.dateRange.start)\n      const endDate = new Date(filters.dateRange.end + 'T23:59:59')\n\n      if (purchaseDate < startDate || purchaseDate > endDate) return false\n      if (filters.paymentStatus && filters.paymentStatus !== 'all' && purchase.payment_status !== filters.paymentStatus) return false\n      if (filters.supplier && purchase.supplier_id !== filters.supplier) return false\n\n      return true\n    })\n\n    const totalAmount = filteredPurchases.reduce((sum, purchase) => sum + (purchase.final_amount || 0), 0)\n    const averageAmount = filteredPurchases.length > 0 ? totalAmount / filteredPurchases.length : 0\n    const paidAmount = filteredPurchases.reduce((sum, purchase) => sum + (purchase.paid_amount || 0), 0)\n    const pendingAmount = totalAmount - paidAmount\n\n    // إضافة تفاصيل المواد لكل فاتورة\n    const purchasesWithItems = filteredPurchases.map(purchase => {\n      const items = purchase.purchase_invoice_items || []\n      const itemsText = items.length > 0\n        ? items.map((item: any) => {\n            const medicineName = item.medicines?.name || item.medicine_name || 'غير محدد'\n            return `${medicineName} (${item.quantity})`\n          }).join(', ')\n        : 'لا توجد مواد'\n\n      return {\n        ...purchase,\n        itemsText\n      }\n    })\n\n    return {\n      title: 'تقرير المشتريات الشامل',\n      summary: {\n        totalRecords: filteredPurchases.length,\n        totalAmount,\n        averageAmount,\n        paidAmount,\n        pendingAmount\n      },\n      data: purchasesWithItems.map(purchase => ({\n        'رقم الفاتورة': purchase.invoice_number,\n        'المورد': purchase.suppliers?.name || 'غير محدد',\n        'التاريخ': new Date(purchase.created_at).toLocaleDateString('ar-EG'),\n        'المبلغ الإجمالي': (purchase.final_amount || 0).toLocaleString() + ' د.ع',\n        'المبلغ المدفوع': (purchase.paid_amount || 0).toLocaleString() + ' د.ع',\n        'المبلغ المتبقي': ((purchase.final_amount || 0) - (purchase.paid_amount || 0)).toLocaleString() + ' د.ع',\n        'حالة الدفع': purchase.payment_status === 'paid' ? 'مدفوع' : purchase.payment_status === 'partial' ? 'جزئي' : 'معلق',\n        'المواد': purchase.itemsText\n      })),\n      charts: [\n        {\n          type: 'pie' as const,\n          data: [\n            { name: 'مدفوع', value: filteredPurchases.filter(p => p.payment_status === 'paid').length },\n            { name: 'جزئي', value: filteredPurchases.filter(p => p.payment_status === 'partial').length },\n            { name: 'معلق', value: filteredPurchases.filter(p => p.payment_status === 'pending').length }\n          ],\n          labels: ['مدفوع', 'جزئي', 'معلق']\n        }\n      ]\n    }\n  }\n\n  // التقرير المالي\n  const generateFinancialReport = async (): Promise<ReportData> => {\n    const [salesResult, purchasesResult, cashResult] = await Promise.all([\n      getSalesInvoices(),\n      getPurchaseInvoices(),\n      getCashTransactions()\n    ])\n\n    const sales = salesResult.data || []\n    const purchases = purchasesResult.data || []\n    const transactions = cashResult.data || []\n\n    // فلترة البيانات حسب التاريخ\n    const filteredSales = sales.filter(sale => {\n      const saleDate = new Date(sale.created_at)\n      const startDate = new Date(filters.dateRange.start)\n      const endDate = new Date(filters.dateRange.end + 'T23:59:59')\n      return saleDate >= startDate && saleDate <= endDate\n    })\n\n    const filteredPurchases = purchases.filter(purchase => {\n      const purchaseDate = new Date(purchase.created_at)\n      const startDate = new Date(filters.dateRange.start)\n      const endDate = new Date(filters.dateRange.end + 'T23:59:59')\n      return purchaseDate >= startDate && purchaseDate <= endDate\n    })\n\n    const filteredTransactions = transactions.filter(transaction => {\n      const transactionDate = new Date(transaction.created_at)\n      const startDate = new Date(filters.dateRange.start)\n      const endDate = new Date(filters.dateRange.end + 'T23:59:59')\n      return transactionDate >= startDate && transactionDate <= endDate\n    })\n\n    const totalRevenue = filteredSales.reduce((sum, sale) => sum + (sale.final_amount || 0), 0)\n    const totalExpenses = filteredPurchases.reduce((sum, purchase) => sum + (purchase.final_amount || 0), 0)\n    const netProfit = totalRevenue - totalExpenses\n    const profitMargin = totalRevenue > 0 ? (netProfit / totalRevenue) * 100 : 0\n\n    // حساب التدفق النقدي\n    const cashIncome = filteredTransactions\n      .filter(t => t.transaction_type === 'income')\n      .reduce((sum, t) => sum + t.amount, 0)\n    const cashExpenses = filteredTransactions\n      .filter(t => t.transaction_type === 'expense')\n      .reduce((sum, t) => sum + t.amount, 0)\n    const netCashFlow = cashIncome - cashExpenses\n\n    return {\n      title: 'التقرير المالي الشامل',\n      summary: {\n        totalRecords: filteredSales.length + filteredPurchases.length,\n        totalAmount: totalRevenue,\n        averageAmount: totalRevenue / (filteredSales.length || 1),\n        totalRevenue,\n        totalExpenses,\n        netProfit,\n        profitMargin,\n        cashIncome,\n        cashExpenses,\n        netCashFlow\n      },\n      data: [\n        { 'البند': 'إجمالي الإيرادات', 'المبلغ': totalRevenue.toLocaleString() + ' د.ع', 'النوع': 'إيرادات' },\n        { 'البند': 'إجمالي المصروفات', 'المبلغ': totalExpenses.toLocaleString() + ' د.ع', 'النوع': 'مصروفات' },\n        { 'البند': 'صافي الربح', 'المبلغ': netProfit.toLocaleString() + ' د.ع', 'النوع': 'ربح' },\n        { 'البند': 'هامش الربح', 'المبلغ': profitMargin.toFixed(2) + '%', 'النوع': 'نسبة' },\n        { 'البند': 'النقد الداخل', 'المبلغ': cashIncome.toLocaleString() + ' د.ع', 'النوع': 'تدفق نقدي' },\n        { 'البند': 'النقد الخارج', 'المبلغ': cashExpenses.toLocaleString() + ' د.ع', 'النوع': 'تدفق نقدي' },\n        { 'البند': 'صافي التدفق النقدي', 'المبلغ': netCashFlow.toLocaleString() + ' د.ع', 'النوع': 'تدفق نقدي' }\n      ],\n      charts: [\n        {\n          type: 'pie' as const,\n          data: [\n            { name: 'الإيرادات', value: totalRevenue },\n            { name: 'المصروفات', value: totalExpenses }\n          ],\n          labels: ['الإيرادات', 'المصروفات']\n        }\n      ]\n    }\n  }\n\n  // باقي دوال التقارير (مبسطة للآن)\n  const generateInventoryReport = async (): Promise<ReportData> => {\n    const result = await getMedicines()\n    const medicines = result.data || []\n\n    return {\n      title: 'تقرير المخزون',\n      summary: {\n        totalRecords: medicines.length,\n        totalAmount: 0,\n        averageAmount: 0\n      },\n      data: medicines.map(medicine => ({\n        'اسم الدواء': medicine.name,\n        'الشركة المصنعة': medicine.manufacturer || 'غير محدد',\n        'الكمية المتاحة': medicine.medicine_batches?.reduce((sum: number, batch: any) => sum + (batch.quantity || 0), 0) || 0,\n        'تاريخ الانتهاء': medicine.medicine_batches?.[0]?.expiry_date ? new Date(medicine.medicine_batches[0].expiry_date).toLocaleDateString('ar-EG') : 'غير محدد'\n      }))\n    }\n  }\n\n  const generateCustomerStatement = async (): Promise<ReportData> => {\n    const [salesResult, customersResult] = await Promise.all([\n      getSalesInvoices(),\n      getCustomers()\n    ])\n\n    const sales = salesResult.data || []\n    const customers = customersResult.data || []\n\n    // فلترة المبيعات حسب التاريخ\n    const filteredSales = sales.filter(sale => {\n      const saleDate = new Date(sale.created_at)\n      const startDate = new Date(filters.dateRange.start)\n      const endDate = new Date(filters.dateRange.end + 'T23:59:59')\n      return saleDate >= startDate && saleDate <= endDate\n    })\n\n    // إذا تم تحديد عميل معين، نعرض تفاصيل فواتيره\n    if (filters.customer) {\n      const selectedCustomer = customers.find(c => c.id === filters.customer)\n      const customerSales = filteredSales.filter(sale => sale.customer_id === filters.customer)\n\n      if (selectedCustomer && customerSales.length > 0) {\n        // عرض تفاصيل فواتير العميل مع المواد\n        const salesWithItems = customerSales.map(sale => {\n          const items = sale.sales_invoice_items || []\n          const itemsText = items.length > 0\n            ? items.map((item: any) => {\n                const medicineName = item.medicine_batches?.medicines?.name || item.medicine_name || 'غير محدد'\n                return `${medicineName} (${item.quantity} × ${(item.unit_price || 0).toLocaleString()} = ${(item.total_price || 0).toLocaleString()} د.ع)`\n              }).join(' | ')\n            : 'لا توجد مواد'\n\n          return {\n            'رقم الفاتورة': sale.invoice_number,\n            'التاريخ': new Date(sale.created_at).toLocaleDateString('ar-EG'),\n            'المبلغ الإجمالي': (sale.final_amount || 0).toLocaleString() + ' د.ع',\n            'المبلغ المدفوع': (sale.paid_amount || 0).toLocaleString() + ' د.ع',\n            'المبلغ المتبقي': ((sale.final_amount || 0) - (sale.paid_amount || 0)).toLocaleString() + ' د.ع',\n            'حالة الدفع': sale.payment_status === 'paid' ? 'مدفوع' : sale.payment_status === 'partial' ? 'جزئي' : 'معلق',\n            'المواد المشتراة': itemsText\n          }\n        })\n\n        const totalAmount = customerSales.reduce((sum, sale) => sum + (sale.final_amount || 0), 0)\n        const paidAmount = customerSales.reduce((sum, sale) => sum + (sale.paid_amount || 0), 0)\n\n        return {\n          title: `كشف حساب العميل: ${selectedCustomer.name}`,\n          summary: {\n            totalRecords: customerSales.length,\n            totalAmount,\n            averageAmount: customerSales.length > 0 ? totalAmount / customerSales.length : 0,\n            paidAmount,\n            pendingAmount: totalAmount - paidAmount\n          },\n          data: salesWithItems\n        }\n      }\n    }\n\n    // عرض ملخص جميع العملاء\n    const customersWithSales = customers.map(customer => {\n      const customerSales = filteredSales.filter(sale => sale.customer_id === customer.id)\n      const totalAmount = customerSales.reduce((sum, sale) => sum + (sale.final_amount || 0), 0)\n      const paidAmount = customerSales.reduce((sum, sale) => sum + (sale.paid_amount || 0), 0)\n\n      return {\n        customer,\n        totalAmount,\n        paidAmount,\n        salesCount: customerSales.length\n      }\n    }).filter(item => item.salesCount > 0) // فقط العملاء الذين لديهم مبيعات\n\n    return {\n      title: 'كشف حساب العملاء',\n      summary: {\n        totalRecords: customersWithSales.length,\n        totalAmount: customersWithSales.reduce((sum, item) => sum + item.totalAmount, 0),\n        averageAmount: customersWithSales.length > 0 ?\n          customersWithSales.reduce((sum, item) => sum + item.totalAmount, 0) / customersWithSales.length : 0\n      },\n      data: customersWithSales.map(item => ({\n        'اسم العميل': item.customer.name,\n        'الهاتف': item.customer.phone || 'غير محدد',\n        'العنوان': item.customer.address || 'غير محدد',\n        'عدد الفواتير': item.salesCount,\n        'إجمالي المبلغ': item.totalAmount.toLocaleString() + ' د.ع',\n        'المبلغ المدفوع': item.paidAmount.toLocaleString() + ' د.ع',\n        'المبلغ المتبقي': (item.totalAmount - item.paidAmount).toLocaleString() + ' د.ع'\n      }))\n    }\n  }\n\n  const generateSupplierStatement = async (): Promise<ReportData> => {\n    const [purchasesResult, suppliersResult] = await Promise.all([\n      getPurchaseInvoices(),\n      getSuppliers()\n    ])\n\n    const purchases = purchasesResult.data || []\n    const suppliers = suppliersResult.data || []\n\n    // فلترة المشتريات حسب التاريخ\n    const filteredPurchases = purchases.filter(purchase => {\n      const purchaseDate = new Date(purchase.created_at)\n      const startDate = new Date(filters.dateRange.start)\n      const endDate = new Date(filters.dateRange.end + 'T23:59:59')\n      return purchaseDate >= startDate && purchaseDate <= endDate\n    })\n\n    // إذا تم تحديد مورد معين، نعرض تفاصيل فواتيره\n    if (filters.supplier) {\n      const selectedSupplier = suppliers.find(s => s.id === filters.supplier)\n      const supplierPurchases = filteredPurchases.filter(purchase => purchase.supplier_id === filters.supplier)\n\n      if (selectedSupplier && supplierPurchases.length > 0) {\n        // عرض تفاصيل فواتير المورد مع المواد\n        const purchasesWithItems = supplierPurchases.map(purchase => {\n          const items = purchase.purchase_invoice_items || []\n          const itemsText = items.length > 0\n            ? items.map((item: any) => {\n                const medicineName = item.medicines?.name || item.medicine_name || 'غير محدد'\n                return `${medicineName} (${item.quantity} × ${(item.unit_cost || 0).toLocaleString()} = ${(item.total_cost || 0).toLocaleString()} د.ع)`\n              }).join(' | ')\n            : 'لا توجد مواد'\n\n          return {\n            'رقم الفاتورة': purchase.invoice_number,\n            'التاريخ': new Date(purchase.created_at).toLocaleDateString('ar-EG'),\n            'المبلغ الإجمالي': (purchase.final_amount || 0).toLocaleString() + ' د.ع',\n            'المبلغ المدفوع': (purchase.paid_amount || 0).toLocaleString() + ' د.ع',\n            'المبلغ المتبقي': ((purchase.final_amount || 0) - (purchase.paid_amount || 0)).toLocaleString() + ' د.ع',\n            'حالة الدفع': purchase.payment_status === 'paid' ? 'مدفوع' : purchase.payment_status === 'partial' ? 'جزئي' : 'معلق',\n            'المواد المشتراة': itemsText\n          }\n        })\n\n        const totalAmount = supplierPurchases.reduce((sum, purchase) => sum + (purchase.final_amount || 0), 0)\n        const paidAmount = supplierPurchases.reduce((sum, purchase) => sum + (purchase.paid_amount || 0), 0)\n\n        return {\n          title: `كشف حساب المورد: ${selectedSupplier.name}`,\n          summary: {\n            totalRecords: supplierPurchases.length,\n            totalAmount,\n            averageAmount: supplierPurchases.length > 0 ? totalAmount / supplierPurchases.length : 0,\n            paidAmount,\n            pendingAmount: totalAmount - paidAmount\n          },\n          data: purchasesWithItems\n        }\n      }\n    }\n\n    // عرض ملخص جميع الموردين\n    const suppliersWithPurchases = suppliers.map(supplier => {\n      const supplierPurchases = filteredPurchases.filter(purchase => purchase.supplier_id === supplier.id)\n      const totalAmount = supplierPurchases.reduce((sum, purchase) => sum + (purchase.final_amount || 0), 0)\n      const paidAmount = supplierPurchases.reduce((sum, purchase) => sum + (purchase.paid_amount || 0), 0)\n\n      return {\n        supplier,\n        totalAmount,\n        paidAmount,\n        purchasesCount: supplierPurchases.length\n      }\n    }).filter(item => item.purchasesCount > 0) // فقط الموردين الذين لديهم مشتريات\n\n    return {\n      title: 'كشف حساب الموردين',\n      summary: {\n        totalRecords: suppliersWithPurchases.length,\n        totalAmount: suppliersWithPurchases.reduce((sum, item) => sum + item.totalAmount, 0),\n        averageAmount: suppliersWithPurchases.length > 0 ?\n          suppliersWithPurchases.reduce((sum, item) => sum + item.totalAmount, 0) / suppliersWithPurchases.length : 0\n      },\n      data: suppliersWithPurchases.map(item => ({\n        'اسم المورد': item.supplier.name,\n        'الهاتف': item.supplier.phone || 'غير محدد',\n        'العنوان': item.supplier.address || 'غير محدد',\n        'الشخص المسؤول': item.supplier.contact_person || 'غير محدد',\n        'عدد الفواتير': item.purchasesCount,\n        'إجمالي المبلغ': item.totalAmount.toLocaleString() + ' د.ع',\n        'المبلغ المدفوع': item.paidAmount.toLocaleString() + ' د.ع',\n        'المبلغ المتبقي': (item.totalAmount - item.paidAmount).toLocaleString() + ' د.ع'\n      }))\n    }\n  }\n\n  const generateProfitLossReport = async (): Promise<ReportData> => {\n    const [salesResult, purchasesResult] = await Promise.all([\n      getSalesInvoices(),\n      getPurchaseInvoices()\n    ])\n\n    const sales = salesResult.data || []\n    const purchases = purchasesResult.data || []\n\n    const revenue = sales.reduce((sum, sale) => sum + (sale.final_amount || 0), 0)\n    const costs = purchases.reduce((sum, purchase) => sum + (purchase.final_amount || 0), 0)\n    const grossProfit = revenue - costs\n\n    return {\n      title: 'تقرير الأرباح والخسائر',\n      summary: {\n        totalRecords: sales.length + purchases.length,\n        totalAmount: revenue,\n        averageAmount: grossProfit\n      },\n      data: [\n        { 'البند': 'الإيرادات', 'المبلغ': revenue.toLocaleString() + ' د.ع' },\n        { 'البند': 'تكلفة البضاعة المباعة', 'المبلغ': costs.toLocaleString() + ' د.ع' },\n        { 'البند': 'إجمالي الربح', 'المبلغ': grossProfit.toLocaleString() + ' د.ع' },\n        { 'البند': 'هامش الربح الإجمالي', 'المبلغ': revenue > 0 ? ((grossProfit / revenue) * 100).toFixed(2) + '%' : '0%' }\n      ]\n    }\n  }\n\n  const generateCashFlowReport = async (): Promise<ReportData> => {\n    const result = await getCashTransactions()\n    const transactions = result.data || []\n\n    // فلترة المعاملات حسب التاريخ\n    const filteredTransactions = transactions.filter(transaction => {\n      const transactionDate = new Date(transaction.created_at)\n      const startDate = new Date(filters.dateRange.start)\n      const endDate = new Date(filters.dateRange.end + 'T23:59:59')\n      return transactionDate >= startDate && transactionDate <= endDate\n    })\n\n    const income = filteredTransactions.filter(t => t.transaction_type === 'income').reduce((sum, t) => sum + t.amount, 0)\n    const expenses = filteredTransactions.filter(t => t.transaction_type === 'expense').reduce((sum, t) => sum + t.amount, 0)\n    const netFlow = income - expenses\n\n    // تفاصيل المعاملات\n    const transactionDetails = filteredTransactions.map(transaction => ({\n      'التاريخ': new Date(transaction.created_at).toLocaleDateString('ar-EG'),\n      'النوع': transaction.transaction_type === 'income' ? 'داخل' : 'خارج',\n      'المبلغ': transaction.amount.toLocaleString() + ' د.ع',\n      'الوصف': transaction.description || 'غير محدد',\n      'المرجع': transaction.reference_type || 'غير محدد'\n    }))\n\n    return {\n      title: 'تقرير التدفق النقدي',\n      summary: {\n        totalRecords: filteredTransactions.length,\n        totalAmount: income,\n        averageAmount: netFlow,\n        income,\n        expenses,\n        netFlow\n      },\n      data: [\n        { 'البند': 'إجمالي الداخل', 'المبلغ': income.toLocaleString() + ' د.ع', 'النوع': 'إيجابي' },\n        { 'البند': 'إجمالي الخارج', 'المبلغ': expenses.toLocaleString() + ' د.ع', 'النوع': 'سلبي' },\n        { 'البند': 'صافي التدفق النقدي', 'المبلغ': netFlow.toLocaleString() + ' د.ع', 'النوع': netFlow >= 0 ? 'إيجابي' : 'سلبي' },\n        ...transactionDetails\n      ],\n      charts: [\n        {\n          type: 'pie' as const,\n          data: [\n            { name: 'الداخل', value: income },\n            { name: 'الخارج', value: expenses }\n          ],\n          labels: ['الداخل', 'الخارج']\n        }\n      ]\n    }\n  }\n\n  const generateTopProductsReport = async (): Promise<ReportData> => {\n    const result = await getMedicines()\n    const medicines = result.data || []\n\n    return {\n      title: 'أفضل المنتجات مبيعاً',\n      summary: {\n        totalRecords: medicines.length,\n        totalAmount: 0,\n        averageAmount: 0\n      },\n      data: medicines.slice(0, 10).map((medicine, index) => ({\n        'الترتيب': index + 1,\n        'اسم الدواء': medicine.name,\n        'الشركة المصنعة': medicine.manufacturer || 'غير محدد',\n        'الكمية المباعة': Math.floor(Math.random() * 100) + 1, // بيانات تجريبية\n        'إجمالي المبيعات': (Math.floor(Math.random() * 1000000) + 100000).toLocaleString() + ' د.ع'\n      }))\n    }\n  }\n\n  const generateCustomerAnalysisReport = async (): Promise<ReportData> => {\n    const [salesResult, customersResult] = await Promise.all([\n      getSalesInvoices(),\n      getCustomers()\n    ])\n\n    const sales = salesResult.data || []\n    const customers = customersResult.data || []\n\n    return {\n      title: 'تحليل العملاء',\n      summary: {\n        totalRecords: customers.length,\n        totalAmount: sales.reduce((sum, sale) => sum + (sale.final_amount || 0), 0),\n        averageAmount: 0\n      },\n      data: customers.slice(0, 10).map(customer => {\n        const customerSales = sales.filter(sale => sale.customer_id === customer.id)\n        return {\n          'اسم العميل': customer.name,\n          'عدد الزيارات': customerSales.length,\n          'متوسط قيمة الطلب': customerSales.length > 0 ?\n            (customerSales.reduce((sum, sale) => sum + (sale.final_amount || 0), 0) / customerSales.length).toLocaleString() + ' د.ع' :\n            '0 د.ع',\n          'آخر زيارة': customerSales.length > 0 ?\n            new Date(Math.max(...customerSales.map(sale => new Date(sale.created_at).getTime()))).toLocaleDateString('ar-EG') :\n            'لا توجد زيارات'\n        }\n      })\n    }\n  }\n\n  // دوال الطباعة والتصدير - استخدام قالب لارين\n  const handlePrintReport = () => {\n    if (!reportData) return\n\n    // استخدام قالب لارين للطباعة\n    const { printReport } = require('@/hooks/usePrintSettings')\n    const { usePrintSettings } = require('@/hooks/usePrintSettings')\n\n    // إعدادات الطباعة الافتراضية\n    const defaultSettings = {\n      companyName: 'مكتب لارين العلمي',\n      companyNameEn: 'LAREN SCIENTIFIC BUREAU',\n      companyAddress: 'بغداد - شارع فلسطين',\n      companyPhone: '+964 ************',\n      companyEmail: '<EMAIL>',\n      showLogo: true,\n      showHeader: true,\n      showFooter: true,\n      footerText: 'شكراً لتعاملكم معنا',\n      fontSize: 'medium',\n      paperSize: 'A4',\n      showBorders: true,\n      showColors: false,\n      includeBarcode: false,\n      includeQRCode: false,\n      showWatermark: false,\n      headerColor: '#1f2937',\n      accentColor: '#3b82f6',\n      textColor: '#374151',\n      backgroundColor: '#ffffff'\n    }\n\n    // طباعة التقرير باستخدام قالب لارين\n    printReport(reportData.data, selectedReport || 'general', reportData.title, defaultSettings)\n\n  }\n\n  const handleExportExcel = async () => {\n    if (!reportData) return\n\n    try {\n      // استيراد مكتبة xlsx ديناميكياً\n      const XLSX = await import('xlsx')\n\n      // إنشاء workbook جديد\n      const workbook = XLSX.utils.book_new()\n\n      // إنشاء worksheet للملخص مع تنسيق محسن\n      const summaryData = [\n        ['نظام إدارة الصيدلية الاحترافي'],\n        [''],\n        ['اسم التقرير:', reportData.title],\n        ['تاريخ الإنشاء:', new Date().toLocaleDateString('ar-EG')],\n        ['وقت الإنشاء:', new Date().toLocaleTimeString('ar-EG')],\n        ['الفترة الزمنية:', `من ${filters.dateRange.start} إلى ${filters.dateRange.end}`],\n        [''],\n        ['الملخص التنفيذي'],\n        ['المؤشر', 'القيمة', 'الوحدة'],\n        ['إجمالي السجلات', reportData.summary.totalRecords, 'سجل'],\n        ['إجمالي المبلغ', Math.round(reportData.summary.totalAmount), 'دينار عراقي'],\n        ['متوسط المبلغ', Math.round(reportData.summary.averageAmount), 'دينار عراقي']\n      ]\n\n      // إضافة بيانات إضافية من الملخص\n      if (reportData.summary.paidAmount !== undefined) {\n        summaryData.push(['المبلغ المدفوع', Math.round(reportData.summary.paidAmount), 'دينار عراقي'])\n      }\n      if (reportData.summary.pendingAmount !== undefined) {\n        summaryData.push(['المبلغ المعلق', Math.round(reportData.summary.pendingAmount), 'دينار عراقي'])\n      }\n      if (reportData.summary.totalRevenue !== undefined) {\n        summaryData.push(['إجمالي الإيرادات', Math.round(reportData.summary.totalRevenue), 'دينار عراقي'])\n      }\n      if (reportData.summary.totalExpenses !== undefined) {\n        summaryData.push(['إجمالي المصروفات', Math.round(reportData.summary.totalExpenses), 'دينار عراقي'])\n      }\n      if (reportData.summary.netProfit !== undefined) {\n        summaryData.push(['صافي الربح', Math.round(reportData.summary.netProfit), 'دينار عراقي'])\n      }\n      if (reportData.summary.profitMargin !== undefined) {\n        summaryData.push(['هامش الربح', Math.round(reportData.summary.profitMargin * 100) / 100, '%'])\n      }\n\n      // إضافة معلومات الفلاتر المطبقة\n      summaryData.push([''])\n      summaryData.push(['الفلاتر المطبقة'])\n      summaryData.push(['الفلتر', 'القيمة'])\n      summaryData.push(['حالة الدفع', filters.paymentStatus === 'all' ? 'جميع الحالات' :\n        filters.paymentStatus === 'paid' ? 'مدفوع' :\n        filters.paymentStatus === 'partial' ? 'جزئي' : 'معلق'])\n      summaryData.push(['طريقة الدفع', filters.paymentMethod === 'all' ? 'جميع الطرق' :\n        filters.paymentMethod === 'cash' ? 'نقداً' : 'آجل'])\n      if (filters.customer) {\n        const customer = customers.find(c => c.id === filters.customer)\n        summaryData.push(['العميل المحدد', customer?.name || 'غير معروف'])\n      }\n      if (filters.supplier) {\n        const supplier = suppliers.find(s => s.id === filters.supplier)\n        summaryData.push(['المورد المحدد', supplier?.name || 'غير معروف'])\n      }\n\n      const summaryWorksheet = XLSX.utils.aoa_to_sheet(summaryData)\n\n      // تنسيق worksheet الملخص\n      const summaryRange = XLSX.utils.decode_range(summaryWorksheet['!ref'] || 'A1')\n\n      // تعيين عرض الأعمدة\n      summaryWorksheet['!cols'] = [\n        { width: 25 }, // العمود الأول\n        { width: 20 }, // العمود الثاني\n        { width: 15 }  // العمود الثالث\n      ]\n\n      XLSX.utils.book_append_sheet(workbook, summaryWorksheet, 'الملخص التنفيذي')\n\n      // إنشاء worksheet للبيانات التفصيلية\n      if (reportData.data.length > 0) {\n        // تحضير البيانات مع ترجمة العناوين\n        const translatedData = reportData.data.map(row => {\n          const translatedRow: any = {}\n          Object.entries(row).forEach(([key, value]) => {\n            const translatedKey = translateColumnName(key)\n            translatedRow[translatedKey] = value\n          })\n          return translatedRow\n        })\n\n        const detailsWorksheet = XLSX.utils.json_to_sheet(translatedData)\n\n        // تعيين عرض الأعمدة للبيانات التفصيلية\n        const headers = Object.keys(translatedData[0] || {})\n        detailsWorksheet['!cols'] = headers.map(header => {\n          if (header === 'المواد' || header === 'المواد المشتراة') {\n            return { width: 50 } // عرض أكبر لعمود المواد\n          }\n          return { width: 15 }\n        })\n\n        XLSX.utils.book_append_sheet(workbook, detailsWorksheet, 'البيانات التفصيلية')\n      }\n\n      // إضافة worksheet للإحصائيات إذا كانت متوفرة\n      if (reportData.charts && reportData.charts.length > 0) {\n        const statsData = [\n          ['الإحصائيات والرسوم البيانية'],\n          ['']\n        ]\n\n        reportData.charts.forEach((chart, index) => {\n          statsData.push([`الرسم البياني ${index + 1}`, chart.type === 'pie' ? 'دائري' : chart.type === 'bar' ? 'عمودي' : 'خطي'])\n          statsData.push(['التصنيف', 'القيمة'])\n\n          chart.data.forEach((item: any) => {\n            statsData.push([item.name || 'غير محدد', item.value || 0])\n          })\n\n          statsData.push(['']) // سطر فارغ بين الرسوم البيانية\n        })\n\n        const statsWorksheet = XLSX.utils.aoa_to_sheet(statsData)\n        statsWorksheet['!cols'] = [{ width: 20 }, { width: 15 }]\n        XLSX.utils.book_append_sheet(workbook, statsWorksheet, 'الإحصائيات')\n      }\n\n      // تصدير الملف\n      const fileName = `${reportData.title}_${new Date().toISOString().split('T')[0]}.xlsx`\n      XLSX.writeFile(workbook, fileName)\n\n    } catch (error) {\n      console.error('Error exporting Excel:', error)\n      // fallback إلى CSV\n      handleExportCSV()\n    }\n  }\n\n  const handleExportCSV = () => {\n    if (!reportData) return\n\n    // إنشاء محتوى CSV\n    const csvContent = [\n      // العنوان\n      [reportData.title],\n      [`تاريخ التقرير: ${new Date().toLocaleDateString('ar-EG')}`],\n      [`الفترة: ${filters.dateRange.start} إلى ${filters.dateRange.end}`],\n      [], // سطر فارغ\n\n      // الملخص\n      ['الملخص'],\n      ['إجمالي السجلات', reportData.summary.totalRecords.toLocaleString()],\n      ['إجمالي المبلغ', reportData.summary.totalAmount.toLocaleString() + ' د.ع'],\n      ['متوسط المبلغ', reportData.summary.averageAmount.toLocaleString() + ' د.ع'],\n      [], // سطر فارغ\n\n      // البيانات\n      ...(reportData.data.length > 0 ? [\n        Object.keys(reportData.data[0]), // العناوين\n        ...reportData.data.map(row => Object.values(row))\n      ] : [])\n    ]\n\n    const csvString = csvContent.map(row =>\n      Array.isArray(row) ? row.join(',') : row\n    ).join('\\n')\n\n    // إنشاء ملف وتحميله\n    const blob = new Blob(['\\ufeff' + csvString], { type: 'text/csv;charset=utf-8;' })\n    const link = document.createElement('a')\n    const url = URL.createObjectURL(blob)\n    link.setAttribute('href', url)\n    link.setAttribute('download', `${reportData.title}_${new Date().toISOString().split('T')[0]}.csv`)\n    link.style.visibility = 'hidden'\n    document.body.appendChild(link)\n    link.click()\n    document.body.removeChild(link)\n  }\n\n  const handleExportPDF = async () => {\n    if (!reportData) return\n\n    try {\n      // استيراد مكتبة jsPDF ديناميكياً\n      const { jsPDF } = await import('jspdf')\n      require('jspdf-autotable')\n\n      // إنشاء مستند PDF جديد\n      const doc = new jsPDF({\n        orientation: 'portrait',\n        unit: 'mm',\n        format: 'a4'\n      })\n\n      // إعداد الخط\n      doc.setFont('helvetica')\n\n      // إضافة خلفية ملونة للعنوان\n      doc.setFillColor(102, 126, 234) // لون أزرق جميل\n      doc.rect(0, 0, 210, 35, 'F')\n\n      // العنوان الرئيسي\n      doc.setTextColor(255, 255, 255)\n      doc.setFontSize(18)\n      doc.text(reportData.title, 105, 15, { align: 'center' })\n\n      // العنوان الفرعي\n      doc.setFontSize(12)\n      doc.text('نظام إدارة الصيدلية الاحترافي', 105, 25, { align: 'center' })\n\n      // إعادة تعيين لون النص\n      doc.setTextColor(0, 0, 0)\n\n      // معلومات التقرير في صندوق\n      doc.setFillColor(248, 250, 252)\n      doc.rect(15, 40, 180, 25, 'F')\n      doc.setDrawColor(226, 232, 240)\n      doc.rect(15, 40, 180, 25, 'S')\n\n      doc.setFontSize(10)\n      doc.text(`تاريخ التقرير: ${new Date().toLocaleDateString('ar-EG')}`, 20, 50)\n      doc.text(`الفترة: ${filters.dateRange.start} إلى ${filters.dateRange.end}`, 20, 58)\n\n      // الملخص في بطاقات ملونة\n      let yPos = 75\n      doc.setFontSize(14)\n      doc.setTextColor(45, 55, 72)\n      doc.text('ملخص التقرير:', 20, yPos)\n      yPos += 10\n\n      // بطاقات الملخص\n      const summaryItems = [\n        { label: 'إجمالي السجلات', value: reportData.summary.totalRecords.toLocaleString(), color: [59, 130, 246] },\n        { label: 'إجمالي المبلغ', value: Math.round(reportData.summary.totalAmount).toLocaleString() + ' د.ع', color: [16, 185, 129] },\n        { label: 'متوسط المبلغ', value: Math.round(reportData.summary.averageAmount).toLocaleString() + ' د.ع', color: [139, 92, 246] }\n      ]\n\n      // إضافة عناصر إضافية للملخص\n      if (reportData.summary.paidAmount !== undefined) {\n        summaryItems.push({\n          label: 'المبلغ المدفوع',\n          value: Math.round(reportData.summary.paidAmount).toLocaleString() + ' د.ع',\n          color: [5, 150, 105]\n        })\n      }\n      if (reportData.summary.pendingAmount !== undefined) {\n        summaryItems.push({\n          label: 'المبلغ المعلق',\n          value: Math.round(reportData.summary.pendingAmount).toLocaleString() + ' د.ع',\n          color: [220, 38, 38]\n        })\n      }\n\n      // رسم بطاقات الملخص\n      const cardWidth = 85\n      const cardHeight = 20\n      let xPos = 20\n\n      summaryItems.forEach((item, index) => {\n        if (index > 0 && index % 2 === 0) {\n          yPos += 25\n          xPos = 20\n        }\n\n        // خلفية البطاقة\n        doc.setFillColor(item.color[0], item.color[1], item.color[2])\n        doc.rect(xPos, yPos, cardWidth, cardHeight, 'F')\n\n        // نص البطاقة\n        doc.setTextColor(255, 255, 255)\n        doc.setFontSize(9)\n        doc.text(item.label, xPos + 5, yPos + 8)\n        doc.setFontSize(12)\n        doc.text(item.value, xPos + 5, yPos + 16)\n\n        xPos += cardWidth + 10\n      })\n\n      yPos += 35\n\n      // الجدول التفصيلي\n      if (reportData.data.length > 0) {\n        doc.setTextColor(0, 0, 0)\n        doc.setFontSize(12)\n        doc.text('تفاصيل التقرير:', 20, yPos)\n        yPos += 10\n\n        // تحضير بيانات الجدول\n        const tableHeaders = Object.keys(reportData.data[0]).map(key => translateColumnName(key))\n        const tableData = reportData.data.map(row =>\n          Object.entries(row).map(([key, value]) => {\n            if (key === 'المواد' || key === 'المواد المشتراة') {\n              // تقصير نص المواد للـ PDF\n              const text = String(value || 'لا توجد مواد')\n              return text.length > 50 ? text.substring(0, 50) + '...' : text\n            }\n            return String(value || '-')\n          })\n        )\n\n        // استخدام autotable لإنشاء الجدول\n        ;(doc as any).autoTable({\n          head: [tableHeaders],\n          body: tableData,\n          startY: yPos,\n          styles: {\n            fontSize: 7,\n            cellPadding: 3,\n            textColor: [45, 55, 72],\n            fillColor: [255, 255, 255],\n            lineColor: [226, 232, 240],\n            lineWidth: 0.1\n          },\n          headStyles: {\n            fillColor: [102, 126, 234],\n            textColor: [255, 255, 255],\n            fontStyle: 'bold',\n            fontSize: 8\n          },\n          alternateRowStyles: {\n            fillColor: [248, 250, 252]\n          },\n          margin: { top: 10, right: 15, bottom: 20, left: 15 },\n          tableWidth: 'auto',\n          columnStyles: {\n            // تخصيص عرض أعمدة المواد\n            [tableHeaders.indexOf('المواد')]: { cellWidth: 40 },\n            [tableHeaders.indexOf('المواد المشتراة')]: { cellWidth: 40 }\n          },\n          didDrawPage: function (data: any) {\n            // إضافة تذييل في كل صفحة\n            const pageHeight = doc.internal.pageSize.height\n            doc.setFontSize(8)\n            doc.setTextColor(128, 128, 128)\n            doc.text(\n              `تم إنشاء هذا التقرير في: ${new Date().toLocaleString('ar-EG')}`,\n              105,\n              pageHeight - 10,\n              { align: 'center' }\n            )\n            doc.text(\n              `صفحة ${data.pageNumber}`,\n              195,\n              pageHeight - 10,\n              { align: 'right' }\n            )\n          }\n        })\n      }\n\n      // حفظ الملف\n      const fileName = `${reportData.title}_${new Date().toISOString().split('T')[0]}.pdf`\n      doc.save(fileName)\n\n    } catch (error) {\n      console.error('Error exporting PDF:', error)\n      // fallback إلى الطباعة\n      alert('حدث خطأ في تصدير PDF. سيتم فتح نافذة الطباعة بدلاً من ذلك.')\n      handlePrintReport()\n    }\n  }\n\n  // دوال مساعدة\n  const translateColumnName = (columnName: string): string => {\n    const translations: { [key: string]: string } = {\n      'رقم الفاتورة': 'رقم الفاتورة',\n      'العميل': 'العميل',\n      'المورد': 'المورد',\n      'التاريخ': 'التاريخ',\n      'المبلغ الإجمالي': 'المبلغ الإجمالي',\n      'المبلغ المدفوع': 'المبلغ المدفوع',\n      'المبلغ المتبقي': 'المبلغ المتبقي',\n      'حالة الدفع': 'حالة الدفع',\n      'طريقة الدفع': 'طريقة الدفع',\n      'المواد': 'المواد',\n      'المواد المشتراة': 'المواد المشتراة',\n      'اسم العميل': 'اسم العميل',\n      'اسم المورد': 'اسم المورد',\n      'الهاتف': 'الهاتف',\n      'العنوان': 'العنوان',\n      'الشخص المسؤول': 'الشخص المسؤول',\n      'عدد الفواتير': 'عدد الفواتير',\n      'البند': 'البند',\n      'المبلغ': 'المبلغ',\n      'النوع': 'النوع',\n      'الوصف': 'الوصف',\n      'المرجع': 'المرجع',\n      'اسم الدواء': 'اسم الدواء',\n      'الشركة المصنعة': 'الشركة المصنعة',\n      'الكمية المتاحة': 'الكمية المتاحة',\n      'تاريخ الانتهاء': 'تاريخ الانتهاء',\n      'الترتيب': 'الترتيب',\n      'الكمية المباعة': 'الكمية المباعة',\n      'إجمالي المبيعات': 'إجمالي المبيعات',\n      'عدد الزيارات': 'عدد الزيارات',\n      'متوسط قيمة الطلب': 'متوسط قيمة الطلب',\n      'آخر زيارة': 'آخر زيارة',\n      // English translations for fallback\n      'invoice_number': 'رقم الفاتورة',\n      'customer_name': 'اسم العميل',\n      'supplier_name': 'اسم المورد',\n      'created_at': 'التاريخ',\n      'final_amount': 'المبلغ الإجمالي',\n      'paid_amount': 'المبلغ المدفوع',\n      'payment_status': 'حالة الدفع',\n      'payment_method': 'طريقة الدفع',\n      'name': 'الاسم',\n      'phone': 'الهاتف',\n      'address': 'العنوان',\n      'quantity': 'الكمية',\n      'unit_price': 'سعر الوحدة',\n      'total_price': 'المجموع'\n    }\n    return translations[columnName] || columnName\n  }\n\n  const formatCellValue = (value: any): string => {\n    if (value === null || value === undefined) return '-'\n    if (typeof value === 'number') return value.toLocaleString()\n    if (typeof value === 'string' && value.includes('T')) {\n      // تاريخ\n      try {\n        return new Date(value).toLocaleDateString('ar-EG')\n      } catch {\n        return value\n      }\n    }\n    return String(value)\n  }\n\n  return (\n    <AppLayout>\n      <div className=\"space-y-6\">\n        {/* Header */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h1 className=\"text-2xl font-bold text-gray-900 flex items-center gap-3\">\n                <BarChart3 className=\"h-8 w-8 text-blue-600\" />\n                نظام التقارير الاحترافي\n              </h1>\n              <p className=\"text-gray-600 mt-1\">تقارير شاملة مع إمكانيات طباعة وتصدير متقدمة</p>\n            </div>\n            <div className=\"flex items-center gap-3\">\n              <button\n                onClick={() => setShowFilters(!showFilters)}\n                className={`flex items-center gap-2 px-4 py-2 rounded-lg border transition-colors ${\n                  showFilters \n                    ? 'bg-blue-50 border-blue-200 text-blue-700' \n                    : 'bg-gray-50 border-gray-200 text-gray-700 hover:bg-gray-100'\n                }`}\n              >\n                <Filter className=\"h-4 w-4\" />\n                الفلاتر المتقدمة\n              </button>\n              <button\n                onClick={() => window.location.reload()}\n                className=\"flex items-center gap-2 px-4 py-2 bg-gray-50 border border-gray-200 rounded-lg text-gray-700 hover:bg-gray-100 transition-colors\"\n              >\n                <RefreshCw className=\"h-4 w-4\" />\n                تحديث\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Filters Panel */}\n        {showFilters && (\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <h3 className=\"text-lg font-semibold text-gray-900 flex items-center gap-2\">\n                <Settings className=\"h-5 w-5\" />\n                الفلاتر المتقدمة\n              </h3>\n              <button\n                onClick={() => setShowFilters(false)}\n                className=\"text-gray-400 hover:text-gray-600\"\n              >\n                <X className=\"h-5 w-5\" />\n              </button>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n              {/* Date Range */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">الفترة الزمنية</label>\n                <select\n                  value={filters.dateRange.preset}\n                  onChange={(e) => updateDatePreset(e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                >\n                  {DATE_PRESETS.map(preset => (\n                    <option key={preset.id} value={preset.id}>{preset.label}</option>\n                  ))}\n                </select>\n              </div>\n\n              {/* Payment Status */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">حالة الدفع</label>\n                <select\n                  value={filters.paymentStatus || 'all'}\n                  onChange={(e) => setFilters(prev => ({ ...prev, paymentStatus: e.target.value as any }))}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                >\n                  <option value=\"all\">جميع الحالات</option>\n                  <option value=\"paid\">مدفوع</option>\n                  <option value=\"partial\">جزئي</option>\n                  <option value=\"pending\">معلق</option>\n                </select>\n              </div>\n\n              {/* Payment Method */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">طريقة الدفع</label>\n                <select\n                  value={filters.paymentMethod || 'all'}\n                  onChange={(e) => setFilters(prev => ({ ...prev, paymentMethod: e.target.value as any }))}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                >\n                  <option value=\"all\">جميع الطرق</option>\n                  <option value=\"cash\">نقداً</option>\n                  <option value=\"credit\">آجل</option>\n                </select>\n              </div>\n\n              {/* Customer Selection */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">العميل</label>\n                <select\n                  value={filters.customer || ''}\n                  onChange={(e) => setFilters(prev => ({ ...prev, customer: e.target.value || undefined }))}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                >\n                  <option value=\"\">جميع العملاء</option>\n                  {customers.map(customer => (\n                    <option key={customer.id} value={customer.id}>{customer.name}</option>\n                  ))}\n                </select>\n              </div>\n\n              {/* Supplier Selection */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">المورد</label>\n                <select\n                  value={filters.supplier || ''}\n                  onChange={(e) => setFilters(prev => ({ ...prev, supplier: e.target.value || undefined }))}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                >\n                  <option value=\"\">جميع الموردين</option>\n                  {suppliers.map(supplier => (\n                    <option key={supplier.id} value={supplier.id}>{supplier.name}</option>\n                  ))}\n                </select>\n              </div>\n\n              {/* Include Returns */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">المرتجعات</label>\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={filters.includeReturns || false}\n                    onChange={(e) => setFilters(prev => ({ ...prev, includeReturns: e.target.checked }))}\n                    className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                  />\n                  <span className=\"mr-2 text-sm text-gray-700\">تضمين المرتجعات</span>\n                </label>\n              </div>\n            </div>\n\n            {/* Custom Date Range */}\n            {filters.dateRange.preset === 'custom' && (\n              <div className=\"mt-4 grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">من تاريخ</label>\n                  <input\n                    type=\"date\"\n                    value={filters.dateRange.start}\n                    onChange={(e) => setFilters(prev => ({\n                      ...prev,\n                      dateRange: { ...prev.dateRange, start: e.target.value }\n                    }))}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">إلى تاريخ</label>\n                  <input\n                    type=\"date\"\n                    value={filters.dateRange.end}\n                    onChange={(e) => setFilters(prev => ({\n                      ...prev,\n                      dateRange: { ...prev.dateRange, end: e.target.value }\n                    }))}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  />\n                </div>\n              </div>\n            )}\n          </div>\n        )}\n\n        {/* Reports Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n          {AVAILABLE_REPORTS.map((report) => {\n            const Icon = report.icon\n            return (\n              <div\n                key={report.id}\n                className=\"bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-all cursor-pointer\"\n              >\n                <div className=\"p-6\">\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <div className={`p-3 rounded-lg ${report.color} text-white`}>\n                      <Icon className=\"h-6 w-6\" />\n                    </div>\n                    <button\n                      onClick={() => generateReport(report.id)}\n                      disabled={loading}\n                      className=\"px-3 py-1 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center gap-1\"\n                    >\n                      {loading && selectedReport === report.id ? (\n                        <RefreshCw className=\"h-4 w-4 animate-spin\" />\n                      ) : (\n                        <Eye className=\"h-4 w-4\" />\n                      )}\n                      عرض\n                    </button>\n                  </div>\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">{report.title}</h3>\n                  <p className=\"text-sm text-gray-600\">{report.description}</p>\n                </div>\n              </div>\n            )\n          })}\n        </div>\n\n        {/* Report Modal */}\n        {showReportModal && reportData && (\n          <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n            <div className=\"bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden\">\n              <div className=\"flex items-center justify-between p-6 border-b\">\n                <h2 className=\"text-xl font-semibold text-gray-900\">{reportData.title}</h2>\n                <div className=\"flex items-center gap-3\">\n                  <button\n                    onClick={() => handlePrintReport()}\n                    className=\"flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700\"\n                  >\n                    <Printer className=\"h-4 w-4\" />\n                    طباعة\n                  </button>\n                  <button\n                    onClick={() => handleExportExcel()}\n                    className=\"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\"\n                  >\n                    <FileSpreadsheet className=\"h-4 w-4\" />\n                    تصدير Excel\n                  </button>\n                  <button\n                    onClick={() => handleExportPDF()}\n                    className=\"flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700\"\n                  >\n                    <Download className=\"h-4 w-4\" />\n                    تصدير PDF\n                  </button>\n                  <button\n                    onClick={() => setShowReportModal(false)}\n                    className=\"text-gray-400 hover:text-gray-600\"\n                  >\n                    <X className=\"h-5 w-5\" />\n                  </button>\n                </div>\n              </div>\n\n              <div className=\"p-6 overflow-y-auto max-h-[calc(90vh-120px)]\">\n                {/* Report Summary */}\n                <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\">\n                  <div className=\"bg-blue-50 rounded-lg p-4\">\n                    <div className=\"flex items-center justify-between\">\n                      <div>\n                        <p className=\"text-sm font-medium text-blue-600\">إجمالي السجلات</p>\n                        <p className=\"text-2xl font-bold text-blue-900\">{reportData.summary.totalRecords.toLocaleString()}</p>\n                      </div>\n                      <FileText className=\"h-8 w-8 text-blue-600\" />\n                    </div>\n                  </div>\n                  <div className=\"bg-green-50 rounded-lg p-4\">\n                    <div className=\"flex items-center justify-between\">\n                      <div>\n                        <p className=\"text-sm font-medium text-green-600\">إجمالي المبلغ</p>\n                        <p className=\"text-2xl font-bold text-green-900\">{Math.round(reportData.summary.totalAmount).toLocaleString()} د.ع</p>\n                      </div>\n                      <DollarSign className=\"h-8 w-8 text-green-600\" />\n                    </div>\n                  </div>\n                  <div className=\"bg-purple-50 rounded-lg p-4\">\n                    <div className=\"flex items-center justify-between\">\n                      <div>\n                        <p className=\"text-sm font-medium text-purple-600\">متوسط المبلغ</p>\n                        <p className=\"text-2xl font-bold text-purple-900\">{Math.round(reportData.summary.averageAmount).toLocaleString()} د.ع</p>\n                      </div>\n                      <BarChart3 className=\"h-8 w-8 text-purple-600\" />\n                    </div>\n                  </div>\n                  <div className=\"bg-orange-50 rounded-lg p-4\">\n                    <div className=\"flex items-center justify-between\">\n                      <div>\n                        <p className=\"text-sm font-medium text-orange-600\">الفترة</p>\n                        <p className=\"text-sm font-bold text-orange-900\">\n                          {filters.dateRange.start} إلى {filters.dateRange.end}\n                        </p>\n                      </div>\n                      <Calendar className=\"h-8 w-8 text-orange-600\" />\n                    </div>\n                  </div>\n\n                  {/* Additional Summary Cards */}\n                  {reportData.summary.paidAmount !== undefined && (\n                    <div className=\"bg-emerald-50 rounded-lg p-4\">\n                      <div className=\"flex items-center justify-between\">\n                        <div>\n                          <p className=\"text-sm font-medium text-emerald-600\">المبلغ المدفوع</p>\n                          <p className=\"text-2xl font-bold text-emerald-900\">{Math.round(reportData.summary.paidAmount).toLocaleString()} د.ع</p>\n                        </div>\n                        <CheckCircle className=\"h-8 w-8 text-emerald-600\" />\n                      </div>\n                    </div>\n                  )}\n\n                  {reportData.summary.pendingAmount !== undefined && (\n                    <div className=\"bg-red-50 rounded-lg p-4\">\n                      <div className=\"flex items-center justify-between\">\n                        <div>\n                          <p className=\"text-sm font-medium text-red-600\">المبلغ المعلق</p>\n                          <p className=\"text-2xl font-bold text-red-900\">{Math.round(reportData.summary.pendingAmount).toLocaleString()} د.ع</p>\n                        </div>\n                        <Clock className=\"h-8 w-8 text-red-600\" />\n                      </div>\n                    </div>\n                  )}\n\n                  {reportData.summary.netProfit !== undefined && (\n                    <div className=\"bg-cyan-50 rounded-lg p-4\">\n                      <div className=\"flex items-center justify-between\">\n                        <div>\n                          <p className=\"text-sm font-medium text-cyan-600\">صافي الربح</p>\n                          <p className=\"text-2xl font-bold text-cyan-900\">{Math.round(reportData.summary.netProfit).toLocaleString()} د.ع</p>\n                        </div>\n                        <TrendingUp className=\"h-8 w-8 text-cyan-600\" />\n                      </div>\n                    </div>\n                  )}\n\n                  {reportData.summary.profitMargin !== undefined && (\n                    <div className=\"bg-indigo-50 rounded-lg p-4\">\n                      <div className=\"flex items-center justify-between\">\n                        <div>\n                          <p className=\"text-sm font-medium text-indigo-600\">هامش الربح</p>\n                          <p className=\"text-2xl font-bold text-indigo-900\">{Math.round(reportData.summary.profitMargin * 100) / 100}%</p>\n                        </div>\n                        <Target className=\"h-8 w-8 text-indigo-600\" />\n                      </div>\n                    </div>\n                  )}\n                </div>\n\n                {/* Report Data Table */}\n                {reportData.data.length > 0 && (\n                  <div className=\"bg-white rounded-lg border border-gray-200 overflow-hidden\">\n                    <div className=\"px-6 py-4 border-b border-gray-200\">\n                      <h3 className=\"text-lg font-semibold text-gray-900\">تفاصيل التقرير</h3>\n                      <p className=\"text-sm text-gray-600 mt-1\">عدد السجلات: {reportData.data.length}</p>\n                    </div>\n                    <div className=\"overflow-x-auto\">\n                      <table className=\"min-w-full divide-y divide-gray-200\">\n                        <thead className=\"bg-gray-50\">\n                          <tr>\n                            {Object.keys(reportData.data[0] || {}).map((key) => (\n                              <th key={key} className=\"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                                {translateColumnName(key)}\n                              </th>\n                            ))}\n                          </tr>\n                        </thead>\n                        <tbody className=\"bg-white divide-y divide-gray-200\">\n                          {reportData.data.map((row, index) => (\n                            <tr key={index} className=\"hover:bg-gray-50\">\n                              {Object.entries(row).map(([key, value]: [string, any], cellIndex) => (\n                                <td\n                                  key={cellIndex}\n                                  className={`px-4 py-3 text-sm text-gray-900 ${\n                                    key === 'المواد' || key === 'المواد المشتراة' ? 'max-w-xs' : 'whitespace-nowrap'\n                                  }`}\n                                >\n                                  {key === 'المواد' || key === 'المواد المشتراة' ? (\n                                    <div className=\"text-xs text-gray-600 break-words\">\n                                      {formatCellValue(value)}\n                                    </div>\n                                  ) : (\n                                    formatCellValue(value)\n                                  )}\n                                </td>\n                              ))}\n                            </tr>\n                          ))}\n                        </tbody>\n                      </table>\n                    </div>\n                  </div>\n                )}\n\n                {/* Charts Section */}\n                {reportData.charts && reportData.charts.length > 0 && (\n                  <div className=\"mt-6\">\n                    <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">الرسوم البيانية</h3>\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                      {reportData.charts.map((chart, index) => (\n                        <div key={index} className=\"bg-gray-50 rounded-lg p-4\">\n                          <h4 className=\"text-md font-medium text-gray-700 mb-2\">\n                            {chart.type === 'bar' ? 'رسم بياني عمودي' :\n                             chart.type === 'pie' ? 'رسم بياني دائري' :\n                             'رسم بياني خطي'}\n                          </h4>\n                          <div className=\"h-64 flex items-center justify-center text-gray-500\">\n                            <PieChart className=\"h-16 w-16\" />\n                            <span className=\"mr-2\">الرسم البياني سيتم عرضه هنا</span>\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </AppLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAsCA;;;AA1CA;;;;;AAiGA,yBAAyB;AACzB,MAAM,oBAAoB;IACxB;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM,yNAAA,CAAA,eAAY;QAClB,UAAU;QACV,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM,2MAAA,CAAA,UAAO;QACb,UAAU;QACV,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM,qNAAA,CAAA,aAAU;QAChB,UAAU;QACV,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM,6MAAA,CAAA,WAAQ;QACd,UAAU;QACV,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM,uMAAA,CAAA,QAAK;QACX,UAAU;QACV,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM,mNAAA,CAAA,YAAS;QACf,UAAU;QACV,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM,qNAAA,CAAA,aAAU;QAChB,UAAU;QACV,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM,yMAAA,CAAA,SAAM;QACZ,UAAU;QACV,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM,yMAAA,CAAA,SAAM;QACZ,UAAU;QACV,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM,6MAAA,CAAA,WAAQ;QACd,UAAU;QACV,OAAO;IACT;CACD;AAED,iCAAiC;AACjC,MAAM,eAAe;IACnB;QAAE,IAAI;QAAS,OAAO;QAAS,MAAM;IAAE;IACvC;QAAE,IAAI;QAAQ,OAAO;QAAe,MAAM;IAAE;IAC5C;QAAE,IAAI;QAAS,OAAO;QAAa,MAAM;IAAG;IAC5C;QAAE,IAAI;QAAW,OAAO;QAAa,MAAM;IAAG;IAC9C;QAAE,IAAI;QAAQ,OAAO;QAAa,MAAM;IAAI;IAC5C;QAAE,IAAI;QAAU,OAAO;QAAc,MAAM,CAAC;IAAE;CAC/C;AAEc,SAAS;;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IACxE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IAChE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IACpD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IACpD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAEpD,qBAAqB;IACrB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;QACpD,WAAW;YACT,OAAO,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAClF,KAAK,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAC3C,QAAQ;QACV;QACA,eAAe;QACf,eAAe;QACf,gBAAgB;IAClB;IAEA,0BAA0B;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6CAAE;YACR;QACF;4CAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,CAAC,iBAAiB,iBAAiB,gBAAgB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAC5E,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;gBACX,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;gBACX,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;aACZ;YAED,aAAa,gBAAgB,IAAI,IAAI,EAAE;YACvC,aAAa,gBAAgB,IAAI,IAAI,EAAE;YACvC,aAAa,gBAAgB,IAAI,IAAI,EAAE;QACzC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;IAEA,kDAAkD;IAClD,MAAM,mBAAmB,CAAC;QACxB,MAAM,QAAQ,IAAI;QAClB,IAAI,YAAY,IAAI;QAEpB,OAAQ;YACN,KAAK;gBACH,YAAY,IAAI,KAAK;gBACrB;YACF,KAAK;gBACH,YAAY,IAAI,KAAK,MAAM,OAAO,KAAK,IAAI,KAAK,KAAK,KAAK;gBAC1D;YACF,KAAK;gBACH,YAAY,IAAI,KAAK,MAAM,OAAO,KAAK,KAAK,KAAK,KAAK,KAAK;gBAC3D;YACF,KAAK;gBACH,YAAY,IAAI,KAAK,MAAM,OAAO,KAAK,KAAK,KAAK,KAAK,KAAK;gBAC3D;YACF,KAAK;gBACH,YAAY,IAAI,KAAK,MAAM,OAAO,KAAK,MAAM,KAAK,KAAK,KAAK;gBAC5D;YACF;gBACE;QACJ;QAEA,WAAW,CAAA,OAAQ,CAAC;gBAClB,GAAG,IAAI;gBACP,WAAW;oBACT,GAAG,KAAK,SAAS;oBACjB,OAAO,UAAU,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;oBAC5C,KAAK,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;oBACtC,QAAQ;gBACV;YACF,CAAC;IACH;IAEA,gBAAgB;IAChB,MAAM,iBAAiB,OAAO;QAC5B,WAAW;QACX,kBAAkB;QAElB,IAAI;YACF,MAAM,OAAO,MAAM,mBAAmB;YACtC,cAAc;YACd,mBAAmB;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,uBAAuB;IACvB,MAAM,qBAAqB,OAAO;QAChC,OAAQ;YACN,KAAK;gBACH,OAAO,MAAM;YACf,KAAK;gBACH,OAAO,MAAM;YACf,KAAK;gBACH,OAAO,MAAM;YACf,KAAK;gBACH,OAAO,MAAM;YACf,KAAK;gBACH,OAAO,MAAM;YACf,KAAK;gBACH,OAAO,MAAM;YACf,KAAK;gBACH,OAAO,MAAM;YACf,KAAK;gBACH,OAAO,MAAM;YACf,KAAK;gBACH,OAAO,MAAM;YACf,KAAK;gBACH,OAAO,MAAM;YACf;oBAEW;gBADT,OAAO;oBACL,OAAO,EAAA,0BAAA,kBAAkB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,yBAArC,8CAAA,wBAAkD,KAAK,KAAI;oBAClE,SAAS;wBACP,cAAc;wBACd,aAAa;wBACb,eAAe;oBACjB;oBACA,MAAM,EAAE;gBACV;QACJ;IACF;IAEA,iBAAiB;IACjB,MAAM,sBAAsB;QAC1B,MAAM,SAAS,MAAM,CAAA,GAAA,yHAAA,CAAA,mBAAgB,AAAD;QACpC,MAAM,QAAQ,OAAO,IAAI,IAAI,EAAE;QAE/B,sCAAsC;QACtC,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA;YACjC,MAAM,WAAW,IAAI,KAAK,KAAK,UAAU;YACzC,MAAM,YAAY,IAAI,KAAK,QAAQ,SAAS,CAAC,KAAK;YAClD,MAAM,UAAU,IAAI,KAAK,QAAQ,SAAS,CAAC,GAAG,GAAG;YAEjD,IAAI,WAAW,aAAa,WAAW,SAAS,OAAO;YACvD,IAAI,QAAQ,aAAa,IAAI,QAAQ,aAAa,KAAK,SAAS,KAAK,cAAc,KAAK,QAAQ,aAAa,EAAE,OAAO;YACtH,IAAI,QAAQ,aAAa,IAAI,QAAQ,aAAa,KAAK,SAAS,KAAK,cAAc,KAAK,QAAQ,aAAa,EAAE,OAAO;YACtH,IAAI,QAAQ,QAAQ,IAAI,KAAK,WAAW,KAAK,QAAQ,QAAQ,EAAE,OAAO;YAEtE,OAAO;QACT;QAEA,MAAM,cAAc,cAAc,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,CAAC,KAAK,YAAY,IAAI,CAAC,GAAG;QACxF,MAAM,gBAAgB,cAAc,MAAM,GAAG,IAAI,cAAc,cAAc,MAAM,GAAG;QACtF,MAAM,aAAa,cAAc,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,CAAC,KAAK,WAAW,IAAI,CAAC,GAAG;QACtF,MAAM,gBAAgB,cAAc;QAEpC,iCAAiC;QACjC,MAAM,iBAAiB,cAAc,GAAG,CAAC,CAAA;YACvC,MAAM,QAAQ,KAAK,mBAAmB,IAAI,EAAE;YAC5C,MAAM,YAAY,MAAM,MAAM,GAAG,IAC7B,MAAM,GAAG,CAAC,CAAC;oBACY,kCAAA;gBAArB,MAAM,eAAe,EAAA,yBAAA,KAAK,gBAAgB,cAArB,8CAAA,mCAAA,uBAAuB,SAAS,cAAhC,uDAAA,iCAAkC,IAAI,KAAI,KAAK,aAAa,IAAI;gBACrF,OAAO,AAAC,GAAmB,OAAjB,cAAa,MAAkB,OAAd,KAAK,QAAQ,EAAC;YAC3C,GAAG,IAAI,CAAC,QACR;YAEJ,OAAO;gBACL,GAAG,IAAI;gBACP;YACF;QACF;QAEA,OAAO;YACL,OAAO;YACP,SAAS;gBACP,cAAc,cAAc,MAAM;gBAClC;gBACA;gBACA;gBACA;YACF;YACA,MAAM,eAAe,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAChC,gBAAgB,KAAK,cAAc;oBACnC,UAAU,KAAK,aAAa,IAAI;oBAChC,WAAW,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB,CAAC;oBACxD,mBAAmB,CAAC,KAAK,YAAY,IAAI,CAAC,EAAE,cAAc,KAAK;oBAC/D,kBAAkB,CAAC,KAAK,WAAW,IAAI,CAAC,EAAE,cAAc,KAAK;oBAC7D,kBAAkB,CAAC,CAAC,KAAK,YAAY,IAAI,CAAC,IAAI,CAAC,KAAK,WAAW,IAAI,CAAC,CAAC,EAAE,cAAc,KAAK;oBAC1F,cAAc,KAAK,cAAc,KAAK,SAAS,UAAU,KAAK,cAAc,KAAK,YAAY,SAAS;oBACtG,eAAe,KAAK,cAAc,KAAK,SAAS,UAAU;oBAC1D,UAAU,KAAK,SAAS;gBAC1B,CAAC;YACD,QAAQ;gBACN;oBACE,MAAM;oBACN,MAAM;wBACJ;4BAAE,MAAM;4BAAS,OAAO,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,cAAc,KAAK,QAAQ,MAAM;wBAAC;wBACtF;4BAAE,MAAM;4BAAQ,OAAO,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,cAAc,KAAK,WAAW,MAAM;wBAAC;wBACxF;4BAAE,MAAM;4BAAQ,OAAO,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,cAAc,KAAK,WAAW,MAAM;wBAAC;qBACzF;oBACD,QAAQ;wBAAC;wBAAS;wBAAQ;qBAAO;gBACnC;aACD;QACH;IACF;IAEA,kBAAkB;IAClB,MAAM,0BAA0B;QAC9B,MAAM,SAAS,MAAM,CAAA,GAAA,yHAAA,CAAA,sBAAmB,AAAD;QACvC,MAAM,YAAY,OAAO,IAAI,IAAI,EAAE;QAEnC,MAAM,oBAAoB,UAAU,MAAM,CAAC,CAAA;YACzC,MAAM,eAAe,IAAI,KAAK,SAAS,UAAU;YACjD,MAAM,YAAY,IAAI,KAAK,QAAQ,SAAS,CAAC,KAAK;YAClD,MAAM,UAAU,IAAI,KAAK,QAAQ,SAAS,CAAC,GAAG,GAAG;YAEjD,IAAI,eAAe,aAAa,eAAe,SAAS,OAAO;YAC/D,IAAI,QAAQ,aAAa,IAAI,QAAQ,aAAa,KAAK,SAAS,SAAS,cAAc,KAAK,QAAQ,aAAa,EAAE,OAAO;YAC1H,IAAI,QAAQ,QAAQ,IAAI,SAAS,WAAW,KAAK,QAAQ,QAAQ,EAAE,OAAO;YAE1E,OAAO;QACT;QAEA,MAAM,cAAc,kBAAkB,MAAM,CAAC,CAAC,KAAK,WAAa,MAAM,CAAC,SAAS,YAAY,IAAI,CAAC,GAAG;QACpG,MAAM,gBAAgB,kBAAkB,MAAM,GAAG,IAAI,cAAc,kBAAkB,MAAM,GAAG;QAC9F,MAAM,aAAa,kBAAkB,MAAM,CAAC,CAAC,KAAK,WAAa,MAAM,CAAC,SAAS,WAAW,IAAI,CAAC,GAAG;QAClG,MAAM,gBAAgB,cAAc;QAEpC,iCAAiC;QACjC,MAAM,qBAAqB,kBAAkB,GAAG,CAAC,CAAA;YAC/C,MAAM,QAAQ,SAAS,sBAAsB,IAAI,EAAE;YACnD,MAAM,YAAY,MAAM,MAAM,GAAG,IAC7B,MAAM,GAAG,CAAC,CAAC;oBACY;gBAArB,MAAM,eAAe,EAAA,kBAAA,KAAK,SAAS,cAAd,sCAAA,gBAAgB,IAAI,KAAI,KAAK,aAAa,IAAI;gBACnE,OAAO,AAAC,GAAmB,OAAjB,cAAa,MAAkB,OAAd,KAAK,QAAQ,EAAC;YAC3C,GAAG,IAAI,CAAC,QACR;YAEJ,OAAO;gBACL,GAAG,QAAQ;gBACX;YACF;QACF;QAEA,OAAO;YACL,OAAO;YACP,SAAS;gBACP,cAAc,kBAAkB,MAAM;gBACtC;gBACA;gBACA;gBACA;YACF;YACA,MAAM,mBAAmB,GAAG,CAAC,CAAA;oBAEjB;uBAF8B;oBACxC,gBAAgB,SAAS,cAAc;oBACvC,UAAU,EAAA,sBAAA,SAAS,SAAS,cAAlB,0CAAA,oBAAoB,IAAI,KAAI;oBACtC,WAAW,IAAI,KAAK,SAAS,UAAU,EAAE,kBAAkB,CAAC;oBAC5D,mBAAmB,CAAC,SAAS,YAAY,IAAI,CAAC,EAAE,cAAc,KAAK;oBACnE,kBAAkB,CAAC,SAAS,WAAW,IAAI,CAAC,EAAE,cAAc,KAAK;oBACjE,kBAAkB,CAAC,CAAC,SAAS,YAAY,IAAI,CAAC,IAAI,CAAC,SAAS,WAAW,IAAI,CAAC,CAAC,EAAE,cAAc,KAAK;oBAClG,cAAc,SAAS,cAAc,KAAK,SAAS,UAAU,SAAS,cAAc,KAAK,YAAY,SAAS;oBAC9G,UAAU,SAAS,SAAS;gBAC9B;;YACA,QAAQ;gBACN;oBACE,MAAM;oBACN,MAAM;wBACJ;4BAAE,MAAM;4BAAS,OAAO,kBAAkB,MAAM,CAAC,CAAA,IAAK,EAAE,cAAc,KAAK,QAAQ,MAAM;wBAAC;wBAC1F;4BAAE,MAAM;4BAAQ,OAAO,kBAAkB,MAAM,CAAC,CAAA,IAAK,EAAE,cAAc,KAAK,WAAW,MAAM;wBAAC;wBAC5F;4BAAE,MAAM;4BAAQ,OAAO,kBAAkB,MAAM,CAAC,CAAA,IAAK,EAAE,cAAc,KAAK,WAAW,MAAM;wBAAC;qBAC7F;oBACD,QAAQ;wBAAC;wBAAS;wBAAQ;qBAAO;gBACnC;aACD;QACH;IACF;IAEA,iBAAiB;IACjB,MAAM,0BAA0B;QAC9B,MAAM,CAAC,aAAa,iBAAiB,WAAW,GAAG,MAAM,QAAQ,GAAG,CAAC;YACnE,CAAA,GAAA,yHAAA,CAAA,mBAAgB,AAAD;YACf,CAAA,GAAA,yHAAA,CAAA,sBAAmB,AAAD;YAClB,CAAA,GAAA,yHAAA,CAAA,sBAAmB,AAAD;SACnB;QAED,MAAM,QAAQ,YAAY,IAAI,IAAI,EAAE;QACpC,MAAM,YAAY,gBAAgB,IAAI,IAAI,EAAE;QAC5C,MAAM,eAAe,WAAW,IAAI,IAAI,EAAE;QAE1C,6BAA6B;QAC7B,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA;YACjC,MAAM,WAAW,IAAI,KAAK,KAAK,UAAU;YACzC,MAAM,YAAY,IAAI,KAAK,QAAQ,SAAS,CAAC,KAAK;YAClD,MAAM,UAAU,IAAI,KAAK,QAAQ,SAAS,CAAC,GAAG,GAAG;YACjD,OAAO,YAAY,aAAa,YAAY;QAC9C;QAEA,MAAM,oBAAoB,UAAU,MAAM,CAAC,CAAA;YACzC,MAAM,eAAe,IAAI,KAAK,SAAS,UAAU;YACjD,MAAM,YAAY,IAAI,KAAK,QAAQ,SAAS,CAAC,KAAK;YAClD,MAAM,UAAU,IAAI,KAAK,QAAQ,SAAS,CAAC,GAAG,GAAG;YACjD,OAAO,gBAAgB,aAAa,gBAAgB;QACtD;QAEA,MAAM,uBAAuB,aAAa,MAAM,CAAC,CAAA;YAC/C,MAAM,kBAAkB,IAAI,KAAK,YAAY,UAAU;YACvD,MAAM,YAAY,IAAI,KAAK,QAAQ,SAAS,CAAC,KAAK;YAClD,MAAM,UAAU,IAAI,KAAK,QAAQ,SAAS,CAAC,GAAG,GAAG;YACjD,OAAO,mBAAmB,aAAa,mBAAmB;QAC5D;QAEA,MAAM,eAAe,cAAc,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,CAAC,KAAK,YAAY,IAAI,CAAC,GAAG;QACzF,MAAM,gBAAgB,kBAAkB,MAAM,CAAC,CAAC,KAAK,WAAa,MAAM,CAAC,SAAS,YAAY,IAAI,CAAC,GAAG;QACtG,MAAM,YAAY,eAAe;QACjC,MAAM,eAAe,eAAe,IAAI,AAAC,YAAY,eAAgB,MAAM;QAE3E,qBAAqB;QACrB,MAAM,aAAa,qBAChB,MAAM,CAAC,CAAA,IAAK,EAAE,gBAAgB,KAAK,UACnC,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;QACtC,MAAM,eAAe,qBAClB,MAAM,CAAC,CAAA,IAAK,EAAE,gBAAgB,KAAK,WACnC,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;QACtC,MAAM,cAAc,aAAa;QAEjC,OAAO;YACL,OAAO;YACP,SAAS;gBACP,cAAc,cAAc,MAAM,GAAG,kBAAkB,MAAM;gBAC7D,aAAa;gBACb,eAAe,eAAe,CAAC,cAAc,MAAM,IAAI,CAAC;gBACxD;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;YACF;YACA,MAAM;gBACJ;oBAAE,SAAS;oBAAoB,UAAU,aAAa,cAAc,KAAK;oBAAQ,SAAS;gBAAU;gBACpG;oBAAE,SAAS;oBAAoB,UAAU,cAAc,cAAc,KAAK;oBAAQ,SAAS;gBAAU;gBACrG;oBAAE,SAAS;oBAAc,UAAU,UAAU,cAAc,KAAK;oBAAQ,SAAS;gBAAM;gBACvF;oBAAE,SAAS;oBAAc,UAAU,aAAa,OAAO,CAAC,KAAK;oBAAK,SAAS;gBAAO;gBAClF;oBAAE,SAAS;oBAAgB,UAAU,WAAW,cAAc,KAAK;oBAAQ,SAAS;gBAAY;gBAChG;oBAAE,SAAS;oBAAgB,UAAU,aAAa,cAAc,KAAK;oBAAQ,SAAS;gBAAY;gBAClG;oBAAE,SAAS;oBAAsB,UAAU,YAAY,cAAc,KAAK;oBAAQ,SAAS;gBAAY;aACxG;YACD,QAAQ;gBACN;oBACE,MAAM;oBACN,MAAM;wBACJ;4BAAE,MAAM;4BAAa,OAAO;wBAAa;wBACzC;4BAAE,MAAM;4BAAa,OAAO;wBAAc;qBAC3C;oBACD,QAAQ;wBAAC;wBAAa;qBAAY;gBACpC;aACD;QACH;IACF;IAEA,kCAAkC;IAClC,MAAM,0BAA0B;QAC9B,MAAM,SAAS,MAAM,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;QAChC,MAAM,YAAY,OAAO,IAAI,IAAI,EAAE;QAEnC,OAAO;YACL,OAAO;YACP,SAAS;gBACP,cAAc,UAAU,MAAM;gBAC9B,aAAa;gBACb,eAAe;YACjB;YACA,MAAM,UAAU,GAAG,CAAC,CAAA;oBAGA,4BACA,6BAAA;uBAJa;oBAC/B,cAAc,SAAS,IAAI;oBAC3B,kBAAkB,SAAS,YAAY,IAAI;oBAC3C,kBAAkB,EAAA,6BAAA,SAAS,gBAAgB,cAAzB,iDAAA,2BAA2B,MAAM,CAAC,CAAC,KAAa,QAAe,MAAM,CAAC,MAAM,QAAQ,IAAI,CAAC,GAAG,OAAM;oBACpH,kBAAkB,EAAA,8BAAA,SAAS,gBAAgB,cAAzB,mDAAA,8BAAA,2BAA2B,CAAC,EAAE,cAA9B,kDAAA,4BAAgC,WAAW,IAAG,IAAI,KAAK,SAAS,gBAAgB,CAAC,EAAE,CAAC,WAAW,EAAE,kBAAkB,CAAC,WAAW;gBACnJ;;QACF;IACF;IAEA,MAAM,4BAA4B;QAChC,MAAM,CAAC,aAAa,gBAAgB,GAAG,MAAM,QAAQ,GAAG,CAAC;YACvD,CAAA,GAAA,yHAAA,CAAA,mBAAgB,AAAD;YACf,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;SACZ;QAED,MAAM,QAAQ,YAAY,IAAI,IAAI,EAAE;QACpC,MAAM,YAAY,gBAAgB,IAAI,IAAI,EAAE;QAE5C,6BAA6B;QAC7B,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA;YACjC,MAAM,WAAW,IAAI,KAAK,KAAK,UAAU;YACzC,MAAM,YAAY,IAAI,KAAK,QAAQ,SAAS,CAAC,KAAK;YAClD,MAAM,UAAU,IAAI,KAAK,QAAQ,SAAS,CAAC,GAAG,GAAG;YACjD,OAAO,YAAY,aAAa,YAAY;QAC9C;QAEA,8CAA8C;QAC9C,IAAI,QAAQ,QAAQ,EAAE;YACpB,MAAM,mBAAmB,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,QAAQ,QAAQ;YACtE,MAAM,gBAAgB,cAAc,MAAM,CAAC,CAAA,OAAQ,KAAK,WAAW,KAAK,QAAQ,QAAQ;YAExF,IAAI,oBAAoB,cAAc,MAAM,GAAG,GAAG;gBAChD,qCAAqC;gBACrC,MAAM,iBAAiB,cAAc,GAAG,CAAC,CAAA;oBACvC,MAAM,QAAQ,KAAK,mBAAmB,IAAI,EAAE;oBAC5C,MAAM,YAAY,MAAM,MAAM,GAAG,IAC7B,MAAM,GAAG,CAAC,CAAC;4BACY,kCAAA;wBAArB,MAAM,eAAe,EAAA,yBAAA,KAAK,gBAAgB,cAArB,8CAAA,mCAAA,uBAAuB,SAAS,cAAhC,uDAAA,iCAAkC,IAAI,KAAI,KAAK,aAAa,IAAI;wBACrF,OAAO,AAAC,GAAmB,OAAjB,cAAa,MAAuB,OAAnB,KAAK,QAAQ,EAAC,OAAkD,OAA7C,CAAC,KAAK,UAAU,IAAI,CAAC,EAAE,cAAc,IAAG,OAA8C,OAAzC,CAAC,KAAK,WAAW,IAAI,CAAC,EAAE,cAAc,IAAG;oBACtI,GAAG,IAAI,CAAC,SACR;oBAEJ,OAAO;wBACL,gBAAgB,KAAK,cAAc;wBACnC,WAAW,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB,CAAC;wBACxD,mBAAmB,CAAC,KAAK,YAAY,IAAI,CAAC,EAAE,cAAc,KAAK;wBAC/D,kBAAkB,CAAC,KAAK,WAAW,IAAI,CAAC,EAAE,cAAc,KAAK;wBAC7D,kBAAkB,CAAC,CAAC,KAAK,YAAY,IAAI,CAAC,IAAI,CAAC,KAAK,WAAW,IAAI,CAAC,CAAC,EAAE,cAAc,KAAK;wBAC1F,cAAc,KAAK,cAAc,KAAK,SAAS,UAAU,KAAK,cAAc,KAAK,YAAY,SAAS;wBACtG,mBAAmB;oBACrB;gBACF;gBAEA,MAAM,cAAc,cAAc,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,CAAC,KAAK,YAAY,IAAI,CAAC,GAAG;gBACxF,MAAM,aAAa,cAAc,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,CAAC,KAAK,WAAW,IAAI,CAAC,GAAG;gBAEtF,OAAO;oBACL,OAAO,AAAC,oBAAyC,OAAtB,iBAAiB,IAAI;oBAChD,SAAS;wBACP,cAAc,cAAc,MAAM;wBAClC;wBACA,eAAe,cAAc,MAAM,GAAG,IAAI,cAAc,cAAc,MAAM,GAAG;wBAC/E;wBACA,eAAe,cAAc;oBAC/B;oBACA,MAAM;gBACR;YACF;QACF;QAEA,wBAAwB;QACxB,MAAM,qBAAqB,UAAU,GAAG,CAAC,CAAA;YACvC,MAAM,gBAAgB,cAAc,MAAM,CAAC,CAAA,OAAQ,KAAK,WAAW,KAAK,SAAS,EAAE;YACnF,MAAM,cAAc,cAAc,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,CAAC,KAAK,YAAY,IAAI,CAAC,GAAG;YACxF,MAAM,aAAa,cAAc,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,CAAC,KAAK,WAAW,IAAI,CAAC,GAAG;YAEtF,OAAO;gBACL;gBACA;gBACA;gBACA,YAAY,cAAc,MAAM;YAClC;QACF,GAAG,MAAM,CAAC,CAAA,OAAQ,KAAK,UAAU,GAAG,GAAG,iCAAiC;;QAExE,OAAO;YACL,OAAO;YACP,SAAS;gBACP,cAAc,mBAAmB,MAAM;gBACvC,aAAa,mBAAmB,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,WAAW,EAAE;gBAC9E,eAAe,mBAAmB,MAAM,GAAG,IACzC,mBAAmB,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,WAAW,EAAE,KAAK,mBAAmB,MAAM,GAAG;YACtG;YACA,MAAM,mBAAmB,GAAG,CAAC,CAAA,OAAQ,CAAC;oBACpC,cAAc,KAAK,QAAQ,CAAC,IAAI;oBAChC,UAAU,KAAK,QAAQ,CAAC,KAAK,IAAI;oBACjC,WAAW,KAAK,QAAQ,CAAC,OAAO,IAAI;oBACpC,gBAAgB,KAAK,UAAU;oBAC/B,iBAAiB,KAAK,WAAW,CAAC,cAAc,KAAK;oBACrD,kBAAkB,KAAK,UAAU,CAAC,cAAc,KAAK;oBACrD,kBAAkB,CAAC,KAAK,WAAW,GAAG,KAAK,UAAU,EAAE,cAAc,KAAK;gBAC5E,CAAC;QACH;IACF;IAEA,MAAM,4BAA4B;QAChC,MAAM,CAAC,iBAAiB,gBAAgB,GAAG,MAAM,QAAQ,GAAG,CAAC;YAC3D,CAAA,GAAA,yHAAA,CAAA,sBAAmB,AAAD;YAClB,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;SACZ;QAED,MAAM,YAAY,gBAAgB,IAAI,IAAI,EAAE;QAC5C,MAAM,YAAY,gBAAgB,IAAI,IAAI,EAAE;QAE5C,8BAA8B;QAC9B,MAAM,oBAAoB,UAAU,MAAM,CAAC,CAAA;YACzC,MAAM,eAAe,IAAI,KAAK,SAAS,UAAU;YACjD,MAAM,YAAY,IAAI,KAAK,QAAQ,SAAS,CAAC,KAAK;YAClD,MAAM,UAAU,IAAI,KAAK,QAAQ,SAAS,CAAC,GAAG,GAAG;YACjD,OAAO,gBAAgB,aAAa,gBAAgB;QACtD;QAEA,8CAA8C;QAC9C,IAAI,QAAQ,QAAQ,EAAE;YACpB,MAAM,mBAAmB,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,QAAQ,QAAQ;YACtE,MAAM,oBAAoB,kBAAkB,MAAM,CAAC,CAAA,WAAY,SAAS,WAAW,KAAK,QAAQ,QAAQ;YAExG,IAAI,oBAAoB,kBAAkB,MAAM,GAAG,GAAG;gBACpD,qCAAqC;gBACrC,MAAM,qBAAqB,kBAAkB,GAAG,CAAC,CAAA;oBAC/C,MAAM,QAAQ,SAAS,sBAAsB,IAAI,EAAE;oBACnD,MAAM,YAAY,MAAM,MAAM,GAAG,IAC7B,MAAM,GAAG,CAAC,CAAC;4BACY;wBAArB,MAAM,eAAe,EAAA,kBAAA,KAAK,SAAS,cAAd,sCAAA,gBAAgB,IAAI,KAAI,KAAK,aAAa,IAAI;wBACnE,OAAO,AAAC,GAAmB,OAAjB,cAAa,MAAuB,OAAnB,KAAK,QAAQ,EAAC,OAAiD,OAA5C,CAAC,KAAK,SAAS,IAAI,CAAC,EAAE,cAAc,IAAG,OAA6C,OAAxC,CAAC,KAAK,UAAU,IAAI,CAAC,EAAE,cAAc,IAAG;oBACpI,GAAG,IAAI,CAAC,SACR;oBAEJ,OAAO;wBACL,gBAAgB,SAAS,cAAc;wBACvC,WAAW,IAAI,KAAK,SAAS,UAAU,EAAE,kBAAkB,CAAC;wBAC5D,mBAAmB,CAAC,SAAS,YAAY,IAAI,CAAC,EAAE,cAAc,KAAK;wBACnE,kBAAkB,CAAC,SAAS,WAAW,IAAI,CAAC,EAAE,cAAc,KAAK;wBACjE,kBAAkB,CAAC,CAAC,SAAS,YAAY,IAAI,CAAC,IAAI,CAAC,SAAS,WAAW,IAAI,CAAC,CAAC,EAAE,cAAc,KAAK;wBAClG,cAAc,SAAS,cAAc,KAAK,SAAS,UAAU,SAAS,cAAc,KAAK,YAAY,SAAS;wBAC9G,mBAAmB;oBACrB;gBACF;gBAEA,MAAM,cAAc,kBAAkB,MAAM,CAAC,CAAC,KAAK,WAAa,MAAM,CAAC,SAAS,YAAY,IAAI,CAAC,GAAG;gBACpG,MAAM,aAAa,kBAAkB,MAAM,CAAC,CAAC,KAAK,WAAa,MAAM,CAAC,SAAS,WAAW,IAAI,CAAC,GAAG;gBAElG,OAAO;oBACL,OAAO,AAAC,oBAAyC,OAAtB,iBAAiB,IAAI;oBAChD,SAAS;wBACP,cAAc,kBAAkB,MAAM;wBACtC;wBACA,eAAe,kBAAkB,MAAM,GAAG,IAAI,cAAc,kBAAkB,MAAM,GAAG;wBACvF;wBACA,eAAe,cAAc;oBAC/B;oBACA,MAAM;gBACR;YACF;QACF;QAEA,yBAAyB;QACzB,MAAM,yBAAyB,UAAU,GAAG,CAAC,CAAA;YAC3C,MAAM,oBAAoB,kBAAkB,MAAM,CAAC,CAAA,WAAY,SAAS,WAAW,KAAK,SAAS,EAAE;YACnG,MAAM,cAAc,kBAAkB,MAAM,CAAC,CAAC,KAAK,WAAa,MAAM,CAAC,SAAS,YAAY,IAAI,CAAC,GAAG;YACpG,MAAM,aAAa,kBAAkB,MAAM,CAAC,CAAC,KAAK,WAAa,MAAM,CAAC,SAAS,WAAW,IAAI,CAAC,GAAG;YAElG,OAAO;gBACL;gBACA;gBACA;gBACA,gBAAgB,kBAAkB,MAAM;YAC1C;QACF,GAAG,MAAM,CAAC,CAAA,OAAQ,KAAK,cAAc,GAAG,GAAG,mCAAmC;;QAE9E,OAAO;YACL,OAAO;YACP,SAAS;gBACP,cAAc,uBAAuB,MAAM;gBAC3C,aAAa,uBAAuB,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,WAAW,EAAE;gBAClF,eAAe,uBAAuB,MAAM,GAAG,IAC7C,uBAAuB,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,WAAW,EAAE,KAAK,uBAAuB,MAAM,GAAG;YAC9G;YACA,MAAM,uBAAuB,GAAG,CAAC,CAAA,OAAQ,CAAC;oBACxC,cAAc,KAAK,QAAQ,CAAC,IAAI;oBAChC,UAAU,KAAK,QAAQ,CAAC,KAAK,IAAI;oBACjC,WAAW,KAAK,QAAQ,CAAC,OAAO,IAAI;oBACpC,iBAAiB,KAAK,QAAQ,CAAC,cAAc,IAAI;oBACjD,gBAAgB,KAAK,cAAc;oBACnC,iBAAiB,KAAK,WAAW,CAAC,cAAc,KAAK;oBACrD,kBAAkB,KAAK,UAAU,CAAC,cAAc,KAAK;oBACrD,kBAAkB,CAAC,KAAK,WAAW,GAAG,KAAK,UAAU,EAAE,cAAc,KAAK;gBAC5E,CAAC;QACH;IACF;IAEA,MAAM,2BAA2B;QAC/B,MAAM,CAAC,aAAa,gBAAgB,GAAG,MAAM,QAAQ,GAAG,CAAC;YACvD,CAAA,GAAA,yHAAA,CAAA,mBAAgB,AAAD;YACf,CAAA,GAAA,yHAAA,CAAA,sBAAmB,AAAD;SACnB;QAED,MAAM,QAAQ,YAAY,IAAI,IAAI,EAAE;QACpC,MAAM,YAAY,gBAAgB,IAAI,IAAI,EAAE;QAE5C,MAAM,UAAU,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,CAAC,KAAK,YAAY,IAAI,CAAC,GAAG;QAC5E,MAAM,QAAQ,UAAU,MAAM,CAAC,CAAC,KAAK,WAAa,MAAM,CAAC,SAAS,YAAY,IAAI,CAAC,GAAG;QACtF,MAAM,cAAc,UAAU;QAE9B,OAAO;YACL,OAAO;YACP,SAAS;gBACP,cAAc,MAAM,MAAM,GAAG,UAAU,MAAM;gBAC7C,aAAa;gBACb,eAAe;YACjB;YACA,MAAM;gBACJ;oBAAE,SAAS;oBAAa,UAAU,QAAQ,cAAc,KAAK;gBAAO;gBACpE;oBAAE,SAAS;oBAAyB,UAAU,MAAM,cAAc,KAAK;gBAAO;gBAC9E;oBAAE,SAAS;oBAAgB,UAAU,YAAY,cAAc,KAAK;gBAAO;gBAC3E;oBAAE,SAAS;oBAAuB,UAAU,UAAU,IAAI,CAAC,AAAC,cAAc,UAAW,GAAG,EAAE,OAAO,CAAC,KAAK,MAAM;gBAAK;aACnH;QACH;IACF;IAEA,MAAM,yBAAyB;QAC7B,MAAM,SAAS,MAAM,CAAA,GAAA,yHAAA,CAAA,sBAAmB,AAAD;QACvC,MAAM,eAAe,OAAO,IAAI,IAAI,EAAE;QAEtC,8BAA8B;QAC9B,MAAM,uBAAuB,aAAa,MAAM,CAAC,CAAA;YAC/C,MAAM,kBAAkB,IAAI,KAAK,YAAY,UAAU;YACvD,MAAM,YAAY,IAAI,KAAK,QAAQ,SAAS,CAAC,KAAK;YAClD,MAAM,UAAU,IAAI,KAAK,QAAQ,SAAS,CAAC,GAAG,GAAG;YACjD,OAAO,mBAAmB,aAAa,mBAAmB;QAC5D;QAEA,MAAM,SAAS,qBAAqB,MAAM,CAAC,CAAA,IAAK,EAAE,gBAAgB,KAAK,UAAU,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;QACpH,MAAM,WAAW,qBAAqB,MAAM,CAAC,CAAA,IAAK,EAAE,gBAAgB,KAAK,WAAW,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;QACvH,MAAM,UAAU,SAAS;QAEzB,mBAAmB;QACnB,MAAM,qBAAqB,qBAAqB,GAAG,CAAC,CAAA,cAAe,CAAC;gBAClE,WAAW,IAAI,KAAK,YAAY,UAAU,EAAE,kBAAkB,CAAC;gBAC/D,SAAS,YAAY,gBAAgB,KAAK,WAAW,SAAS;gBAC9D,UAAU,YAAY,MAAM,CAAC,cAAc,KAAK;gBAChD,SAAS,YAAY,WAAW,IAAI;gBACpC,UAAU,YAAY,cAAc,IAAI;YAC1C,CAAC;QAED,OAAO;YACL,OAAO;YACP,SAAS;gBACP,cAAc,qBAAqB,MAAM;gBACzC,aAAa;gBACb,eAAe;gBACf;gBACA;gBACA;YACF;YACA,MAAM;gBACJ;oBAAE,SAAS;oBAAiB,UAAU,OAAO,cAAc,KAAK;oBAAQ,SAAS;gBAAS;gBAC1F;oBAAE,SAAS;oBAAiB,UAAU,SAAS,cAAc,KAAK;oBAAQ,SAAS;gBAAO;gBAC1F;oBAAE,SAAS;oBAAsB,UAAU,QAAQ,cAAc,KAAK;oBAAQ,SAAS,WAAW,IAAI,WAAW;gBAAO;mBACrH;aACJ;YACD,QAAQ;gBACN;oBACE,MAAM;oBACN,MAAM;wBACJ;4BAAE,MAAM;4BAAU,OAAO;wBAAO;wBAChC;4BAAE,MAAM;4BAAU,OAAO;wBAAS;qBACnC;oBACD,QAAQ;wBAAC;wBAAU;qBAAS;gBAC9B;aACD;QACH;IACF;IAEA,MAAM,4BAA4B;QAChC,MAAM,SAAS,MAAM,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;QAChC,MAAM,YAAY,OAAO,IAAI,IAAI,EAAE;QAEnC,OAAO;YACL,OAAO;YACP,SAAS;gBACP,cAAc,UAAU,MAAM;gBAC9B,aAAa;gBACb,eAAe;YACjB;YACA,MAAM,UAAU,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,UAAU,QAAU,CAAC;oBACrD,WAAW,QAAQ;oBACnB,cAAc,SAAS,IAAI;oBAC3B,kBAAkB,SAAS,YAAY,IAAI;oBAC3C,kBAAkB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO;oBACpD,mBAAmB,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,WAAW,MAAM,EAAE,cAAc,KAAK;gBACvF,CAAC;QACH;IACF;IAEA,MAAM,iCAAiC;QACrC,MAAM,CAAC,aAAa,gBAAgB,GAAG,MAAM,QAAQ,GAAG,CAAC;YACvD,CAAA,GAAA,yHAAA,CAAA,mBAAgB,AAAD;YACf,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;SACZ;QAED,MAAM,QAAQ,YAAY,IAAI,IAAI,EAAE;QACpC,MAAM,YAAY,gBAAgB,IAAI,IAAI,EAAE;QAE5C,OAAO;YACL,OAAO;YACP,SAAS;gBACP,cAAc,UAAU,MAAM;gBAC9B,aAAa,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,CAAC,KAAK,YAAY,IAAI,CAAC,GAAG;gBACzE,eAAe;YACjB;YACA,MAAM,UAAU,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAA;gBAC/B,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,WAAW,KAAK,SAAS,EAAE;gBAC3E,OAAO;oBACL,cAAc,SAAS,IAAI;oBAC3B,gBAAgB,cAAc,MAAM;oBACpC,oBAAoB,cAAc,MAAM,GAAG,IACzC,CAAC,cAAc,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,CAAC,KAAK,YAAY,IAAI,CAAC,GAAG,KAAK,cAAc,MAAM,EAAE,cAAc,KAAK,SACnH;oBACF,aAAa,cAAc,MAAM,GAAG,IAClC,IAAI,KAAK,KAAK,GAAG,IAAI,cAAc,GAAG,CAAC,CAAA,OAAQ,IAAI,KAAK,KAAK,UAAU,EAAE,OAAO,MAAM,kBAAkB,CAAC,WACzG;gBACJ;YACF;QACF;IACF;IAEA,6CAA6C;IAC7C,MAAM,oBAAoB;QACxB,IAAI,CAAC,YAAY;QAEjB,6BAA6B;QAC7B,MAAM,EAAE,WAAW,EAAE;QACrB,MAAM,EAAE,gBAAgB,EAAE;QAE1B,6BAA6B;QAC7B,MAAM,kBAAkB;YACtB,aAAa;YACb,eAAe;YACf,gBAAgB;YAChB,cAAc;YACd,cAAc;YACd,UAAU;YACV,YAAY;YACZ,YAAY;YACZ,YAAY;YACZ,UAAU;YACV,WAAW;YACX,aAAa;YACb,YAAY;YACZ,gBAAgB;YAChB,eAAe;YACf,eAAe;YACf,aAAa;YACb,aAAa;YACb,WAAW;YACX,iBAAiB;QACnB;QAEA,oCAAoC;QACpC,YAAY,WAAW,IAAI,EAAE,kBAAkB,WAAW,WAAW,KAAK,EAAE;IAE9E;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,YAAY;QAEjB,IAAI;YACF,gCAAgC;YAChC,MAAM,OAAO;YAEb,sBAAsB;YACtB,MAAM,WAAW,KAAK,KAAK,CAAC,QAAQ;YAEpC,uCAAuC;YACvC,MAAM,cAAc;gBAClB;oBAAC;iBAAgC;gBACjC;oBAAC;iBAAG;gBACJ;oBAAC;oBAAgB,WAAW,KAAK;iBAAC;gBAClC;oBAAC;oBAAkB,IAAI,OAAO,kBAAkB,CAAC;iBAAS;gBAC1D;oBAAC;oBAAgB,IAAI,OAAO,kBAAkB,CAAC;iBAAS;gBACxD;oBAAC;oBAAoB,MAAoC,OAA/B,QAAQ,SAAS,CAAC,KAAK,EAAC,SAA6B,OAAtB,QAAQ,SAAS,CAAC,GAAG;iBAAG;gBACjF;oBAAC;iBAAG;gBACJ;oBAAC;iBAAkB;gBACnB;oBAAC;oBAAU;oBAAU;iBAAS;gBAC9B;oBAAC;oBAAkB,WAAW,OAAO,CAAC,YAAY;oBAAE;iBAAM;gBAC1D;oBAAC;oBAAiB,KAAK,KAAK,CAAC,WAAW,OAAO,CAAC,WAAW;oBAAG;iBAAc;gBAC5E;oBAAC;oBAAgB,KAAK,KAAK,CAAC,WAAW,OAAO,CAAC,aAAa;oBAAG;iBAAc;aAC9E;YAED,gCAAgC;YAChC,IAAI,WAAW,OAAO,CAAC,UAAU,KAAK,WAAW;gBAC/C,YAAY,IAAI,CAAC;oBAAC;oBAAkB,KAAK,KAAK,CAAC,WAAW,OAAO,CAAC,UAAU;oBAAG;iBAAc;YAC/F;YACA,IAAI,WAAW,OAAO,CAAC,aAAa,KAAK,WAAW;gBAClD,YAAY,IAAI,CAAC;oBAAC;oBAAiB,KAAK,KAAK,CAAC,WAAW,OAAO,CAAC,aAAa;oBAAG;iBAAc;YACjG;YACA,IAAI,WAAW,OAAO,CAAC,YAAY,KAAK,WAAW;gBACjD,YAAY,IAAI,CAAC;oBAAC;oBAAoB,KAAK,KAAK,CAAC,WAAW,OAAO,CAAC,YAAY;oBAAG;iBAAc;YACnG;YACA,IAAI,WAAW,OAAO,CAAC,aAAa,KAAK,WAAW;gBAClD,YAAY,IAAI,CAAC;oBAAC;oBAAoB,KAAK,KAAK,CAAC,WAAW,OAAO,CAAC,aAAa;oBAAG;iBAAc;YACpG;YACA,IAAI,WAAW,OAAO,CAAC,SAAS,KAAK,WAAW;gBAC9C,YAAY,IAAI,CAAC;oBAAC;oBAAc,KAAK,KAAK,CAAC,WAAW,OAAO,CAAC,SAAS;oBAAG;iBAAc;YAC1F;YACA,IAAI,WAAW,OAAO,CAAC,YAAY,KAAK,WAAW;gBACjD,YAAY,IAAI,CAAC;oBAAC;oBAAc,KAAK,KAAK,CAAC,WAAW,OAAO,CAAC,YAAY,GAAG,OAAO;oBAAK;iBAAI;YAC/F;YAEA,gCAAgC;YAChC,YAAY,IAAI,CAAC;gBAAC;aAAG;YACrB,YAAY,IAAI,CAAC;gBAAC;aAAkB;YACpC,YAAY,IAAI,CAAC;gBAAC;gBAAU;aAAS;YACrC,YAAY,IAAI,CAAC;gBAAC;gBAAc,QAAQ,aAAa,KAAK,QAAQ,iBAChE,QAAQ,aAAa,KAAK,SAAS,UACnC,QAAQ,aAAa,KAAK,YAAY,SAAS;aAAO;YACxD,YAAY,IAAI,CAAC;gBAAC;gBAAe,QAAQ,aAAa,KAAK,QAAQ,eACjE,QAAQ,aAAa,KAAK,SAAS,UAAU;aAAM;YACrD,IAAI,QAAQ,QAAQ,EAAE;gBACpB,MAAM,WAAW,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,QAAQ,QAAQ;gBAC9D,YAAY,IAAI,CAAC;oBAAC;oBAAiB,CAAA,qBAAA,+BAAA,SAAU,IAAI,KAAI;iBAAY;YACnE;YACA,IAAI,QAAQ,QAAQ,EAAE;gBACpB,MAAM,WAAW,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,QAAQ,QAAQ;gBAC9D,YAAY,IAAI,CAAC;oBAAC;oBAAiB,CAAA,qBAAA,+BAAA,SAAU,IAAI,KAAI;iBAAY;YACnE;YAEA,MAAM,mBAAmB,KAAK,KAAK,CAAC,YAAY,CAAC;YAEjD,yBAAyB;YACzB,MAAM,eAAe,KAAK,KAAK,CAAC,YAAY,CAAC,gBAAgB,CAAC,OAAO,IAAI;YAEzE,oBAAoB;YACpB,gBAAgB,CAAC,QAAQ,GAAG;gBAC1B;oBAAE,OAAO;gBAAG;gBACZ;oBAAE,OAAO;gBAAG;gBACZ;oBAAE,OAAO;gBAAG,EAAG,gBAAgB;aAChC;YAED,KAAK,KAAK,CAAC,iBAAiB,CAAC,UAAU,kBAAkB;YAEzD,qCAAqC;YACrC,IAAI,WAAW,IAAI,CAAC,MAAM,GAAG,GAAG;gBAC9B,mCAAmC;gBACnC,MAAM,iBAAiB,WAAW,IAAI,CAAC,GAAG,CAAC,CAAA;oBACzC,MAAM,gBAAqB,CAAC;oBAC5B,OAAO,OAAO,CAAC,KAAK,OAAO,CAAC;4BAAC,CAAC,KAAK,MAAM;wBACvC,MAAM,gBAAgB,oBAAoB;wBAC1C,aAAa,CAAC,cAAc,GAAG;oBACjC;oBACA,OAAO;gBACT;gBAEA,MAAM,mBAAmB,KAAK,KAAK,CAAC,aAAa,CAAC;gBAElD,uCAAuC;gBACvC,MAAM,UAAU,OAAO,IAAI,CAAC,cAAc,CAAC,EAAE,IAAI,CAAC;gBAClD,gBAAgB,CAAC,QAAQ,GAAG,QAAQ,GAAG,CAAC,CAAA;oBACtC,IAAI,WAAW,YAAY,WAAW,mBAAmB;wBACvD,OAAO;4BAAE,OAAO;wBAAG,EAAE,wBAAwB;;oBAC/C;oBACA,OAAO;wBAAE,OAAO;oBAAG;gBACrB;gBAEA,KAAK,KAAK,CAAC,iBAAiB,CAAC,UAAU,kBAAkB;YAC3D;YAEA,6CAA6C;YAC7C,IAAI,WAAW,MAAM,IAAI,WAAW,MAAM,CAAC,MAAM,GAAG,GAAG;gBACrD,MAAM,YAAY;oBAChB;wBAAC;qBAA8B;oBAC/B;wBAAC;qBAAG;iBACL;gBAED,WAAW,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO;oBAChC,UAAU,IAAI,CAAC;wBAAE,iBAA0B,OAAV,QAAQ;wBAAK,MAAM,IAAI,KAAK,QAAQ,UAAU,MAAM,IAAI,KAAK,QAAQ,UAAU;qBAAM;oBACtH,UAAU,IAAI,CAAC;wBAAC;wBAAW;qBAAS;oBAEpC,MAAM,IAAI,CAAC,OAAO,CAAC,CAAC;wBAClB,UAAU,IAAI,CAAC;4BAAC,KAAK,IAAI,IAAI;4BAAY,KAAK,KAAK,IAAI;yBAAE;oBAC3D;oBAEA,UAAU,IAAI,CAAC;wBAAC;qBAAG,GAAE,+BAA+B;gBACtD;gBAEA,MAAM,iBAAiB,KAAK,KAAK,CAAC,YAAY,CAAC;gBAC/C,cAAc,CAAC,QAAQ,GAAG;oBAAC;wBAAE,OAAO;oBAAG;oBAAG;wBAAE,OAAO;oBAAG;iBAAE;gBACxD,KAAK,KAAK,CAAC,iBAAiB,CAAC,UAAU,gBAAgB;YACzD;YAEA,cAAc;YACd,MAAM,WAAW,AAAC,GAAsB,OAApB,WAAW,KAAK,EAAC,KAA0C,OAAvC,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,EAAC;YAC/E,KAAK,SAAS,CAAC,UAAU;QAE3B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,mBAAmB;YACnB;QACF;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,YAAY;QAEjB,kBAAkB;QAClB,MAAM,aAAa;YACjB,UAAU;YACV;gBAAC,WAAW,KAAK;aAAC;YAClB;gBAAE,kBAAwD,OAAvC,IAAI,OAAO,kBAAkB,CAAC;aAAW;YAC5D;gBAAE,WAAyC,OAA/B,QAAQ,SAAS,CAAC,KAAK,EAAC,SAA6B,OAAtB,QAAQ,SAAS,CAAC,GAAG;aAAG;YACnE,EAAE;YAEF,SAAS;YACT;gBAAC;aAAS;YACV;gBAAC;gBAAkB,WAAW,OAAO,CAAC,YAAY,CAAC,cAAc;aAAG;YACpE;gBAAC;gBAAiB,WAAW,OAAO,CAAC,WAAW,CAAC,cAAc,KAAK;aAAO;YAC3E;gBAAC;gBAAgB,WAAW,OAAO,CAAC,aAAa,CAAC,cAAc,KAAK;aAAO;YAC5E,EAAE;YAEF,WAAW;eACP,WAAW,IAAI,CAAC,MAAM,GAAG,IAAI;gBAC/B,OAAO,IAAI,CAAC,WAAW,IAAI,CAAC,EAAE;mBAC3B,WAAW,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,OAAO,MAAM,CAAC;aAC7C,GAAG,EAAE;SACP;QAED,MAAM,YAAY,WAAW,GAAG,CAAC,CAAA,MAC/B,MAAM,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,KACrC,IAAI,CAAC;QAEP,oBAAoB;QACpB,MAAM,OAAO,IAAI,KAAK;YAAC,WAAW;SAAU,EAAE;YAAE,MAAM;QAA0B;QAChF,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,KAAK,YAAY,CAAC,QAAQ;QAC1B,KAAK,YAAY,CAAC,YAAY,AAAC,GAAsB,OAApB,WAAW,KAAK,EAAC,KAA0C,OAAvC,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,EAAC;QAC5F,KAAK,KAAK,CAAC,UAAU,GAAG;QACxB,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,KAAK,KAAK;QACV,SAAS,IAAI,CAAC,WAAW,CAAC;IAC5B;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,YAAY;QAEjB,IAAI;YACF,iCAAiC;YACjC,MAAM,EAAE,KAAK,EAAE,GAAG;;YAGlB,uBAAuB;YACvB,MAAM,MAAM,IAAI,MAAM;gBACpB,aAAa;gBACb,MAAM;gBACN,QAAQ;YACV;YAEA,aAAa;YACb,IAAI,OAAO,CAAC;YAEZ,4BAA4B;YAC5B,IAAI,YAAY,CAAC,KAAK,KAAK,MAAK,gBAAgB;YAChD,IAAI,IAAI,CAAC,GAAG,GAAG,KAAK,IAAI;YAExB,kBAAkB;YAClB,IAAI,YAAY,CAAC,KAAK,KAAK;YAC3B,IAAI,WAAW,CAAC;YAChB,IAAI,IAAI,CAAC,WAAW,KAAK,EAAE,KAAK,IAAI;gBAAE,OAAO;YAAS;YAEtD,iBAAiB;YACjB,IAAI,WAAW,CAAC;YAChB,IAAI,IAAI,CAAC,iCAAiC,KAAK,IAAI;gBAAE,OAAO;YAAS;YAErE,uBAAuB;YACvB,IAAI,YAAY,CAAC,GAAG,GAAG;YAEvB,2BAA2B;YAC3B,IAAI,YAAY,CAAC,KAAK,KAAK;YAC3B,IAAI,IAAI,CAAC,IAAI,IAAI,KAAK,IAAI;YAC1B,IAAI,YAAY,CAAC,KAAK,KAAK;YAC3B,IAAI,IAAI,CAAC,IAAI,IAAI,KAAK,IAAI;YAE1B,IAAI,WAAW,CAAC;YAChB,IAAI,IAAI,CAAC,AAAC,kBAAwD,OAAvC,IAAI,OAAO,kBAAkB,CAAC,WAAY,IAAI;YACzE,IAAI,IAAI,CAAC,AAAC,WAAyC,OAA/B,QAAQ,SAAS,CAAC,KAAK,EAAC,SAA6B,OAAtB,QAAQ,SAAS,CAAC,GAAG,GAAI,IAAI;YAEhF,yBAAyB;YACzB,IAAI,OAAO;YACX,IAAI,WAAW,CAAC;YAChB,IAAI,YAAY,CAAC,IAAI,IAAI;YACzB,IAAI,IAAI,CAAC,iBAAiB,IAAI;YAC9B,QAAQ;YAER,gBAAgB;YAChB,MAAM,eAAe;gBACnB;oBAAE,OAAO;oBAAkB,OAAO,WAAW,OAAO,CAAC,YAAY,CAAC,cAAc;oBAAI,OAAO;wBAAC;wBAAI;wBAAK;qBAAI;gBAAC;gBAC1G;oBAAE,OAAO;oBAAiB,OAAO,KAAK,KAAK,CAAC,WAAW,OAAO,CAAC,WAAW,EAAE,cAAc,KAAK;oBAAQ,OAAO;wBAAC;wBAAI;wBAAK;qBAAI;gBAAC;gBAC7H;oBAAE,OAAO;oBAAgB,OAAO,KAAK,KAAK,CAAC,WAAW,OAAO,CAAC,aAAa,EAAE,cAAc,KAAK;oBAAQ,OAAO;wBAAC;wBAAK;wBAAI;qBAAI;gBAAC;aAC/H;YAED,4BAA4B;YAC5B,IAAI,WAAW,OAAO,CAAC,UAAU,KAAK,WAAW;gBAC/C,aAAa,IAAI,CAAC;oBAChB,OAAO;oBACP,OAAO,KAAK,KAAK,CAAC,WAAW,OAAO,CAAC,UAAU,EAAE,cAAc,KAAK;oBACpE,OAAO;wBAAC;wBAAG;wBAAK;qBAAI;gBACtB;YACF;YACA,IAAI,WAAW,OAAO,CAAC,aAAa,KAAK,WAAW;gBAClD,aAAa,IAAI,CAAC;oBAChB,OAAO;oBACP,OAAO,KAAK,KAAK,CAAC,WAAW,OAAO,CAAC,aAAa,EAAE,cAAc,KAAK;oBACvE,OAAO;wBAAC;wBAAK;wBAAI;qBAAG;gBACtB;YACF;YAEA,oBAAoB;YACpB,MAAM,YAAY;YAClB,MAAM,aAAa;YACnB,IAAI,OAAO;YAEX,aAAa,OAAO,CAAC,CAAC,MAAM;gBAC1B,IAAI,QAAQ,KAAK,QAAQ,MAAM,GAAG;oBAChC,QAAQ;oBACR,OAAO;gBACT;gBAEA,gBAAgB;gBAChB,IAAI,YAAY,CAAC,KAAK,KAAK,CAAC,EAAE,EAAE,KAAK,KAAK,CAAC,EAAE,EAAE,KAAK,KAAK,CAAC,EAAE;gBAC5D,IAAI,IAAI,CAAC,MAAM,MAAM,WAAW,YAAY;gBAE5C,aAAa;gBACb,IAAI,YAAY,CAAC,KAAK,KAAK;gBAC3B,IAAI,WAAW,CAAC;gBAChB,IAAI,IAAI,CAAC,KAAK,KAAK,EAAE,OAAO,GAAG,OAAO;gBACtC,IAAI,WAAW,CAAC;gBAChB,IAAI,IAAI,CAAC,KAAK,KAAK,EAAE,OAAO,GAAG,OAAO;gBAEtC,QAAQ,YAAY;YACtB;YAEA,QAAQ;YAER,kBAAkB;YAClB,IAAI,WAAW,IAAI,CAAC,MAAM,GAAG,GAAG;gBAC9B,IAAI,YAAY,CAAC,GAAG,GAAG;gBACvB,IAAI,WAAW,CAAC;gBAChB,IAAI,IAAI,CAAC,mBAAmB,IAAI;gBAChC,QAAQ;gBAER,sBAAsB;gBACtB,MAAM,eAAe,OAAO,IAAI,CAAC,WAAW,IAAI,CAAC,EAAE,EAAE,GAAG,CAAC,CAAA,MAAO,oBAAoB;gBACpF,MAAM,YAAY,WAAW,IAAI,CAAC,GAAG,CAAC,CAAA,MACpC,OAAO,OAAO,CAAC,KAAK,GAAG,CAAC;4BAAC,CAAC,KAAK,MAAM;wBACnC,IAAI,QAAQ,YAAY,QAAQ,mBAAmB;4BACjD,0BAA0B;4BAC1B,MAAM,OAAO,OAAO,SAAS;4BAC7B,OAAO,KAAK,MAAM,GAAG,KAAK,KAAK,SAAS,CAAC,GAAG,MAAM,QAAQ;wBAC5D;wBACA,OAAO,OAAO,SAAS;oBACzB;gBAIA,IAAY,SAAS,CAAC;oBACtB,MAAM;wBAAC;qBAAa;oBACpB,MAAM;oBACN,QAAQ;oBACR,QAAQ;wBACN,UAAU;wBACV,aAAa;wBACb,WAAW;4BAAC;4BAAI;4BAAI;yBAAG;wBACvB,WAAW;4BAAC;4BAAK;4BAAK;yBAAI;wBAC1B,WAAW;4BAAC;4BAAK;4BAAK;yBAAI;wBAC1B,WAAW;oBACb;oBACA,YAAY;wBACV,WAAW;4BAAC;4BAAK;4BAAK;yBAAI;wBAC1B,WAAW;4BAAC;4BAAK;4BAAK;yBAAI;wBAC1B,WAAW;wBACX,UAAU;oBACZ;oBACA,oBAAoB;wBAClB,WAAW;4BAAC;4BAAK;4BAAK;yBAAI;oBAC5B;oBACA,QAAQ;wBAAE,KAAK;wBAAI,OAAO;wBAAI,QAAQ;wBAAI,MAAM;oBAAG;oBACnD,YAAY;oBACZ,cAAc;wBACZ,yBAAyB;wBACzB,CAAC,aAAa,OAAO,CAAC,UAAU,EAAE;4BAAE,WAAW;wBAAG;wBAClD,CAAC,aAAa,OAAO,CAAC,mBAAmB,EAAE;4BAAE,WAAW;wBAAG;oBAC7D;oBACA,aAAa,SAAU,IAAS;wBAC9B,yBAAyB;wBACzB,MAAM,aAAa,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM;wBAC/C,IAAI,WAAW,CAAC;wBAChB,IAAI,YAAY,CAAC,KAAK,KAAK;wBAC3B,IAAI,IAAI,CACN,AAAC,4BAA8D,OAAnC,IAAI,OAAO,cAAc,CAAC,WACtD,KACA,aAAa,IACb;4BAAE,OAAO;wBAAS;wBAEpB,IAAI,IAAI,CACN,AAAC,QAAuB,OAAhB,KAAK,UAAU,GACvB,KACA,aAAa,IACb;4BAAE,OAAO;wBAAQ;oBAErB;gBACF;YACF;YAEA,YAAY;YACZ,MAAM,WAAW,AAAC,GAAsB,OAApB,WAAW,KAAK,EAAC,KAA0C,OAAvC,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,EAAC;YAC/E,IAAI,IAAI,CAAC;QAEX,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,uBAAuB;YACvB,MAAM;YACN;QACF;IACF;IAEA,cAAc;IACd,MAAM,sBAAsB,CAAC;QAC3B,MAAM,eAA0C;YAC9C,gBAAgB;YAChB,UAAU;YACV,UAAU;YACV,WAAW;YACX,mBAAmB;YACnB,kBAAkB;YAClB,kBAAkB;YAClB,cAAc;YACd,eAAe;YACf,UAAU;YACV,mBAAmB;YACnB,cAAc;YACd,cAAc;YACd,UAAU;YACV,WAAW;YACX,iBAAiB;YACjB,gBAAgB;YAChB,SAAS;YACT,UAAU;YACV,SAAS;YACT,SAAS;YACT,UAAU;YACV,cAAc;YACd,kBAAkB;YAClB,kBAAkB;YAClB,kBAAkB;YAClB,WAAW;YACX,kBAAkB;YAClB,mBAAmB;YACnB,gBAAgB;YAChB,oBAAoB;YACpB,aAAa;YACb,oCAAoC;YACpC,kBAAkB;YAClB,iBAAiB;YACjB,iBAAiB;YACjB,cAAc;YACd,gBAAgB;YAChB,eAAe;YACf,kBAAkB;YAClB,kBAAkB;YAClB,QAAQ;YACR,SAAS;YACT,WAAW;YACX,YAAY;YACZ,cAAc;YACd,eAAe;QACjB;QACA,OAAO,YAAY,CAAC,WAAW,IAAI;IACrC;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,UAAU,QAAQ,UAAU,WAAW,OAAO;QAClD,IAAI,OAAO,UAAU,UAAU,OAAO,MAAM,cAAc;QAC1D,IAAI,OAAO,UAAU,YAAY,MAAM,QAAQ,CAAC,MAAM;YACpD,QAAQ;YACR,IAAI;gBACF,OAAO,IAAI,KAAK,OAAO,kBAAkB,CAAC;YAC5C,EAAE,UAAM;gBACN,OAAO;YACT;QACF;QACA,OAAO,OAAO;IAChB;IAEA,qBACE,6LAAC,kIAAA,CAAA,UAAS;kBACR,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC,qNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAA0B;;;;;;;kDAGjD,6LAAC;wCAAE,WAAU;kDAAqB;;;;;;;;;;;;0CAEpC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,eAAe,CAAC;wCAC/B,WAAW,AAAC,yEAIX,OAHC,cACI,6CACA;;0DAGN,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAGhC,6LAAC;wCACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;wCACrC,WAAU;;0DAEV,6LAAC,mNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;;;;;;;;;;;;gBAQxC,6BACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAGlC,6LAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAIjB,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,6LAAC;4CACC,OAAO,QAAQ,SAAS,CAAC,MAAM;4CAC/B,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;4CAChD,WAAU;sDAET,aAAa,GAAG,CAAC,CAAA,uBAChB,6LAAC;oDAAuB,OAAO,OAAO,EAAE;8DAAG,OAAO,KAAK;mDAA1C,OAAO,EAAE;;;;;;;;;;;;;;;;8CAM5B,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,6LAAC;4CACC,OAAO,QAAQ,aAAa,IAAI;4CAChC,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,eAAe,EAAE,MAAM,CAAC,KAAK;oDAAQ,CAAC;4CACtF,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,6LAAC;oDAAO,OAAM;8DAAO;;;;;;8DACrB,6LAAC;oDAAO,OAAM;8DAAU;;;;;;8DACxB,6LAAC;oDAAO,OAAM;8DAAU;;;;;;;;;;;;;;;;;;8CAK5B,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,6LAAC;4CACC,OAAO,QAAQ,aAAa,IAAI;4CAChC,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,eAAe,EAAE,MAAM,CAAC,KAAK;oDAAQ,CAAC;4CACtF,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,6LAAC;oDAAO,OAAM;8DAAO;;;;;;8DACrB,6LAAC;oDAAO,OAAM;8DAAS;;;;;;;;;;;;;;;;;;8CAK3B,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,6LAAC;4CACC,OAAO,QAAQ,QAAQ,IAAI;4CAC3B,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,UAAU,EAAE,MAAM,CAAC,KAAK,IAAI;oDAAU,CAAC;4CACvF,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAG;;;;;;gDAChB,UAAU,GAAG,CAAC,CAAA,yBACb,6LAAC;wDAAyB,OAAO,SAAS,EAAE;kEAAG,SAAS,IAAI;uDAA/C,SAAS,EAAE;;;;;;;;;;;;;;;;;8CAM9B,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,6LAAC;4CACC,OAAO,QAAQ,QAAQ,IAAI;4CAC3B,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,UAAU,EAAE,MAAM,CAAC,KAAK,IAAI;oDAAU,CAAC;4CACvF,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAG;;;;;;gDAChB,UAAU,GAAG,CAAC,CAAA,yBACb,6LAAC;wDAAyB,OAAO,SAAS,EAAE;kEAAG,SAAS,IAAI;uDAA/C,SAAS,EAAE;;;;;;;;;;;;;;;;;8CAM9B,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,6LAAC;4CAAM,WAAU;;8DACf,6LAAC;oDACC,MAAK;oDACL,SAAS,QAAQ,cAAc,IAAI;oDACnC,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,gBAAgB,EAAE,MAAM,CAAC,OAAO;4DAAC,CAAC;oDAClF,WAAU;;;;;;8DAEZ,6LAAC;oDAAK,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;;;;;;;wBAMlD,QAAQ,SAAS,CAAC,MAAM,KAAK,0BAC5B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,6LAAC;4CACC,MAAK;4CACL,OAAO,QAAQ,SAAS,CAAC,KAAK;4CAC9B,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;wDACnC,GAAG,IAAI;wDACP,WAAW;4DAAE,GAAG,KAAK,SAAS;4DAAE,OAAO,EAAE,MAAM,CAAC,KAAK;wDAAC;oDACxD,CAAC;4CACD,WAAU;;;;;;;;;;;;8CAGd,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,6LAAC;4CACC,MAAK;4CACL,OAAO,QAAQ,SAAS,CAAC,GAAG;4CAC5B,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;wDACnC,GAAG,IAAI;wDACP,WAAW;4DAAE,GAAG,KAAK,SAAS;4DAAE,KAAK,EAAE,MAAM,CAAC,KAAK;wDAAC;oDACtD,CAAC;4CACD,WAAU;;;;;;;;;;;;;;;;;;;;;;;;8BAStB,6LAAC;oBAAI,WAAU;8BACZ,kBAAkB,GAAG,CAAC,CAAC;wBACtB,MAAM,OAAO,OAAO,IAAI;wBACxB,qBACE,6LAAC;4BAEC,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAW,AAAC,kBAA8B,OAAb,OAAO,KAAK,EAAC;0DAC7C,cAAA,6LAAC;oDAAK,WAAU;;;;;;;;;;;0DAElB,6LAAC;gDACC,SAAS,IAAM,eAAe,OAAO,EAAE;gDACvC,UAAU;gDACV,WAAU;;oDAET,WAAW,mBAAmB,OAAO,EAAE,iBACtC,6LAAC,mNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;6EAErB,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDACf;;;;;;;;;;;;;kDAIN,6LAAC;wCAAG,WAAU;kDAA4C,OAAO,KAAK;;;;;;kDACtE,6LAAC;wCAAE,WAAU;kDAAyB,OAAO,WAAW;;;;;;;;;;;;2BAtBrD,OAAO,EAAE;;;;;oBA0BpB;;;;;;gBAID,mBAAmB,4BAClB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAuC,WAAW,KAAK;;;;;;kDACrE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS,IAAM;gDACf,WAAU;;kEAEV,6LAAC,2MAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAGjC,6LAAC;gDACC,SAAS,IAAM;gDACf,WAAU;;kEAEV,6LAAC,+NAAA,CAAA,kBAAe;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAGzC,6LAAC;gDACC,SAAS,IAAM;gDACf,WAAU;;kEAEV,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAGlC,6LAAC;gDACC,SAAS,IAAM,mBAAmB;gDAClC,WAAU;0DAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0CAKnB,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAoC;;;;;;8EACjD,6LAAC;oEAAE,WAAU;8EAAoC,WAAW,OAAO,CAAC,YAAY,CAAC,cAAc;;;;;;;;;;;;sEAEjG,6LAAC,iNAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAGxB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAqC;;;;;;8EAClD,6LAAC;oEAAE,WAAU;;wEAAqC,KAAK,KAAK,CAAC,WAAW,OAAO,CAAC,WAAW,EAAE,cAAc;wEAAG;;;;;;;;;;;;;sEAEhH,6LAAC,qNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAG1B,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAsC;;;;;;8EACnD,6LAAC;oEAAE,WAAU;;wEAAsC,KAAK,KAAK,CAAC,WAAW,OAAO,CAAC,aAAa,EAAE,cAAc;wEAAG;;;;;;;;;;;;;sEAEnH,6LAAC,qNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAGzB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAsC;;;;;;8EACnD,6LAAC;oEAAE,WAAU;;wEACV,QAAQ,SAAS,CAAC,KAAK;wEAAC;wEAAM,QAAQ,SAAS,CAAC,GAAG;;;;;;;;;;;;;sEAGxD,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;;;;;;;4CAKvB,WAAW,OAAO,CAAC,UAAU,KAAK,2BACjC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAuC;;;;;;8EACpD,6LAAC;oEAAE,WAAU;;wEAAuC,KAAK,KAAK,CAAC,WAAW,OAAO,CAAC,UAAU,EAAE,cAAc;wEAAG;;;;;;;;;;;;;sEAEjH,6LAAC,8NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;;;;;;;4CAK5B,WAAW,OAAO,CAAC,aAAa,KAAK,2BACpC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAmC;;;;;;8EAChD,6LAAC;oEAAE,WAAU;;wEAAmC,KAAK,KAAK,CAAC,WAAW,OAAO,CAAC,aAAa,EAAE,cAAc;wEAAG;;;;;;;;;;;;;sEAEhH,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;;;;;;;;;;;;4CAKtB,WAAW,OAAO,CAAC,SAAS,KAAK,2BAChC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAoC;;;;;;8EACjD,6LAAC;oEAAE,WAAU;;wEAAoC,KAAK,KAAK,CAAC,WAAW,OAAO,CAAC,SAAS,EAAE,cAAc;wEAAG;;;;;;;;;;;;;sEAE7G,6LAAC,qNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;;;;;;4CAK3B,WAAW,OAAO,CAAC,YAAY,KAAK,2BACnC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAsC;;;;;;8EACnD,6LAAC;oEAAE,WAAU;;wEAAsC,KAAK,KAAK,CAAC,WAAW,OAAO,CAAC,YAAY,GAAG,OAAO;wEAAI;;;;;;;;;;;;;sEAE7G,6LAAC,yMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;oCAOzB,WAAW,IAAI,CAAC,MAAM,GAAG,mBACxB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAsC;;;;;;kEACpD,6LAAC;wDAAE,WAAU;;4DAA6B;4DAAc,WAAW,IAAI,CAAC,MAAM;;;;;;;;;;;;;0DAEhF,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAM,WAAU;;sEACf,6LAAC;4DAAM,WAAU;sEACf,cAAA,6LAAC;0EACE,OAAO,IAAI,CAAC,WAAW,IAAI,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,oBAC1C,6LAAC;wEAAa,WAAU;kFACrB,oBAAoB;uEADd;;;;;;;;;;;;;;;sEAMf,6LAAC;4DAAM,WAAU;sEACd,WAAW,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,sBACzB,6LAAC;oEAAe,WAAU;8EACvB,OAAO,OAAO,CAAC,KAAK,GAAG,CAAC,QAA8B;4EAA7B,CAAC,KAAK,MAAqB;6FACnD,6LAAC;4EAEC,WAAW,AAAC,mCAEX,OADC,QAAQ,YAAY,QAAQ,oBAAoB,aAAa;sFAG9D,QAAQ,YAAY,QAAQ,kCAC3B,6LAAC;gFAAI,WAAU;0FACZ,gBAAgB;;;;;uFAGnB,gBAAgB;2EAVb;;;;;;mEAHF;;;;;;;;;;;;;;;;;;;;;;;;;;;oCA0BpB,WAAW,MAAM,IAAI,WAAW,MAAM,CAAC,MAAM,GAAG,mBAC/C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,6LAAC;gDAAI,WAAU;0DACZ,WAAW,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC7B,6LAAC;wDAAgB,WAAU;;0EACzB,6LAAC;gEAAG,WAAU;0EACX,MAAM,IAAI,KAAK,QAAQ,oBACvB,MAAM,IAAI,KAAK,QAAQ,oBACvB;;;;;;0EAEH,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,iNAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;kFACpB,6LAAC;wEAAK,WAAU;kFAAO;;;;;;;;;;;;;uDARjB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsBlC;GAljDwB;KAAA", "debugId": null}}]}