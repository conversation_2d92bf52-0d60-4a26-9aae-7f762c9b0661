# 🖨️ تطبيق قالب لارين في جميع أنواع الطباعة

## ✅ تم تطبيق قالب لارين بنجاح في النظام

تم **تحديث النظام بالكامل** لاستخدام **قالب لارين الاحترافي** في جميع أنواع الطباعة، مما يضمن **تصميماً موحداً وجميلاً** لجميع المستندات.

---

## 📋 **أنواع المستندات التي تستخدم قالب لارين:**

### **🧾 1. فواتير المبيعات**
- **الصفحة الرئيسية:** `/sales`
- **سجل المبيعات:** `/sales-history`
- **سجلات المبيعات:** `/sales-records`

**الاستخدام:**
```typescript
import { printInvoice } from '@/hooks/usePrintSettings'

// طباعة فاتورة مبيعات
printInvoice(invoice, 'sales', printSettings)
```

### **🛒 2. فواتير المشتريات**
- **الصفحة الرئيسية:** `/purchases`
- **سجل المشتريات:** `/purchases-history`
- **سجلات المشتريات:** `/purchases-records`

**الاستخدام:**
```typescript
import { printInvoice } from '@/hooks/usePrintSettings'

// طباعة فاتورة مشتريات
printInvoice(invoice, 'purchase', printSettings)
```

### **↩️ 3. مرتجعات المبيعات والمشتريات**
- **صفحة المرتجعات:** `/returns-records`

**الاستخدام:**
```typescript
import { generateLarenReturnHTML } from '@/utils/larenPrintTemplate'

// طباعة مرتجع
const printContent = generateLarenReturnHTML(returnRecord, settings)
```

### **📊 4. التقارير المختلفة**
- **صفحة التقارير:** `/reports`
- **تقارير المبيعات**
- **تقارير المشتريات**
- **تقارير المخزون**
- **التقارير المالية**

**الاستخدام:**
```typescript
import { printReport } from '@/hooks/usePrintSettings'

// طباعة تقرير
printReport(reportData, reportType, title, settings)
```

---

## 🎨 **مميزات قالب لارين:**

### **📄 1. رأس الصفحة الاحترافي**
- **اسم الشركة:** مكتب لارين العلمي
- **الاسم الإنجليزي:** LAREN SCIENTIFIC BUREAU
- **العنوان:** بغداد - شارع فلسطين
- **الهاتف:** +964 ************
- **البريد الإلكتروني:** <EMAIL>

### **🏷️ 2. شعار مميز**
- **تصميم دائري احترافي**
- **مكان مخصص للشعار**
- **ألوان متناسقة**

### **📋 3. معلومات شاملة**
- **تفاصيل العملاء/الموردين**
- **معلومات الفاتورة**
- **التاريخ والوقت**
- **رقم الفاتورة**

### **📊 4. جداول منظمة**
- **أعمدة واضحة ومحددة**
- **ترقيم تسلسلي للمواد**
- **معلومات الانتهاء ورقم الدفعة**
- **ألوان تمييزية**

### **💰 5. ملخص المبالغ**
- **المجموع الفرعي**
- **الخصومات**
- **المجموع النهائي**
- **حالة الدفع**

### **✍️ 6. قسم التوقيع**
- **مكان مخصص لختم وتوقيع المسؤول**
- **تصميم دائري احترافي**
- **نص تذييل قابل للتخصيص**

---

## ⚙️ **إعدادات الطباعة:**

### **🏢 معلومات الشركة**
```typescript
const defaultSettings = {
  companyName: 'مكتب لارين العلمي',
  companyNameEn: 'LAREN SCIENTIFIC BUREAU',
  companyAddress: 'بغداد - شارع فلسطين',
  companyPhone: '+964 ************',
  companyEmail: '<EMAIL>',
  footerText: 'شكراً لتعاملكم معنا'
}
```

### **🎨 إعدادات التصميم**
```typescript
const designSettings = {
  showLogo: true,
  showHeader: true,
  showFooter: true,
  fontSize: 'medium',
  paperSize: 'A4',
  showBorders: true,
  showColors: false,
  headerColor: '#1f2937',
  accentColor: '#3b82f6',
  textColor: '#374151',
  backgroundColor: '#ffffff'
}
```

---

## 🔧 **الملفات الرئيسية:**

### **📁 قالب لارين**
- **الملف:** `src/utils/larenPrintTemplate.ts`
- **الوظائف:**
  - `generateLarenInvoiceHTML()` - فواتير المبيعات والمشتريات
  - `generateLarenReportHTML()` - التقارير المختلفة
  - `generateLarenReturnHTML()` - المرتجعات

### **⚙️ إعدادات الطباعة**
- **الملف:** `src/hooks/usePrintSettings.ts`
- **الوظائف:**
  - `printInvoice()` - طباعة الفواتير
  - `printReport()` - طباعة التقارير
  - `usePrintSettings()` - إدارة الإعدادات

---

## 🚀 **كيفية الاستخدام:**

### **1. طباعة فاتورة مبيعات**
```typescript
import { printInvoice } from '@/hooks/usePrintSettings'

const handlePrint = () => {
  printInvoice(invoiceData, 'sales', printSettings)
}
```

### **2. طباعة فاتورة مشتريات**
```typescript
import { printInvoice } from '@/hooks/usePrintSettings'

const handlePrint = () => {
  printInvoice(invoiceData, 'purchase', printSettings)
}
```

### **3. طباعة تقرير**
```typescript
import { printReport } from '@/hooks/usePrintSettings'

const handlePrint = () => {
  printReport(reportData, 'sales_summary', 'تقرير المبيعات', printSettings)
}
```

### **4. طباعة مرتجع**
```typescript
import { generateLarenReturnHTML } from '@/utils/larenPrintTemplate'

const handlePrint = () => {
  const printWindow = window.open('', '_blank')
  const printContent = generateLarenReturnHTML(returnRecord, settings)
  printWindow.document.write(printContent)
  printWindow.print()
}
```

---

## ✅ **النتائج المحققة:**

### **🎯 تصميم موحد**
- جميع المستندات تستخدم نفس القالب
- هوية بصرية متسقة
- مظهر احترافي موحد

### **📱 سهولة الاستخدام**
- واجهة بسيطة وواضحة
- طباعة بنقرة واحدة
- معاينة قبل الطباعة

### **🔧 قابلية التخصيص**
- إعدادات قابلة للتعديل
- ألوان وخطوط متنوعة
- أحجام ورق مختلفة

### **🌟 جودة عالية**
- تصميم احترافي
- طباعة واضحة ومقروءة
- تفاصيل شاملة ومنظمة

---

## 🎉 **الخلاصة:**

تم **تطبيق قالب لارين بنجاح** في جميع أنواع الطباعة في النظام:
- ✅ **فواتير المبيعات** - تستخدم قالب لارين
- ✅ **فواتير المشتريات** - تستخدم قالب لارين  
- ✅ **المرتجعات** - تستخدم قالب لارين
- ✅ **التقارير** - تستخدم قالب لارين

**النظام الآن يطبع جميع المستندات بتصميم موحد واحترافي يليق بمكتب لارين العلمي!** 🎊✨
