'use client'

import React, { useState } from 'react'
import { InvoicePrint } from './PrintTemplate'
import { <PERSON>er, Eye } from 'lucide-react'

// مثال على بيانات فاتورة للاختبار - مشابه للفاتورة المرفقة
const sampleInvoice = {
  id: 1,
  invoice_number: '1723',
  created_at: '2025-01-16',
  final_amount: 770000,
  paid_amount: 770000,
  payment_method: 'cash',
  payment_status: 'paid',
  notes: 'يرجى حفظ الدواء في مكان بارد وجاف بعيداً عن متناول الأطفال',
  customer_name: 'أحمد محمد علي',
  customers: {
    name: 'أحمد محمد علي',
    phone: '+964 ************',
    address: 'بغداد - الكرادة'
  },
  sales_invoice_items: [
    {
      id: 1,
      quantity: 10,
      unit_price: 7700,
      total_price: 77000,
      medicine_batches: {
        batch_number: '770000',
        expiry_date: '2025-12-31',
        medicines: {
          name: 'Dextamine 5mg',
          category: 'مضادات الهيستامين'
        }
      }
    },
    {
      id: 2,
      quantity: 100,
      unit_price: 6930,
      total_price: 693000,
      medicine_batches: {
        batch_number: '770000',
        expiry_date: '2026-06-30',
        medicines: {
          name: 'Amoxicillin 500mg',
          category: 'مضادات حيوية'
        }
      }
    }
  ]
}

export default function ClassicInvoiceExample() {
  const [showPreview, setShowPreview] = useState(false)

  const handlePrint = () => {
    window.print()
  }

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            مثال على التصميم الكلاسيكي للفاتورة
          </h1>
          
          <p className="text-gray-600 mb-6">
            هذا مثال على تصميم الفاتورة الكلاسيكي المشابه للفاتورة التي قدمتها. 
            التصميم يحاكي النمط التقليدي للفواتير العربية مع الحدود السوداء والتخطيط الكلاسيكي.
          </p>

          <div className="flex gap-4">
            <button
              onClick={() => setShowPreview(true)}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              <Eye className="h-4 w-4" />
              معاينة الفاتورة
            </button>
            
            <button
              onClick={handlePrint}
              className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
            >
              <Printer className="h-4 w-4" />
              طباعة مباشرة
            </button>
          </div>
        </div>

        {/* معاينة الفاتورة */}
        {showPreview && (
          <div className="bg-white rounded-lg shadow-lg p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-bold">معاينة الفاتورة</h2>
              <button
                onClick={() => setShowPreview(false)}
                className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
              >
                إغلاق
              </button>
            </div>
            
            <div className="border border-gray-300 rounded-lg overflow-hidden">
              <InvoicePrint 
                invoice={sampleInvoice} 
                type="sales"
                settings={{
                  companyName: 'صيدلية الشفاء',
                  companyAddress: 'بغداد - شارع فلسطين',
                  companyPhone: '+964 ************'
                }}
              />
            </div>
          </div>
        )}

        {/* الفاتورة للطباعة المباشرة */}
        <div className="print-only">
          <InvoicePrint 
            invoice={sampleInvoice} 
            type="sales"
            settings={{
              companyName: 'صيدلية الشفاء',
              companyAddress: 'بغداد - شارع فلسطين',
              companyPhone: '+964 ************'
            }}
          />
        </div>
      </div>

      <style jsx>{`
        @media screen {
          .print-only {
            display: none;
          }
        }
        
        @media print {
          .print-only {
            display: block;
          }
          
          .no-print {
            display: none !important;
          }
        }
      `}</style>
    </div>
  )
}
