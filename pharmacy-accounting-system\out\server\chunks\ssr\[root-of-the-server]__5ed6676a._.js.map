{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%287%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useEffect, useState } from 'react'\n\n// أنواع البيانات المؤقتة\ninterface User {\n  id: string\n  username: string\n  email: string\n  full_name: string\n  role: string\n  permissions: UserPermissions\n  is_active: boolean\n  last_login: string | null\n  created_at: string\n}\n\ninterface UserPermissions {\n  sales_view: boolean\n  sales_create: boolean\n  sales_edit: boolean\n  sales_delete: boolean\n  sales_print: boolean\n  sales_view_prices: boolean\n  purchases_view: boolean\n  purchases_create: boolean\n  purchases_edit: boolean\n  purchases_delete: boolean\n  purchases_print: boolean\n  inventory_view: boolean\n  inventory_create: boolean\n  inventory_edit: boolean\n  inventory_delete: boolean\n  inventory_print: boolean\n  customers_view: boolean\n  customers_create: boolean\n  customers_edit: boolean\n  customers_delete: boolean\n  suppliers_view: boolean\n  suppliers_create: boolean\n  suppliers_edit: boolean\n  suppliers_delete: boolean\n  reports_view: boolean\n  reports_financial: boolean\n  reports_detailed: boolean\n  reports_export: boolean\n  users_view: boolean\n  users_create: boolean\n  users_edit: boolean\n  users_delete: boolean\n  settings_view: boolean\n  settings_edit: boolean\n  cashbox_view: boolean\n  cashbox_manage: boolean\n  returns_view: boolean\n  returns_create: boolean\n  returns_edit: boolean\n  returns_delete: boolean\n}\n\n// دوال مؤقتة للاختبار\nconst authenticateUser = async (username: string, password: string, rememberMe: boolean = false) => {\n  // محاكاة تسجيل دخول ناجح\n  if (username === 'admin' && password === 'admin123') {\n    const mockUser: User = {\n      id: '1',\n      username: 'admin',\n      email: '<EMAIL>',\n      full_name: 'مدير النظام',\n      role: 'admin',\n      permissions: {\n        sales_view: true,\n        sales_create: true,\n        sales_edit: true,\n        sales_delete: true,\n        sales_print: true,\n        sales_view_prices: true,\n        purchases_view: true,\n        purchases_create: true,\n        purchases_edit: true,\n        purchases_delete: true,\n        purchases_print: true,\n        inventory_view: true,\n        inventory_create: true,\n        inventory_edit: true,\n        inventory_delete: true,\n        inventory_print: true,\n        customers_view: true,\n        customers_create: true,\n        customers_edit: true,\n        customers_delete: true,\n        suppliers_view: true,\n        suppliers_create: true,\n        suppliers_edit: true,\n        suppliers_delete: true,\n        reports_view: true,\n        reports_financial: true,\n        reports_detailed: true,\n        reports_export: true,\n        users_view: true,\n        users_create: true,\n        users_edit: true,\n        users_delete: true,\n        settings_view: true,\n        settings_edit: true,\n        cashbox_view: true,\n        cashbox_manage: true,\n        returns_view: true,\n        returns_create: true,\n        returns_edit: true,\n        returns_delete: true,\n      },\n      is_active: true,\n      last_login: new Date().toISOString(),\n      created_at: '2024-01-01T00:00:00Z'\n    }\n\n    return {\n      success: true,\n      user: mockUser,\n      session: { token: 'mock-session-token' }\n    }\n  }\n\n  return {\n    success: false,\n    error: 'اسم المستخدم أو كلمة المرور غير صحيحة'\n  }\n}\n\nconst logoutUser = async (token: string) => {\n  return { success: true }\n}\n\nconst validateSession = async (token: string) => {\n  if (token === 'mock-session-token') {\n    return {\n      success: true,\n      user: {\n        id: '1',\n        username: 'admin',\n        email: '<EMAIL>',\n        full_name: 'مدير النظام',\n        role: 'admin',\n        permissions: {\n          sales_view: true,\n          sales_create: true,\n          sales_edit: true,\n          sales_delete: true,\n          sales_print: true,\n          sales_view_prices: true,\n          purchases_view: true,\n          purchases_create: true,\n          purchases_edit: true,\n          purchases_delete: true,\n          purchases_print: true,\n          inventory_view: true,\n          inventory_create: true,\n          inventory_edit: true,\n          inventory_delete: true,\n          inventory_print: true,\n          customers_view: true,\n          customers_create: true,\n          customers_edit: true,\n          customers_delete: true,\n          suppliers_view: true,\n          suppliers_create: true,\n          suppliers_edit: true,\n          suppliers_delete: true,\n          reports_view: true,\n          reports_financial: true,\n          reports_detailed: true,\n          reports_export: true,\n          users_view: true,\n          users_create: true,\n          users_edit: true,\n          users_delete: true,\n          settings_view: true,\n          settings_edit: true,\n          cashbox_view: true,\n          cashbox_manage: true,\n          returns_view: true,\n          returns_create: true,\n          returns_edit: true,\n          returns_delete: true,\n        },\n        is_active: true\n      }\n    }\n  }\n\n  return { success: false, error: 'جلسة غير صالحة' }\n}\n\nconst logActivity = async (data: any) => {\n  console.log('Activity logged:', data)\n  return { success: true }\n}\n\ninterface AuthContextType {\n  user: User | null\n  permissions: UserPermissions | null\n  isLoading: boolean\n  isAuthenticated: boolean\n  login: (username: string, password: string, rememberMe?: boolean) => Promise<{ success: boolean; error?: string }>\n  logout: () => Promise<void>\n  hasPermission: (permission: keyof UserPermissions) => boolean\n  hasAnyPermission: (permissions: (keyof UserPermissions)[]) => boolean\n  hasRole: (role: string) => boolean\n  refreshUser: () => Promise<void>\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n\ninterface AuthProviderProps {\n  children: React.ReactNode\n}\n\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\n  const [user, setUser] = useState<User | null>(null)\n  const [permissions, setPermissions] = useState<UserPermissions | null>(null)\n  const [isLoading, setIsLoading] = useState(true)\n\n  // التحقق من الجلسة عند تحميل التطبيق\n  useEffect(() => {\n    checkSession()\n  }, [])\n\n  const checkSession = async () => {\n    try {\n      const sessionToken = localStorage.getItem('sessionToken')\n      if (!sessionToken) {\n        setIsLoading(false)\n        return\n      }\n\n      const result = await validateSession(sessionToken)\n      if (result.success && result.user) {\n        setUser(result.user as User)\n        setPermissions(result.user.permissions as UserPermissions)\n      } else {\n        localStorage.removeItem('sessionToken')\n      }\n    } catch (error) {\n      console.error('Error checking session:', error)\n      localStorage.removeItem('sessionToken')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const login = async (username: string, password: string, rememberMe: boolean = false) => {\n    try {\n      setIsLoading(true)\n      const result = await authenticateUser(username, password, rememberMe)\n      \n      if (result.success && result.user && result.session) {\n        setUser(result.user as User)\n        setPermissions(result.user.permissions as UserPermissions)\n        localStorage.setItem('sessionToken', result.session.token)\n        \n        return { success: true }\n      } else {\n        return { success: false, error: result.error || 'فشل في تسجيل الدخول' }\n      }\n    } catch (error: any) {\n      return { success: false, error: error.message || 'حدث خطأ غير متوقع' }\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const logout = async () => {\n    try {\n      const sessionToken = localStorage.getItem('sessionToken')\n      if (sessionToken) {\n        await logoutUser(sessionToken)\n      }\n      \n      setUser(null)\n      setPermissions(null)\n      localStorage.removeItem('sessionToken')\n    } catch (error) {\n      console.error('Error during logout:', error)\n      // حتى لو فشل في الخادم، نقوم بتنظيف البيانات المحلية\n      setUser(null)\n      setPermissions(null)\n      localStorage.removeItem('sessionToken')\n    }\n  }\n\n  const hasPermission = (permission: keyof UserPermissions): boolean => {\n    if (!permissions) return false\n    return permissions[permission] === true\n  }\n\n  const hasAnyPermission = (permissionsList: (keyof UserPermissions)[]): boolean => {\n    if (!permissions) return false\n    return permissionsList.some(permission => permissions[permission] === true)\n  }\n\n  const hasRole = (role: string): boolean => {\n    if (!user) return false\n    return user.role === role\n  }\n\n  const refreshUser = async () => {\n    await checkSession()\n  }\n\n  const value: AuthContextType = {\n    user,\n    permissions,\n    isLoading,\n    isAuthenticated: !!user,\n    login,\n    logout,\n    hasPermission,\n    hasAnyPermission,\n    hasRole,\n    refreshUser\n  }\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  )\n}\n\n// Hook للتحقق من الصلاحيات\nexport const usePermissions = () => {\n  const { permissions, hasPermission, hasAnyPermission } = useAuth()\n  \n  return {\n    permissions,\n    hasPermission,\n    hasAnyPermission,\n    \n    // صلاحيات المبيعات\n    canViewSales: hasPermission('sales_view'),\n    canCreateSales: hasPermission('sales_create'),\n    canEditSales: hasPermission('sales_edit'),\n    canDeleteSales: hasPermission('sales_delete'),\n    canPrintSales: hasPermission('sales_print'),\n    canViewPrices: hasPermission('sales_view_prices'),\n    \n    // صلاحيات المشتريات\n    canViewPurchases: hasPermission('purchases_view'),\n    canCreatePurchases: hasPermission('purchases_create'),\n    canEditPurchases: hasPermission('purchases_edit'),\n    canDeletePurchases: hasPermission('purchases_delete'),\n    canPrintPurchases: hasPermission('purchases_print'),\n    \n    // صلاحيات المخزون\n    canViewInventory: hasPermission('inventory_view'),\n    canCreateInventory: hasPermission('inventory_create'),\n    canEditInventory: hasPermission('inventory_edit'),\n    canDeleteInventory: hasPermission('inventory_delete'),\n    canPrintInventory: hasPermission('inventory_print'),\n    \n    // صلاحيات العملاء\n    canViewCustomers: hasPermission('customers_view'),\n    canCreateCustomers: hasPermission('customers_create'),\n    canEditCustomers: hasPermission('customers_edit'),\n    canDeleteCustomers: hasPermission('customers_delete'),\n    \n    // صلاحيات الموردين\n    canViewSuppliers: hasPermission('suppliers_view'),\n    canCreateSuppliers: hasPermission('suppliers_create'),\n    canEditSuppliers: hasPermission('suppliers_edit'),\n    canDeleteSuppliers: hasPermission('suppliers_delete'),\n    \n    // صلاحيات التقارير\n    canViewReports: hasPermission('reports_view'),\n    canViewFinancialReports: hasPermission('reports_financial'),\n    canViewDetailedReports: hasPermission('reports_detailed'),\n    canExportReports: hasPermission('reports_export'),\n    \n    // صلاحيات المستخدمين\n    canViewUsers: hasPermission('users_view'),\n    canCreateUsers: hasPermission('users_create'),\n    canEditUsers: hasPermission('users_edit'),\n    canDeleteUsers: hasPermission('users_delete'),\n    \n    // صلاحيات الإعدادات\n    canViewSettings: hasPermission('settings_view'),\n    canEditSettings: hasPermission('settings_edit'),\n    \n    // صلاحيات الصندوق\n    canViewCashbox: hasPermission('cashbox_view'),\n    canManageCashbox: hasPermission('cashbox_manage'),\n    \n    // صلاحيات المرتجعات\n    canViewReturns: hasPermission('returns_view'),\n    canCreateReturns: hasPermission('returns_create'),\n    canEditReturns: hasPermission('returns_edit'),\n    canDeleteReturns: hasPermission('returns_delete'),\n    \n    // دوال مساعدة\n    canAccessSalesModule: hasAnyPermission(['sales_view', 'sales_create', 'sales_edit']),\n    canAccessPurchasesModule: hasAnyPermission(['purchases_view', 'purchases_create', 'purchases_edit']),\n    canAccessInventoryModule: hasAnyPermission(['inventory_view', 'inventory_create', 'inventory_edit']),\n    canAccessReportsModule: hasPermission('reports_view'),\n    canAccessUsersModule: hasAnyPermission(['users_view', 'users_create', 'users_edit']),\n    canAccessSettingsModule: hasAnyPermission(['settings_view', 'settings_edit']),\n  }\n}\n\n// Hook لتسجيل النشاطات\nexport const useActivityLogger = () => {\n  const { user } = useAuth()\n  \n  const logUserActivity = async (action: string, description: string, additionalData?: {\n    table_name?: string\n    record_id?: string\n    old_values?: any\n    new_values?: any\n  }) => {\n    if (!user) return\n    \n    try {\n      await logActivity({\n        user_id: user.id,\n        action,\n        description,\n        ...additionalData\n      })\n    } catch (error) {\n      console.error('Error logging activity:', error)\n    }\n  }\n  \n  return { logUserActivity }\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AAFA;;;AA4DA,sBAAsB;AACtB,MAAM,mBAAmB,OAAO,UAAkB,UAAkB,aAAsB,KAAK;IAC7F,yBAAyB;IACzB,IAAI,aAAa,WAAW,aAAa,YAAY;QACnD,MAAM,WAAiB;YACrB,IAAI;YACJ,UAAU;YACV,OAAO;YACP,WAAW;YACX,MAAM;YACN,aAAa;gBACX,YAAY;gBACZ,cAAc;gBACd,YAAY;gBACZ,cAAc;gBACd,aAAa;gBACb,mBAAmB;gBACnB,gBAAgB;gBAChB,kBAAkB;gBAClB,gBAAgB;gBAChB,kBAAkB;gBAClB,iBAAiB;gBACjB,gBAAgB;gBAChB,kBAAkB;gBAClB,gBAAgB;gBAChB,kBAAkB;gBAClB,iBAAiB;gBACjB,gBAAgB;gBAChB,kBAAkB;gBAClB,gBAAgB;gBAChB,kBAAkB;gBAClB,gBAAgB;gBAChB,kBAAkB;gBAClB,gBAAgB;gBAChB,kBAAkB;gBAClB,cAAc;gBACd,mBAAmB;gBACnB,kBAAkB;gBAClB,gBAAgB;gBAChB,YAAY;gBACZ,cAAc;gBACd,YAAY;gBACZ,cAAc;gBACd,eAAe;gBACf,eAAe;gBACf,cAAc;gBACd,gBAAgB;gBAChB,cAAc;gBACd,gBAAgB;gBAChB,cAAc;gBACd,gBAAgB;YAClB;YACA,WAAW;YACX,YAAY,IAAI,OAAO,WAAW;YAClC,YAAY;QACd;QAEA,OAAO;YACL,SAAS;YACT,MAAM;YACN,SAAS;gBAAE,OAAO;YAAqB;QACzC;IACF;IAEA,OAAO;QACL,SAAS;QACT,OAAO;IACT;AACF;AAEA,MAAM,aAAa,OAAO;IACxB,OAAO;QAAE,SAAS;IAAK;AACzB;AAEA,MAAM,kBAAkB,OAAO;IAC7B,IAAI,UAAU,sBAAsB;QAClC,OAAO;YACL,SAAS;YACT,MAAM;gBACJ,IAAI;gBACJ,UAAU;gBACV,OAAO;gBACP,WAAW;gBACX,MAAM;gBACN,aAAa;oBACX,YAAY;oBACZ,cAAc;oBACd,YAAY;oBACZ,cAAc;oBACd,aAAa;oBACb,mBAAmB;oBACnB,gBAAgB;oBAChB,kBAAkB;oBAClB,gBAAgB;oBAChB,kBAAkB;oBAClB,iBAAiB;oBACjB,gBAAgB;oBAChB,kBAAkB;oBAClB,gBAAgB;oBAChB,kBAAkB;oBAClB,iBAAiB;oBACjB,gBAAgB;oBAChB,kBAAkB;oBAClB,gBAAgB;oBAChB,kBAAkB;oBAClB,gBAAgB;oBAChB,kBAAkB;oBAClB,gBAAgB;oBAChB,kBAAkB;oBAClB,cAAc;oBACd,mBAAmB;oBACnB,kBAAkB;oBAClB,gBAAgB;oBAChB,YAAY;oBACZ,cAAc;oBACd,YAAY;oBACZ,cAAc;oBACd,eAAe;oBACf,eAAe;oBACf,cAAc;oBACd,gBAAgB;oBAChB,cAAc;oBACd,gBAAgB;oBAChB,cAAc;oBACd,gBAAgB;gBAClB;gBACA,WAAW;YACb;QACF;IACF;IAEA,OAAO;QAAE,SAAS;QAAO,OAAO;IAAiB;AACnD;AAEA,MAAM,cAAc,OAAO;IACzB,QAAQ,GAAG,CAAC,oBAAoB;IAChC,OAAO;QAAE,SAAS;IAAK;AACzB;AAeA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,MAAM,UAAU;IACrB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAMO,MAAM,eAA4C,CAAC,EAAE,QAAQ,EAAE;IACpE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B;IACvE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qCAAqC;IACrC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,eAAe,aAAa,OAAO,CAAC;YAC1C,IAAI,CAAC,cAAc;gBACjB,aAAa;gBACb;YACF;YAEA,MAAM,SAAS,MAAM,gBAAgB;YACrC,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;gBACjC,QAAQ,OAAO,IAAI;gBACnB,eAAe,OAAO,IAAI,CAAC,WAAW;YACxC,OAAO;gBACL,aAAa,UAAU,CAAC;YAC1B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,aAAa,UAAU,CAAC;QAC1B,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,QAAQ,OAAO,UAAkB,UAAkB,aAAsB,KAAK;QAClF,IAAI;YACF,aAAa;YACb,MAAM,SAAS,MAAM,iBAAiB,UAAU,UAAU;YAE1D,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,IAAI,OAAO,OAAO,EAAE;gBACnD,QAAQ,OAAO,IAAI;gBACnB,eAAe,OAAO,IAAI,CAAC,WAAW;gBACtC,aAAa,OAAO,CAAC,gBAAgB,OAAO,OAAO,CAAC,KAAK;gBAEzD,OAAO;oBAAE,SAAS;gBAAK;YACzB,OAAO;gBACL,OAAO;oBAAE,SAAS;oBAAO,OAAO,OAAO,KAAK,IAAI;gBAAsB;YACxE;QACF,EAAE,OAAO,OAAY;YACnB,OAAO;gBAAE,SAAS;gBAAO,OAAO,MAAM,OAAO,IAAI;YAAoB;QACvE,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,SAAS;QACb,IAAI;YACF,MAAM,eAAe,aAAa,OAAO,CAAC;YAC1C,IAAI,cAAc;gBAChB,MAAM,WAAW;YACnB;YAEA,QAAQ;YACR,eAAe;YACf,aAAa,UAAU,CAAC;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,qDAAqD;YACrD,QAAQ;YACR,eAAe;YACf,aAAa,UAAU,CAAC;QAC1B;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,CAAC,aAAa,OAAO;QACzB,OAAO,WAAW,CAAC,WAAW,KAAK;IACrC;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,CAAC,aAAa,OAAO;QACzB,OAAO,gBAAgB,IAAI,CAAC,CAAA,aAAc,WAAW,CAAC,WAAW,KAAK;IACxE;IAEA,MAAM,UAAU,CAAC;QACf,IAAI,CAAC,MAAM,OAAO;QAClB,OAAO,KAAK,IAAI,KAAK;IACvB;IAEA,MAAM,cAAc;QAClB,MAAM;IACR;IAEA,MAAM,QAAyB;QAC7B;QACA;QACA;QACA,iBAAiB,CAAC,CAAC;QACnB;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;AAGO,MAAM,iBAAiB;IAC5B,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,gBAAgB,EAAE,GAAG;IAEzD,OAAO;QACL;QACA;QACA;QAEA,mBAAmB;QACnB,cAAc,cAAc;QAC5B,gBAAgB,cAAc;QAC9B,cAAc,cAAc;QAC5B,gBAAgB,cAAc;QAC9B,eAAe,cAAc;QAC7B,eAAe,cAAc;QAE7B,oBAAoB;QACpB,kBAAkB,cAAc;QAChC,oBAAoB,cAAc;QAClC,kBAAkB,cAAc;QAChC,oBAAoB,cAAc;QAClC,mBAAmB,cAAc;QAEjC,kBAAkB;QAClB,kBAAkB,cAAc;QAChC,oBAAoB,cAAc;QAClC,kBAAkB,cAAc;QAChC,oBAAoB,cAAc;QAClC,mBAAmB,cAAc;QAEjC,kBAAkB;QAClB,kBAAkB,cAAc;QAChC,oBAAoB,cAAc;QAClC,kBAAkB,cAAc;QAChC,oBAAoB,cAAc;QAElC,mBAAmB;QACnB,kBAAkB,cAAc;QAChC,oBAAoB,cAAc;QAClC,kBAAkB,cAAc;QAChC,oBAAoB,cAAc;QAElC,mBAAmB;QACnB,gBAAgB,cAAc;QAC9B,yBAAyB,cAAc;QACvC,wBAAwB,cAAc;QACtC,kBAAkB,cAAc;QAEhC,qBAAqB;QACrB,cAAc,cAAc;QAC5B,gBAAgB,cAAc;QAC9B,cAAc,cAAc;QAC5B,gBAAgB,cAAc;QAE9B,oBAAoB;QACpB,iBAAiB,cAAc;QAC/B,iBAAiB,cAAc;QAE/B,kBAAkB;QAClB,gBAAgB,cAAc;QAC9B,kBAAkB,cAAc;QAEhC,oBAAoB;QACpB,gBAAgB,cAAc;QAC9B,kBAAkB,cAAc;QAChC,gBAAgB,cAAc;QAC9B,kBAAkB,cAAc;QAEhC,cAAc;QACd,sBAAsB,iBAAiB;YAAC;YAAc;YAAgB;SAAa;QACnF,0BAA0B,iBAAiB;YAAC;YAAkB;YAAoB;SAAiB;QACnG,0BAA0B,iBAAiB;YAAC;YAAkB;YAAoB;SAAiB;QACnG,wBAAwB,cAAc;QACtC,sBAAsB,iBAAiB;YAAC;YAAc;YAAgB;SAAa;QACnF,yBAAyB,iBAAiB;YAAC;YAAiB;SAAgB;IAC9E;AACF;AAGO,MAAM,oBAAoB;IAC/B,MAAM,EAAE,IAAI,EAAE,GAAG;IAEjB,MAAM,kBAAkB,OAAO,QAAgB,aAAqB;QAMlE,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,MAAM,YAAY;gBAChB,SAAS,KAAK,EAAE;gBAChB;gBACA;gBACA,GAAG,cAAc;YACnB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,OAAO;QAAE;IAAgB;AAC3B", "debugId": null}}, {"offset": {"line": 388, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%287%29/New%20folder%20%2819%29/pharmacy-accounting-system/src/contexts/NotificationContext.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useState, useEffect } from 'react'\nimport { supabase } from '@/lib/supabase'\n\nexport interface Notification {\n  id: string\n  type: 'info' | 'warning' | 'error' | 'success'\n  title: string\n  message: string\n  category: 'inventory' | 'sales' | 'system' | 'user' | 'financial'\n  priority: 'low' | 'medium' | 'high' | 'critical'\n  isRead: boolean\n  actionUrl?: string\n  actionLabel?: string\n  data?: any\n  createdAt: string\n  expiresAt?: string\n}\n\ninterface NotificationContextType {\n  notifications: Notification[]\n  unreadCount: number\n  addNotification: (notification: Omit<Notification, 'id' | 'isRead' | 'createdAt'>) => void\n  markAsRead: (id: string) => void\n  markAllAsRead: () => void\n  removeNotification: (id: string) => void\n  clearAll: () => void\n  getNotificationsByCategory: (category: string) => Notification[]\n  getNotificationsByPriority: (priority: string) => Notification[]\n  refreshNotifications: () => Promise<void>\n}\n\nconst NotificationContext = createContext<NotificationContextType | undefined>(undefined)\n\nexport const useNotifications = () => {\n  const context = useContext(NotificationContext)\n  if (context === undefined) {\n    throw new Error('useNotifications must be used within a NotificationProvider')\n  }\n  return context\n}\n\ninterface NotificationProviderProps {\n  children: React.ReactNode\n}\n\nexport const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {\n  const [notifications, setNotifications] = useState<Notification[]>([])\n\n  // تحديث التنبيهات عند تحميل المكون\n  useEffect(() => {\n    loadNotifications()\n    \n    // تحديث التنبيهات كل دقيقة\n    const interval = setInterval(loadNotifications, 60000)\n    \n    return () => clearInterval(interval)\n  }, [])\n\n  const loadNotifications = async () => {\n    try {\n      // تحميل التنبيهات من قاعدة البيانات أو إنشاء تنبيهات تجريبية\n      const mockNotifications = await generateSystemNotifications()\n      setNotifications(mockNotifications)\n    } catch (error) {\n      console.error('Error loading notifications:', error)\n    }\n  }\n\n  const generateSystemNotifications = async (): Promise<Notification[]> => {\n    const notifications: Notification[] = []\n    const now = new Date()\n\n    // تنبيهات المخزون\n    notifications.push({\n      id: '1',\n      type: 'warning',\n      title: 'أدوية قاربت على الانتهاء',\n      message: 'يوجد 5 أدوية ستنتهي صلاحيتها خلال 30 يوم',\n      category: 'inventory',\n      priority: 'high',\n      isRead: false,\n      actionUrl: '/inventory?filter=expiring',\n      actionLabel: 'عرض الأدوية',\n      createdAt: new Date(now.getTime() - 2 * 60 * 60 * 1000).toISOString(), // منذ ساعتين\n    })\n\n    notifications.push({\n      id: '2',\n      type: 'error',\n      title: 'نفاد مخزون',\n      message: 'باراسيتامول 500mg - الكمية المتبقية: 0',\n      category: 'inventory',\n      priority: 'critical',\n      isRead: false,\n      actionUrl: '/inventory?search=باراسيتامول',\n      actionLabel: 'إضافة مخزون',\n      createdAt: new Date(now.getTime() - 30 * 60 * 1000).toISOString(), // منذ 30 دقيقة\n    })\n\n    notifications.push({\n      id: '3',\n      type: 'warning',\n      title: 'مخزون منخفض',\n      message: 'يوجد 8 أدوية كميتها أقل من الحد الأدنى',\n      category: 'inventory',\n      priority: 'medium',\n      isRead: false,\n      actionUrl: '/inventory?filter=low-stock',\n      actionLabel: 'عرض التفاصيل',\n      createdAt: new Date(now.getTime() - 4 * 60 * 60 * 1000).toISOString(), // منذ 4 ساعات\n    })\n\n    // تنبيهات المبيعات\n    notifications.push({\n      id: '4',\n      type: 'info',\n      title: 'مبيعات اليوم',\n      message: 'تم تحقيق 2,450,000 د.ع من المبيعات اليوم',\n      category: 'sales',\n      priority: 'low',\n      isRead: true,\n      actionUrl: '/sales-records',\n      actionLabel: 'عرض التفاصيل',\n      createdAt: new Date(now.getTime() - 6 * 60 * 60 * 1000).toISOString(), // منذ 6 ساعات\n    })\n\n    // تنبيهات النظام\n    notifications.push({\n      id: '5',\n      type: 'success',\n      title: 'تحديث النظام',\n      message: 'تم تحديث النظام بنجاح إلى الإصدار 1.0.1',\n      category: 'system',\n      priority: 'low',\n      isRead: false,\n      createdAt: new Date(now.getTime() - 24 * 60 * 60 * 1000).toISOString(), // منذ يوم\n    })\n\n    // تنبيهات المستخدمين\n    notifications.push({\n      id: '6',\n      type: 'info',\n      title: 'مستخدم جديد',\n      message: 'تم إضافة مستخدم جديد: أحمد الصيدلي',\n      category: 'user',\n      priority: 'low',\n      isRead: true,\n      actionUrl: '/users',\n      actionLabel: 'إدارة المستخدمين',\n      createdAt: new Date(now.getTime() - 12 * 60 * 60 * 1000).toISOString(), // منذ 12 ساعة\n    })\n\n    // تنبيهات مالية\n    notifications.push({\n      id: '7',\n      type: 'warning',\n      title: 'فواتير معلقة',\n      message: 'يوجد 12 فاتورة معلقة الدفع بقيمة 850,000 د.ع',\n      category: 'financial',\n      priority: 'high',\n      isRead: false,\n      actionUrl: '/sales-records?filter=pending',\n      actionLabel: 'عرض الفواتير',\n      createdAt: new Date(now.getTime() - 8 * 60 * 60 * 1000).toISOString(), // منذ 8 ساعات\n    })\n\n    return notifications.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())\n  }\n\n  const addNotification = (notificationData: Omit<Notification, 'id' | 'isRead' | 'createdAt'>) => {\n    const newNotification: Notification = {\n      ...notificationData,\n      id: Date.now().toString(),\n      isRead: false,\n      createdAt: new Date().toISOString(),\n    }\n    \n    setNotifications(prev => [newNotification, ...prev])\n  }\n\n  const markAsRead = (id: string) => {\n    setNotifications(prev =>\n      prev.map(notification =>\n        notification.id === id\n          ? { ...notification, isRead: true }\n          : notification\n      )\n    )\n  }\n\n  const markAllAsRead = () => {\n    setNotifications(prev =>\n      prev.map(notification => ({ ...notification, isRead: true }))\n    )\n  }\n\n  const removeNotification = (id: string) => {\n    setNotifications(prev => prev.filter(notification => notification.id !== id))\n  }\n\n  const clearAll = () => {\n    setNotifications([])\n  }\n\n  const getNotificationsByCategory = (category: string) => {\n    return notifications.filter(notification => notification.category === category)\n  }\n\n  const getNotificationsByPriority = (priority: string) => {\n    return notifications.filter(notification => notification.priority === priority)\n  }\n\n  const refreshNotifications = async () => {\n    await loadNotifications()\n  }\n\n  const unreadCount = notifications.filter(n => !n.isRead).length\n\n  const value: NotificationContextType = {\n    notifications,\n    unreadCount,\n    addNotification,\n    markAsRead,\n    markAllAsRead,\n    removeNotification,\n    clearAll,\n    getNotificationsByCategory,\n    getNotificationsByPriority,\n    refreshNotifications,\n  }\n\n  return (\n    <NotificationContext.Provider value={value}>\n      {children}\n    </NotificationContext.Provider>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAiCA,MAAM,oCAAsB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAuC;AAExE,MAAM,mBAAmB;IAC9B,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAMO,MAAM,uBAA4D,CAAC,EAAE,QAAQ,EAAE;IACpF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IAErE,mCAAmC;IACnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QAEA,2BAA2B;QAC3B,MAAM,WAAW,YAAY,mBAAmB;QAEhD,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,MAAM,oBAAoB;QACxB,IAAI;YACF,6DAA6D;YAC7D,MAAM,oBAAoB,MAAM;YAChC,iBAAiB;QACnB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF;IAEA,MAAM,8BAA8B;QAClC,MAAM,gBAAgC,EAAE;QACxC,MAAM,MAAM,IAAI;QAEhB,kBAAkB;QAClB,cAAc,IAAI,CAAC;YACjB,IAAI;YACJ,MAAM;YACN,OAAO;YACP,SAAS;YACT,UAAU;YACV,UAAU;YACV,QAAQ;YACR,WAAW;YACX,aAAa;YACb,WAAW,IAAI,KAAK,IAAI,OAAO,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;QACrE;QAEA,cAAc,IAAI,CAAC;YACjB,IAAI;YACJ,MAAM;YACN,OAAO;YACP,SAAS;YACT,UAAU;YACV,UAAU;YACV,QAAQ;YACR,WAAW;YACX,aAAa;YACb,WAAW,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,KAAK,MAAM,WAAW;QACjE;QAEA,cAAc,IAAI,CAAC;YACjB,IAAI;YACJ,MAAM;YACN,OAAO;YACP,SAAS;YACT,UAAU;YACV,UAAU;YACV,QAAQ;YACR,WAAW;YACX,aAAa;YACb,WAAW,IAAI,KAAK,IAAI,OAAO,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;QACrE;QAEA,mBAAmB;QACnB,cAAc,IAAI,CAAC;YACjB,IAAI;YACJ,MAAM;YACN,OAAO;YACP,SAAS;YACT,UAAU;YACV,UAAU;YACV,QAAQ;YACR,WAAW;YACX,aAAa;YACb,WAAW,IAAI,KAAK,IAAI,OAAO,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;QACrE;QAEA,iBAAiB;QACjB,cAAc,IAAI,CAAC;YACjB,IAAI;YACJ,MAAM;YACN,OAAO;YACP,SAAS;YACT,UAAU;YACV,UAAU;YACV,QAAQ;YACR,WAAW,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,KAAK,KAAK,MAAM,WAAW;QACtE;QAEA,qBAAqB;QACrB,cAAc,IAAI,CAAC;YACjB,IAAI;YACJ,MAAM;YACN,OAAO;YACP,SAAS;YACT,UAAU;YACV,UAAU;YACV,QAAQ;YACR,WAAW;YACX,aAAa;YACb,WAAW,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,KAAK,KAAK,MAAM,WAAW;QACtE;QAEA,gBAAgB;QAChB,cAAc,IAAI,CAAC;YACjB,IAAI;YACJ,MAAM;YACN,OAAO;YACP,SAAS;YACT,UAAU;YACV,UAAU;YACV,QAAQ;YACR,WAAW;YACX,aAAa;YACb,WAAW,IAAI,KAAK,IAAI,OAAO,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;QACrE;QAEA,OAAO,cAAc,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;IACrG;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,kBAAgC;YACpC,GAAG,gBAAgB;YACnB,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,QAAQ;YACR,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,iBAAiB,CAAA,OAAQ;gBAAC;mBAAoB;aAAK;IACrD;IAEA,MAAM,aAAa,CAAC;QAClB,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,eACP,aAAa,EAAE,KAAK,KAChB;oBAAE,GAAG,YAAY;oBAAE,QAAQ;gBAAK,IAChC;IAGV;IAEA,MAAM,gBAAgB;QACpB,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,eAAgB,CAAC;oBAAE,GAAG,YAAY;oBAAE,QAAQ;gBAAK,CAAC;IAE/D;IAEA,MAAM,qBAAqB,CAAC;QAC1B,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,eAAgB,aAAa,EAAE,KAAK;IAC3E;IAEA,MAAM,WAAW;QACf,iBAAiB,EAAE;IACrB;IAEA,MAAM,6BAA6B,CAAC;QAClC,OAAO,cAAc,MAAM,CAAC,CAAA,eAAgB,aAAa,QAAQ,KAAK;IACxE;IAEA,MAAM,6BAA6B,CAAC;QAClC,OAAO,cAAc,MAAM,CAAC,CAAA,eAAgB,aAAa,QAAQ,KAAK;IACxE;IAEA,MAAM,uBAAuB;QAC3B,MAAM;IACR;IAEA,MAAM,cAAc,cAAc,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,MAAM,EAAE,MAAM;IAE/D,MAAM,QAAiC;QACrC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,oBAAoB,QAAQ;QAAC,OAAO;kBAClC;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 582, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%287%29/New%20folder%20%2819%29/pharmacy-accounting-system/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAIF,QAAQC,GAAG,CAACK,yBAAyB,EAAE;;SAcpC;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;QAGT,OAAO;;IAOT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 603, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%287%29/New%20folder%20%2819%29/pharmacy-accounting-system/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 610, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%20%287%29/New%20folder%20%2819%29/pharmacy-accounting-system/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,KAAK", "ignoreList": [0], "debugId": null}}]}