'use client'

import { useState, useEffect } from 'react'
import AppLayout from '@/components/AppLayout'
import ProtectedRoute from '@/components/ProtectedRoute'
import { useAuth, useActivityLogger } from '@/contexts/AuthContext'
import { 
  Activity, 
  Search, 
  Filter, 
  Calendar,
  User,
  Eye,
  Download,
  RefreshCw,
  Clock,
  Shield,
  AlertCircle,
  CheckCircle,
  XCircle,
  Info
} from 'lucide-react'

interface ActivityLogEntry {
  id: string
  user_id: string
  user_name: string
  action: string
  description: string
  table_name: string | null
  record_id: string | null
  old_values: any | null
  new_values: any | null
  ip_address: string | null
  user_agent: string | null
  created_at: string
}

export default function ActivityLogPage() {
  const [activities, setActivities] = useState<ActivityLogEntry[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [userFilter, setUserFilter] = useState('all')
  const [actionFilter, setActionFilter] = useState('all')
  const [dateFilter, setDateFilter] = useState('today')
  const [selectedActivity, setSelectedActivity] = useState<ActivityLogEntry | null>(null)
  const [showDetailsModal, setShowDetailsModal] = useState(false)

  const { user: currentUser } = useAuth()

  useEffect(() => {
    loadActivities()
  }, [dateFilter, userFilter, actionFilter])

  const loadActivities = async () => {
    try {
      setLoading(true)
      // هنا سيتم استدعاء API لجلب سجل النشاطات
      // مؤقتاً سنستخدم بيانات وهمية
      const mockActivities: ActivityLogEntry[] = [
        {
          id: '1',
          user_id: '1',
          user_name: 'مدير النظام',
          action: 'LOGIN',
          description: 'تسجيل دخول المستخدم: admin',
          table_name: 'users',
          record_id: '1',
          old_values: null,
          new_values: null,
          ip_address: '*************',
          user_agent: 'Mozilla/5.0...',
          created_at: new Date().toISOString()
        },
        {
          id: '2',
          user_id: '2',
          user_name: 'أحمد الصيدلي',
          action: 'CREATE_SALE',
          description: 'إنشاء فاتورة مبيعات جديدة: INV-001',
          table_name: 'sales_invoices',
          record_id: 'inv-001',
          old_values: null,
          new_values: { invoice_number: 'INV-001', total_amount: 150000 },
          ip_address: '*************',
          user_agent: 'Mozilla/5.0...',
          created_at: new Date(Date.now() - 3600000).toISOString()
        },
        {
          id: '3',
          user_id: '1',
          user_name: 'مدير النظام',
          action: 'UPDATE_MEDICINE',
          description: 'تعديل معلومات الدواء: باراسيتامول',
          table_name: 'medicines',
          record_id: 'med-001',
          old_values: { price: 5000 },
          new_values: { price: 5500 },
          ip_address: '*************',
          user_agent: 'Mozilla/5.0...',
          created_at: new Date(Date.now() - 7200000).toISOString()
        },
        {
          id: '4',
          user_id: '3',
          user_name: 'فاطمة الكاشير',
          action: 'DELETE_CUSTOMER',
          description: 'حذف عميل: أحمد محمد',
          table_name: 'customers',
          record_id: 'cust-001',
          old_values: { name: 'أحمد محمد', phone: '07701234567' },
          new_values: null,
          ip_address: '*************',
          user_agent: 'Mozilla/5.0...',
          created_at: new Date(Date.now() - 10800000).toISOString()
        },
        {
          id: '5',
          user_id: '2',
          user_name: 'أحمد الصيدلي',
          action: 'PRINT_INVOICE',
          description: 'طباعة فاتورة: INV-001',
          table_name: 'sales_invoices',
          record_id: 'inv-001',
          old_values: null,
          new_values: null,
          ip_address: '*************',
          user_agent: 'Mozilla/5.0...',
          created_at: new Date(Date.now() - 14400000).toISOString()
        }
      ]
      setActivities(mockActivities)
    } catch (error) {
      console.error('Error loading activities:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredActivities = activities.filter(activity => {
    const matchesSearch = activity.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         activity.user_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         activity.action.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesUser = userFilter === 'all' || activity.user_id === userFilter
    const matchesAction = actionFilter === 'all' || activity.action.includes(actionFilter.toUpperCase())
    
    return matchesSearch && matchesUser && matchesAction
  })

  const getActionIcon = (action: string) => {
    if (action.includes('LOGIN') || action.includes('LOGOUT')) {
      return <Shield className="h-4 w-4" />
    } else if (action.includes('CREATE')) {
      return <CheckCircle className="h-4 w-4" />
    } else if (action.includes('UPDATE') || action.includes('EDIT')) {
      return <Info className="h-4 w-4" />
    } else if (action.includes('DELETE')) {
      return <XCircle className="h-4 w-4" />
    } else {
      return <Activity className="h-4 w-4" />
    }
  }

  const getActionColor = (action: string) => {
    if (action.includes('LOGIN') || action.includes('LOGOUT')) {
      return 'text-blue-600 bg-blue-100'
    } else if (action.includes('CREATE')) {
      return 'text-green-600 bg-green-100'
    } else if (action.includes('UPDATE') || action.includes('EDIT')) {
      return 'text-yellow-600 bg-yellow-100'
    } else if (action.includes('DELETE')) {
      return 'text-red-600 bg-red-100'
    } else {
      return 'text-gray-600 bg-gray-100'
    }
  }

  const getActionDisplayName = (action: string) => {
    const actionNames: Record<string, string> = {
      'LOGIN': 'تسجيل دخول',
      'LOGOUT': 'تسجيل خروج',
      'CREATE_SALE': 'إنشاء مبيعات',
      'CREATE_PURCHASE': 'إنشاء مشتريات',
      'CREATE_CUSTOMER': 'إضافة عميل',
      'CREATE_SUPPLIER': 'إضافة مورد',
      'CREATE_MEDICINE': 'إضافة دواء',
      'CREATE_USER': 'إضافة مستخدم',
      'UPDATE_SALE': 'تعديل مبيعات',
      'UPDATE_PURCHASE': 'تعديل مشتريات',
      'UPDATE_CUSTOMER': 'تعديل عميل',
      'UPDATE_SUPPLIER': 'تعديل مورد',
      'UPDATE_MEDICINE': 'تعديل دواء',
      'UPDATE_USER': 'تعديل مستخدم',
      'DELETE_SALE': 'حذف مبيعات',
      'DELETE_PURCHASE': 'حذف مشتريات',
      'DELETE_CUSTOMER': 'حذف عميل',
      'DELETE_SUPPLIER': 'حذف مورد',
      'DELETE_MEDICINE': 'حذف دواء',
      'DELETE_USER': 'حذف مستخدم',
      'PRINT_INVOICE': 'طباعة فاتورة',
      'EXPORT_REPORT': 'تصدير تقرير',
      'ACTIVATE_USER': 'تفعيل مستخدم',
      'DEACTIVATE_USER': 'تعطيل مستخدم'
    }
    return actionNames[action] || action
  }

  const handleViewDetails = (activity: ActivityLogEntry) => {
    setSelectedActivity(activity)
    setShowDetailsModal(true)
  }

  return (
    <ProtectedRoute requiredPermissions={['users_view']}>
      <AppLayout>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">سجل النشاطات</h1>
              <p className="text-gray-600 mt-1">تتبع جميع العمليات والأنشطة في النظام</p>
            </div>
            
            <div className="flex items-center gap-3">
              <button
                onClick={loadActivities}
                className="flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
              >
                <RefreshCw className="h-4 w-4" />
                تحديث
              </button>
              
              <button className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                <Download className="h-4 w-4" />
                تصدير
              </button>
            </div>
          </div>

          {/* Filters */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {/* Search */}
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="text"
                  placeholder="البحث في النشاطات..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              {/* User Filter */}
              <div>
                <select
                  value={userFilter}
                  onChange={(e) => setUserFilter(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">جميع المستخدمين</option>
                  <option value="1">مدير النظام</option>
                  <option value="2">أحمد الصيدلي</option>
                  <option value="3">فاطمة الكاشير</option>
                </select>
              </div>

              {/* Action Filter */}
              <div>
                <select
                  value={actionFilter}
                  onChange={(e) => setActionFilter(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">جميع الأنشطة</option>
                  <option value="login">تسجيل الدخول/الخروج</option>
                  <option value="create">إنشاء</option>
                  <option value="update">تعديل</option>
                  <option value="delete">حذف</option>
                  <option value="print">طباعة</option>
                </select>
              </div>

              {/* Date Filter */}
              <div>
                <select
                  value={dateFilter}
                  onChange={(e) => setDateFilter(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="today">اليوم</option>
                  <option value="yesterday">أمس</option>
                  <option value="week">هذا الأسبوع</option>
                  <option value="month">هذا الشهر</option>
                  <option value="all">جميع الفترات</option>
                </select>
              </div>
            </div>
          </div>

          {/* Activities List */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      النشاط
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      المستخدم
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      الوصف
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      الوقت
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      عنوان IP
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      الإجراءات
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {loading ? (
                    <tr>
                      <td colSpan={6} className="px-6 py-12 text-center">
                        <div className="flex items-center justify-center">
                          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                          <span className="mr-3 text-gray-600">جاري التحميل...</span>
                        </div>
                      </td>
                    </tr>
                  ) : filteredActivities.length === 0 ? (
                    <tr>
                      <td colSpan={6} className="px-6 py-12 text-center">
                        <Activity className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <p className="text-gray-500">لا توجد نشاطات</p>
                      </td>
                    </tr>
                  ) : (
                    filteredActivities.map((activity) => (
                      <tr key={activity.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className={`flex-shrink-0 h-8 w-8 rounded-full flex items-center justify-center ${getActionColor(activity.action)}`}>
                              {getActionIcon(activity.action)}
                            </div>
                            <div className="mr-3">
                              <div className="text-sm font-medium text-gray-900">
                                {getActionDisplayName(activity.action)}
                              </div>
                              {activity.table_name && (
                                <div className="text-xs text-gray-500">
                                  {activity.table_name}
                                </div>
                              )}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <User className="h-4 w-4 text-gray-400 ml-2" />
                            <div className="text-sm text-gray-900">{activity.user_name}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="text-sm text-gray-900 max-w-xs truncate">
                            {activity.description}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center text-sm text-gray-500">
                            <Clock className="h-4 w-4 ml-1" />
                            {new Date(activity.created_at).toLocaleString('ar-EG')}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {activity.ip_address || 'غير محدد'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <button
                            onClick={() => handleViewDetails(activity)}
                            className="text-blue-600 hover:text-blue-900"
                            title="عرض التفاصيل"
                          >
                            <Eye className="h-4 w-4" />
                          </button>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>

          {/* Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Activity className="h-8 w-8 text-blue-600" />
                </div>
                <div className="mr-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      إجمالي النشاطات
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {activities.length}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CheckCircle className="h-8 w-8 text-green-600" />
                </div>
                <div className="mr-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      عمليات الإنشاء
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {activities.filter(a => a.action.includes('CREATE')).length}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Info className="h-8 w-8 text-yellow-600" />
                </div>
                <div className="mr-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      عمليات التعديل
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {activities.filter(a => a.action.includes('UPDATE')).length}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <XCircle className="h-8 w-8 text-red-600" />
                </div>
                <div className="mr-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      عمليات الحذف
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {activities.filter(a => a.action.includes('DELETE')).length}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Details Modal */}
        {showDetailsModal && selectedActivity && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden">
              <div className="flex items-center justify-between p-6 border-b">
                <h2 className="text-xl font-semibold text-gray-900">تفاصيل النشاط</h2>
                <button
                  onClick={() => setShowDetailsModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <XCircle className="h-6 w-6" />
                </button>
              </div>
              
              <div className="p-6 overflow-auto max-h-[calc(90vh-120px)]">
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">النشاط</label>
                    <p className="mt-1 text-sm text-gray-900">{getActionDisplayName(selectedActivity.action)}</p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700">المستخدم</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedActivity.user_name}</p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700">الوصف</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedActivity.description}</p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700">الوقت</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {new Date(selectedActivity.created_at).toLocaleString('ar-EG')}
                    </p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700">عنوان IP</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedActivity.ip_address || 'غير محدد'}</p>
                  </div>
                  
                  {selectedActivity.old_values && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700">القيم السابقة</label>
                      <pre className="mt-1 text-sm text-gray-900 bg-gray-50 p-3 rounded-lg overflow-auto">
                        {JSON.stringify(selectedActivity.old_values, null, 2)}
                      </pre>
                    </div>
                  )}
                  
                  {selectedActivity.new_values && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700">القيم الجديدة</label>
                      <pre className="mt-1 text-sm text-gray-900 bg-gray-50 p-3 rounded-lg overflow-auto">
                        {JSON.stringify(selectedActivity.new_values, null, 2)}
                      </pre>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </AppLayout>
    </ProtectedRoute>
  )
}
