import Link from 'next/link'
import { Home, ArrowRight, Search } from 'lucide-react'

export default function NotFound() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center px-4">
      <div className="max-w-md w-full text-center">
        {/* 404 Animation */}
        <div className="mb-8">
          <div className="text-8xl font-bold text-transparent bg-gradient-to-r from-blue-600 to-indigo-700 bg-clip-text mb-4">
            404
          </div>
          <div className="w-24 h-1 bg-gradient-to-r from-blue-600 to-indigo-700 mx-auto rounded-full"></div>
        </div>

        {/* Content */}
        <div className="bg-white/80 backdrop-blur-lg rounded-2xl shadow-2xl border border-white/20 p-8 mb-8">
          <div className="mb-6">
            <Search className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              الصفحة غير موجودة
            </h1>
            <p className="text-gray-600">
              عذراً، لا يمكن العثور على الصفحة التي تبحث عنها
            </p>
          </div>

          {/* Actions */}
          <div className="space-y-4">
            <Link
              href="/"
              className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
            >
              <Home className="h-5 w-5" />
              العودة للرئيسية
              <ArrowRight className="h-4 w-4" />
            </Link>
            
            <div className="text-sm text-gray-500">
              أو يمكنك استخدام القائمة الجانبية للتنقل
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center text-sm text-gray-500">
          © 2024 مكتب لارين العلمي - نظام إدارة الصيدلية
        </div>
      </div>
    </div>
  )
}
