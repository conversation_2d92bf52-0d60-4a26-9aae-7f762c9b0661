'use client'

import { useState, useEffect } from 'react'
import AppLayout from '@/components/AppLayout'
import {
  Plus,
  Search,
  Filter,
  TrendingUp,
  TrendingDown,
  Wallet,
  Users,
  Building,
  Calendar,
  DollarSign,
  Eye,
  CreditCard,
  Banknote,
  AlertCircle,
  CheckCircle,
  Clock,
  Printer,
  ShoppingCart,
  Package,
  FileText,
  Download,
  RefreshCw
} from 'lucide-react'
import PrintTemplate, { ReportPrint, InvoicePrint } from '@/components/PrintTemplate'
import { usePrintSettings, printReport, printInvoice } from '@/hooks/usePrintSettings'
import {
  getCashTransactions,
  getCashBalance,
  addCashTransaction,
  getCustomerDebts,
  getSupplierDebts,
  updatePaymentStatus,
  initializeSystemData
} from '@/lib/database'

interface CashTransaction {
  id: string
  transaction_type: 'income' | 'expense'
  category: string
  amount: number
  description: string
  reference_type?: string
  reference_id?: string
  payment_method: string
  notes?: string
  created_at: string
}

interface SalesInvoice {
  id: string
  invoice_number: string
  customer_name: string
  total_amount: number
  discount_amount: number
  final_amount: number
  payment_method: string
  payment_status: string
  notes?: string
  created_at: string
}

interface PurchaseInvoice {
  id: string
  invoice_number: string
  supplier_name: string
  total_amount: number
  discount_amount: number
  final_amount: number
  payment_method: string
  payment_status: string
  notes?: string
  created_at: string
}

interface DebtRecord {
  id: string
  invoice_number: string
  customer_name?: string
  supplier_name?: string
  final_amount: number
  payment_status: string
  created_at: string
  customers?: { name: string; phone?: string }
  suppliers?: { name: string; contact_person?: string; phone?: string }
}

export default function CashBoxPage() {
  const [transactions, setTransactions] = useState<CashTransaction[]>([])
  const [filteredTransactions, setFilteredTransactions] = useState<CashTransaction[]>([])
  const [cashBalance, setCashBalance] = useState(0)
  const [customerDebts, setCustomerDebts] = useState<DebtRecord[]>([])
  const [supplierDebts, setSupplierDebts] = useState<DebtRecord[]>([])

  // New states for invoices
  const [salesInvoices, setSalesInvoices] = useState<SalesInvoice[]>([])
  const [purchaseInvoices, setPurchaseInvoices] = useState<PurchaseInvoice[]>([])
  const [filteredInvoices, setFilteredInvoices] = useState<(SalesInvoice | PurchaseInvoice)[]>([])

  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState<'all' | 'income' | 'expense' | 'sales' | 'purchases'>('all')
  const [filterCategory, setFilterCategory] = useState('')
  const [showAddModal, setShowAddModal] = useState(false)
  const [loading, setLoading] = useState(false)
  const [activeTab, setActiveTab] = useState<'transactions' | 'debts' | 'invoices'>('invoices')
  const [showPrintPreview, setShowPrintPreview] = useState(false)
  const [selectedInvoice, setSelectedInvoice] = useState<SalesInvoice | PurchaseInvoice | null>(null)
  const { settings: printSettings } = usePrintSettings()

  // New transaction form
  const [newTransaction, setNewTransaction] = useState({
    transaction_type: 'expense' as 'income' | 'expense',
    category: '',
    amount: 0,
    description: '',
    payment_method: 'cash',
    notes: ''
  })

  useEffect(() => {
    loadData()
  }, [])

  useEffect(() => {
    filterTransactions()
  }, [transactions, searchTerm, filterType, filterCategory])

  useEffect(() => {
    filterInvoices()
  }, [salesInvoices, purchaseInvoices, searchTerm, filterType])

  const loadData = async () => {
    console.log('Loading cash box data...')
    setLoading(true)
    try {
      // Initialize system data first
      await initializeSystemData()

      const [transactionsResult, balanceResult, customerDebtsResult, supplierDebtsResult] = await Promise.all([
        getCashTransactions(),
        getCashBalance(),
        getCustomerDebts(),
        getSupplierDebts()
      ])

      console.log('Cash box data results:', {
        transactions: transactionsResult,
        balance: balanceResult,
        customerDebts: customerDebtsResult,
        supplierDebts: supplierDebtsResult
      })

      if (transactionsResult.success) {
        console.log('Setting transactions:', transactionsResult.data?.length || 0, 'items')
        setTransactions(transactionsResult.data || [])
      }
      if (balanceResult.success) {
        console.log('Setting cash balance:', balanceResult.data || 0)
        setCashBalance(balanceResult.data || 0)
      }
      if (customerDebtsResult.success) setCustomerDebts(customerDebtsResult.data || [])
      if (supplierDebtsResult.success) setSupplierDebts(supplierDebtsResult.data || [])

      // Load invoices from localStorage
      await loadInvoices()
    } catch (error) {
      console.error('Error loading cash box data:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadInvoices = async () => {
    try {
      // Load sales invoices
      const salesData = JSON.parse(localStorage.getItem('sales_invoices') || '[]')
      setSalesInvoices(salesData)

      // Load purchase invoices
      const purchasesData = JSON.parse(localStorage.getItem('purchase_invoices') || '[]')
      setPurchaseInvoices(purchasesData)

      console.log(`Loaded ${salesData.length} sales invoices and ${purchasesData.length} purchase invoices`)
    } catch (error) {
      console.error('Error loading invoices:', error)
    }
  }

  const filterInvoices = () => {
    let allInvoices: (SalesInvoice | PurchaseInvoice)[] = []

    if (filterType === 'all' || filterType === 'sales') {
      allInvoices = [...allInvoices, ...salesInvoices.map(inv => ({ ...inv, type: 'sales' }))]
    }

    if (filterType === 'all' || filterType === 'purchases') {
      allInvoices = [...allInvoices, ...purchaseInvoices.map(inv => ({ ...inv, type: 'purchases' }))]
    }

    if (searchTerm) {
      allInvoices = allInvoices.filter(invoice => {
        const customerName = 'customer_name' in invoice ? invoice.customer_name : ''
        const supplierName = 'supplier_name' in invoice ? invoice.supplier_name : ''
        return (
          invoice.invoice_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
          customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
          supplierName.toLowerCase().includes(searchTerm.toLowerCase())
        )
      })
    }

    // Sort by creation date (newest first)
    allInvoices.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())

    setFilteredInvoices(allInvoices)
  }

  const filterTransactions = () => {
    let filtered = transactions

    if (searchTerm) {
      filtered = filtered.filter(transaction =>
        transaction.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        transaction.category.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (filterType !== 'all') {
      filtered = filtered.filter(transaction => transaction.transaction_type === filterType)
    }

    if (filterCategory) {
      filtered = filtered.filter(transaction => transaction.category === filterCategory)
    }

    setFilteredTransactions(filtered)
  }

  const handleAddTransaction = async () => {
    if (!newTransaction.category || !newTransaction.description || newTransaction.amount <= 0) {
      alert('يرجى ملء جميع الحقول المطلوبة')
      return
    }

    setLoading(true)
    try {
      console.log('Adding cash transaction:', newTransaction)
      const result = await addCashTransaction(newTransaction)
      console.log('Add transaction result:', result)

      if (result.success) {
        alert('تم إضافة المعاملة بنجاح!')
        setShowAddModal(false)
        setNewTransaction({
          transaction_type: 'expense',
          category: '',
          amount: 0,
          description: '',
          payment_method: 'cash',
          notes: ''
        })
        console.log('Reloading data after successful transaction...')
        await loadData()
      } else {
        console.error('Failed to add transaction:', result)
        alert('حدث خطأ أثناء إضافة المعاملة')
      }
    } catch (error) {
      console.error('Error adding transaction:', error)
      alert('حدث خطأ أثناء إضافة المعاملة')
    } finally {
      setLoading(false)
    }
  }

  const handlePayDebt = async (invoiceType: 'sales' | 'purchase', invoiceId: string, amount: number) => {
    if (confirm(`هل تريد تسديد هذا الدين بمبلغ ${amount.toLocaleString()} د.ع؟`)) {
      setLoading(true)
      try {
        const result = await updatePaymentStatus(invoiceType, invoiceId, 'paid', amount)
        if (result.success) {
          alert('تم تسديد الدين بنجاح!')
          await loadData()
        } else {
          alert('حدث خطأ أثناء تسديد الدين')
        }
      } catch (error) {
        console.error('Error paying debt:', error)
        alert('حدث خطأ أثناء تسديد الدين')
      } finally {
        setLoading(false)
      }
    }
  }

  const handleViewInvoice = (invoice: SalesInvoice | PurchaseInvoice) => {
    setSelectedInvoice(invoice)
    setShowPrintPreview(true)
  }

  const handlePrintInvoice = async (invoice: SalesInvoice | PurchaseInvoice) => {
    try {
      const invoiceType = 'customer_name' in invoice ? 'sales' : 'purchases'

      // Enhance invoice data with medicine names for printing
      const enhancedInvoice = await enhanceInvoiceForPrint(invoice, invoiceType)
      printInvoice(enhancedInvoice, invoiceType, printSettings)
    } catch (error) {
      console.error('Error printing invoice:', error)
      // Fallback to original invoice
      const invoiceType = 'customer_name' in invoice ? 'sales' : 'purchases'
      printInvoice(invoice, invoiceType, printSettings)
    }
  }

  const enhanceInvoiceForPrint = async (invoice: SalesInvoice | PurchaseInvoice, type: 'sales' | 'purchases') => {
    try {
      // Get related data from localStorage
      const itemsKey = type === 'sales' ? 'sales_invoice_items' : 'purchase_invoice_items'
      const items = JSON.parse(localStorage.getItem(itemsKey) || '[]')
      const medicines = JSON.parse(localStorage.getItem('medicines') || '[]')
      const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')

      // Get items for this invoice
      const invoiceItems = items.filter((item: any) => item.invoice_id === invoice.id)

      // Enhance items with medicine names
      const enhancedItems = invoiceItems.map((item: any) => {
        const batch = batches.find((b: any) => b.id === item.medicine_batch_id)
        const medicine = medicines.find((m: any) => m.id === batch?.medicine_id)
        const medicineName = medicine?.name || item.medicine_name || item.medicineName || 'غير محدد'

        return {
          ...item,
          medicine_name: medicineName,
          medicineName: medicineName,
          medicine_batches: {
            batch_code: batch?.batch_code || '',
            expiry_date: batch?.expiry_date || '',
            medicines: {
              name: medicineName,
              category: medicine?.category || '',
              manufacturer: medicine?.manufacturer || '',
              strength: medicine?.strength || '',
              form: medicine?.form || ''
            }
          }
        }
      })

      const itemsProperty = type === 'sales' ? 'sales_invoice_items' : 'purchase_invoice_items'
      return {
        ...invoice,
        [itemsProperty]: enhancedItems
      }
    } catch (error) {
      console.error('Error enhancing invoice:', error)
      return invoice
    }
  }

  const getTransactionIcon = (type: string) => {
    return type === 'income' ? (
      <TrendingUp className="h-4 w-4 text-green-500" />
    ) : (
      <TrendingDown className="h-4 w-4 text-red-500" />
    )
  }

  const getTransactionColor = (type: string) => {
    return type === 'income' ? 'text-green-600' : 'text-red-600'
  }

  const calculateTotalIncome = () => {
    return transactions
      .filter(t => t.transaction_type === 'income')
      .reduce((total, t) => total + t.amount, 0)
  }

  const calculateTotalExpenses = () => {
    return transactions
      .filter(t => t.transaction_type === 'expense')
      .reduce((total, t) => total + t.amount, 0)
  }

  const calculateTotalCustomerDebts = () => {
    return customerDebts.reduce((total, debt) => total + debt.final_amount, 0)
  }

  const calculateTotalSupplierDebts = () => {
    return supplierDebts.reduce((total, debt) => total + debt.final_amount, 0)
  }

  const handlePrintCashReport = () => {
    if (filteredTransactions.length > 0) {
      setShowPrintPreview(true)
    } else {
      alert('لا توجد معاملات للطباعة')
    }
  }

  const handleDirectPrint = () => {
    if (filteredTransactions.length > 0) {
      printReport(filteredTransactions, 'cashbox', 'تقرير الصندوق', printSettings)
    } else {
      alert('لا توجد معاملات للطباعة')
    }
  }

  const expenseCategories = [
    'رواتب',
    'إيجار',
    'كهرباء',
    'ماء',
    'هاتف وإنترنت',
    'صيانة',
    'مواد تنظيف',
    'قرطاسية',
    'مواصلات',
    'ضرائب ورسوم',
    'تأمين',
    'دعاية وإعلان',
    'أخرى'
  ]

  const incomeCategories = [
    'مبيعات',
    'خدمات',
    'استشارات',
    'أخرى'
  ]

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">إدارة الصندوق</h1>
            <p className="text-gray-600 mt-1">متابعة الواردات والمصروفات والمديونيات</p>
          </div>
          <div className="flex items-center gap-3">
            <button
              onClick={handlePrintCashReport}
              disabled={filteredTransactions.length === 0}
              className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center gap-2"
            >
              <Printer className="h-4 w-4" />
              معاينة وطباعة
            </button>
            <button
              onClick={handleDirectPrint}
              disabled={filteredTransactions.length === 0}
              className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center gap-2"
            >
              <Printer className="h-4 w-4" />
              طباعة مباشرة
            </button>
            <button
              onClick={() => setShowAddModal(true)}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              إضافة معاملة
            </button>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">رصيد الصندوق</p>
                <p className="text-2xl font-bold text-blue-600">{cashBalance.toLocaleString()} د.ع</p>
              </div>
              <Wallet className="h-8 w-8 text-blue-600" />
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي الواردات</p>
                <p className="text-2xl font-bold text-green-600">{calculateTotalIncome().toLocaleString()} د.ع</p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-600" />
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي المصروفات</p>
                <p className="text-2xl font-bold text-red-600">{calculateTotalExpenses().toLocaleString()} د.ع</p>
              </div>
              <TrendingDown className="h-8 w-8 text-red-600" />
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">صافي الربح</p>
                <p className={`text-2xl font-bold ${calculateTotalIncome() - calculateTotalExpenses() >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {(calculateTotalIncome() - calculateTotalExpenses()).toLocaleString()} د.ع
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-gray-600" />
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              <button
                onClick={() => setActiveTab('invoices')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'invoices'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  جميع الفواتير
                </div>
              </button>
              <button
                onClick={() => setActiveTab('transactions')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'transactions'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                المعاملات المالية
              </button>
              <button
                onClick={() => setActiveTab('debts')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'debts'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                المديونيات
              </button>
            </nav>
          </div>
        </div>

        {/* Invoices Tab */}
        {activeTab === 'invoices' && (
          <div className="space-y-6">
            {/* Filters */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">البحث</label>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <input
                      type="text"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="البحث في الفواتير..."
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">نوع الفاتورة</label>
                  <select
                    value={filterType}
                    onChange={(e) => setFilterType(e.target.value as any)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="all">جميع الفواتير</option>
                    <option value="sales">فواتير المبيعات</option>
                    <option value="purchases">فواتير المشتريات</option>
                  </select>
                </div>

                <div className="flex items-end">
                  <button
                    onClick={() => loadInvoices()}
                    className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2"
                  >
                    <RefreshCw className="h-4 w-4" />
                    تحديث
                  </button>
                </div>
              </div>
            </div>

            {/* Invoices Summary */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">فواتير المبيعات</p>
                    <p className="text-2xl font-bold text-green-600">{salesInvoices.length}</p>
                  </div>
                  <ShoppingCart className="h-8 w-8 text-green-600" />
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">فواتير المشتريات</p>
                    <p className="text-2xl font-bold text-blue-600">{purchaseInvoices.length}</p>
                  </div>
                  <Package className="h-8 w-8 text-blue-600" />
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">إجمالي الفواتير</p>
                    <p className="text-2xl font-bold text-purple-600">{filteredInvoices.length}</p>
                  </div>
                  <FileText className="h-8 w-8 text-purple-600" />
                </div>
              </div>
            </div>

            {/* Invoices Table */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">قائمة الفواتير</h3>
              </div>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        رقم الفاتورة
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        النوع
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        العميل/المورد
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        المبلغ النهائي
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        حالة الدفع
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        التاريخ
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        الإجراءات
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredInvoices.map((invoice: any) => (
                      <tr key={invoice.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {invoice.invoice_number}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            invoice.type === 'sales'
                              ? 'bg-green-100 text-green-800'
                              : 'bg-blue-100 text-blue-800'
                          }`}>
                            {invoice.type === 'sales' ? 'مبيعات' : 'مشتريات'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {invoice.customer_name || invoice.supplier_name}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {invoice.final_amount?.toLocaleString()} د.ع
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            invoice.payment_status === 'paid'
                              ? 'bg-green-100 text-green-800'
                              : 'bg-yellow-100 text-yellow-800'
                          }`}>
                            {invoice.payment_status === 'paid' ? 'مدفوع' : 'معلق'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(invoice.created_at).toLocaleDateString('ar-EG')}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex items-center gap-2">
                            <button
                              onClick={() => handleViewInvoice(invoice)}
                              className="text-blue-600 hover:text-blue-900"
                              title="عرض التفاصيل"
                            >
                              <Eye className="h-4 w-4" />
                            </button>
                            <button
                              onClick={() => handlePrintInvoice(invoice)}
                              className="text-green-600 hover:text-green-900"
                              title="طباعة"
                            >
                              <Printer className="h-4 w-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
                {filteredInvoices.length === 0 && (
                  <div className="text-center py-12">
                    <FileText className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">لا توجد فواتير</h3>
                    <p className="mt-1 text-sm text-gray-500">لم يتم العثور على فواتير تطابق معايير البحث.</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Transactions Tab */}
        {activeTab === 'transactions' && (
          <div className="space-y-6">
            {/* Filters */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">البحث</label>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <input
                      type="text"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="البحث في المعاملات..."
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">نوع المعاملة</label>
                  <select
                    value={filterType}
                    onChange={(e) => setFilterType(e.target.value as any)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="all">جميع المعاملات</option>
                    <option value="income">واردات</option>
                    <option value="expense">مصروفات</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">الفئة</label>
                  <select
                    value={filterCategory}
                    onChange={(e) => setFilterCategory(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">جميع الفئات</option>
                    {[...new Set([...expenseCategories, ...incomeCategories])].map((category, index) => (
                      <option key={`filter_category_${index}_${category}`} value={category}>{category}</option>
                    ))}
                  </select>
                </div>

                <div className="flex items-end">
                  <button
                    onClick={() => {
                      setSearchTerm('')
                      setFilterType('all')
                      setFilterCategory('')
                    }}
                    className="w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 flex items-center justify-center gap-2"
                  >
                    <Filter className="h-4 w-4" />
                    إعادة تعيين
                  </button>
                </div>
              </div>
            </div>

            {/* Transactions List */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              {filteredTransactions.length === 0 ? (
                <div className="text-center py-12">
                  <Wallet className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد معاملات</h3>
                  <p className="text-gray-500 mb-4">لم يتم تسجيل أي معاملات مالية بعد</p>
                  <button
                    onClick={() => setShowAddModal(true)}
                    className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
                  >
                    إضافة أول معاملة
                  </button>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          النوع
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          الفئة
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          الوصف
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          المبلغ
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          طريقة الدفع
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          التاريخ
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {filteredTransactions.map((transaction) => (
                        <tr key={transaction.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              {getTransactionIcon(transaction.transaction_type)}
                              <span className={`mr-2 text-sm font-medium ${getTransactionColor(transaction.transaction_type)}`}>
                                {transaction.transaction_type === 'income' ? 'وارد' : 'مصروف'}
                              </span>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className="text-sm text-gray-900">{transaction.category}</span>
                          </td>
                          <td className="px-6 py-4">
                            <span className="text-sm text-gray-900">{transaction.description}</span>
                            {transaction.notes && (
                              <p className="text-xs text-gray-500 mt-1">{transaction.notes}</p>
                            )}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <DollarSign className="h-4 w-4 text-gray-400 mr-1" />
                              <span className={`text-sm font-medium ${getTransactionColor(transaction.transaction_type)}`}>
                                {transaction.transaction_type === 'income' ? '+' : '-'}{transaction.amount.toLocaleString()} د.ع
                              </span>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              {transaction.payment_method === 'cash' ? (
                                <Banknote className="h-4 w-4 text-green-500 mr-1" />
                              ) : (
                                <CreditCard className="h-4 w-4 text-blue-500 mr-1" />
                              )}
                              <span className="text-sm text-gray-900">
                                {transaction.payment_method === 'cash' ? 'نقداً' : 'بنكي'}
                              </span>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <Calendar className="h-4 w-4 text-gray-400 mr-1" />
                              <span className="text-sm text-gray-900">
                                {new Date(transaction.created_at).toLocaleDateString('ar-EG')}
                              </span>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Debts Tab */}
        {activeTab === 'debts' && (
          <div className="space-y-6">
            {/* Customer Debts */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold text-gray-900">ديون العملاء</h2>
                <div className="flex items-center gap-2">
                  <Users className="h-5 w-5 text-blue-600" />
                  <span className="text-sm text-gray-600">
                    إجمالي: {calculateTotalCustomerDebts().toLocaleString()} د.ع
                  </span>
                </div>
              </div>

              {customerDebts.length === 0 ? (
                <div className="text-center py-8">
                  <CheckCircle className="h-12 w-12 text-green-300 mx-auto mb-4" />
                  <p className="text-gray-500">لا توجد ديون على العملاء</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                          رقم الفاتورة
                        </th>
                        <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                          العميل
                        </th>
                        <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                          المبلغ
                        </th>
                        <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                          التاريخ
                        </th>
                        <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                          إجراءات
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {customerDebts.map((debt) => (
                        <tr key={debt.id} className="hover:bg-gray-50">
                          <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                            {debt.invoice_number}
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                            {debt.customers?.name || debt.customer_name}
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-red-600">
                            {debt.final_amount.toLocaleString()} د.ع
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                            {new Date(debt.created_at).toLocaleDateString('ar-EG')}
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap">
                            <button
                              onClick={() => handlePayDebt('sales', debt.id, debt.final_amount)}
                              className="bg-green-600 text-white px-3 py-1 rounded text-xs hover:bg-green-700"
                            >
                              تسديد
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>

            {/* Supplier Debts */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold text-gray-900">ديون الموردين</h2>
                <div className="flex items-center gap-2">
                  <Building className="h-5 w-5 text-orange-600" />
                  <span className="text-sm text-gray-600">
                    إجمالي: {calculateTotalSupplierDebts().toLocaleString()} د.ع
                  </span>
                </div>
              </div>

              {supplierDebts.length === 0 ? (
                <div className="text-center py-8">
                  <CheckCircle className="h-12 w-12 text-green-300 mx-auto mb-4" />
                  <p className="text-gray-500">لا توجد ديون للموردين</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                          رقم الفاتورة
                        </th>
                        <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                          المورد
                        </th>
                        <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                          المبلغ
                        </th>
                        <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                          التاريخ
                        </th>
                        <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                          إجراءات
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {supplierDebts.map((debt) => (
                        <tr key={debt.id} className="hover:bg-gray-50">
                          <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                            {debt.invoice_number}
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                            {debt.suppliers?.name}
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-orange-600">
                            {debt.final_amount.toLocaleString()} د.ع
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                            {new Date(debt.created_at).toLocaleDateString('ar-EG')}
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap">
                            <button
                              onClick={() => handlePayDebt('purchase', debt.id, debt.final_amount)}
                              className="bg-orange-600 text-white px-3 py-1 rounded text-xs hover:bg-orange-700"
                            >
                              دفع
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Add Transaction Modal */}
        {showAddModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold text-gray-900">إضافة معاملة جديدة</h2>
                <button
                  onClick={() => setShowAddModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">نوع المعاملة</label>
                  <div className="grid grid-cols-2 gap-3">
                    <button
                      onClick={() => setNewTransaction({ ...newTransaction, transaction_type: 'income' })}
                      className={`p-3 border-2 rounded-lg text-center transition-colors ${
                        newTransaction.transaction_type === 'income'
                          ? 'border-green-500 bg-green-50 text-green-700'
                          : 'border-gray-300 text-gray-600 hover:border-green-300'
                      }`}
                    >
                      <TrendingUp className="h-5 w-5 mx-auto mb-1" />
                      <div className="text-sm font-medium">وارد</div>
                    </button>

                    <button
                      onClick={() => setNewTransaction({ ...newTransaction, transaction_type: 'expense' })}
                      className={`p-3 border-2 rounded-lg text-center transition-colors ${
                        newTransaction.transaction_type === 'expense'
                          ? 'border-red-500 bg-red-50 text-red-700'
                          : 'border-gray-300 text-gray-600 hover:border-red-300'
                      }`}
                    >
                      <TrendingDown className="h-5 w-5 mx-auto mb-1" />
                      <div className="text-sm font-medium">مصروف</div>
                    </button>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">الفئة *</label>
                  <select
                    value={newTransaction.category}
                    onChange={(e) => setNewTransaction({ ...newTransaction, category: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">اختر الفئة</option>
                    {(newTransaction.transaction_type === 'expense' ? expenseCategories : incomeCategories).map((category, index) => (
                      <option key={`new_transaction_category_${index}_${category}`} value={category}>{category}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">المبلغ *</label>
                  <input
                    type="number"
                    value={newTransaction.amount}
                    onChange={(e) => setNewTransaction({ ...newTransaction, amount: Number(e.target.value) })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="0"
                    min="0"
                    step="0.01"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">الوصف *</label>
                  <input
                    type="text"
                    value={newTransaction.description}
                    onChange={(e) => setNewTransaction({ ...newTransaction, description: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="وصف المعاملة"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">طريقة الدفع</label>
                  <select
                    value={newTransaction.payment_method}
                    onChange={(e) => setNewTransaction({ ...newTransaction, payment_method: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="cash">نقداً</option>
                    <option value="bank">بنكي</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">ملاحظات</label>
                  <textarea
                    value={newTransaction.notes}
                    onChange={(e) => setNewTransaction({ ...newTransaction, notes: e.target.value })}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="ملاحظات إضافية..."
                  />
                </div>
              </div>

              <div className="flex justify-end gap-3 mt-6">
                <button
                  onClick={() => setShowAddModal(false)}
                  className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
                >
                  إلغاء
                </button>
                <button
                  onClick={handleAddTransaction}
                  disabled={loading || !newTransaction.category || !newTransaction.description || newTransaction.amount <= 0}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
                >
                  {loading ? 'جاري الإضافة...' : 'إضافة المعاملة'}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Print Preview Modal */}
      {showPrintPreview && !selectedInvoice && filteredTransactions.length > 0 && (
        <PrintTemplate
          title="تقرير الصندوق"
          data={filteredTransactions}
          type="report"
          settings={printSettings}
          onClose={() => setShowPrintPreview(false)}
        >
          <ReportPrint
            reportData={filteredTransactions}
            reportType="cashbox"
            title="تقرير الصندوق"
            settings={printSettings}
          />
        </PrintTemplate>
      )}

      {/* Invoice Preview Modal */}
      {showPrintPreview && selectedInvoice && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900">
                معاينة الفاتورة - {selectedInvoice.invoice_number}
              </h2>
              <div className="flex items-center gap-2">
                <button
                  onClick={() => handlePrintInvoice(selectedInvoice)}
                  className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center gap-2"
                >
                  <Printer className="h-4 w-4" />
                  طباعة
                </button>
                <button
                  onClick={() => {
                    setShowPrintPreview(false)
                    setSelectedInvoice(null)
                  }}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>
            </div>

            <div className="border border-gray-200 rounded-lg p-6 bg-gray-50">
              <div className="bg-white p-6 rounded-lg">
                <div className="text-center mb-6">
                  <h1 className="text-2xl font-bold text-gray-900">صيدلية الشفاء</h1>
                  <p className="text-gray-600">فاتورة {'customer_name' in selectedInvoice ? 'مبيعات' : 'مشتريات'}</p>
                </div>

                <div className="grid grid-cols-2 gap-6 mb-6">
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">معلومات الفاتورة</h3>
                    <p><span className="font-medium">رقم الفاتورة:</span> {selectedInvoice.invoice_number}</p>
                    <p><span className="font-medium">التاريخ:</span> {new Date(selectedInvoice.created_at).toLocaleDateString('ar-EG')}</p>
                    <p><span className="font-medium">طريقة الدفع:</span> {selectedInvoice.payment_method === 'cash' ? 'نقداً' : 'آجل'}</p>
                    <p><span className="font-medium">حالة الدفع:</span> {selectedInvoice.payment_status === 'paid' ? 'مدفوع' : 'معلق'}</p>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">
                      {'customer_name' in selectedInvoice ? 'معلومات العميل' : 'معلومات المورد'}
                    </h3>
                    <p><span className="font-medium">الاسم:</span> {'customer_name' in selectedInvoice ? selectedInvoice.customer_name : selectedInvoice.supplier_name}</p>
                  </div>
                </div>

                <div className="mb-6">
                  <h3 className="font-semibold text-gray-900 mb-4">تفاصيل الفاتورة</h3>
                  <div className="overflow-x-auto">
                    <table className="w-full border border-gray-300">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="border border-gray-300 px-4 py-2 text-right">الدواء</th>
                          <th className="border border-gray-300 px-4 py-2 text-right">الكمية</th>
                          <th className="border border-gray-300 px-4 py-2 text-right">السعر</th>
                          <th className="border border-gray-300 px-4 py-2 text-right">المجموع</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td className="border border-gray-300 px-4 py-2" colSpan={4}>
                            <div className="text-center text-gray-500 py-4">
                              لا توجد تفاصيل متاحة للعرض
                            </div>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>

                <div className="border-t pt-4">
                  <div className="flex justify-end">
                    <div className="w-64">
                      <div className="flex justify-between py-2">
                        <span>المجموع الفرعي:</span>
                        <span>{selectedInvoice.total_amount?.toLocaleString()} د.ع</span>
                      </div>
                      <div className="flex justify-between py-2">
                        <span>الخصم:</span>
                        <span>{selectedInvoice.discount_amount?.toLocaleString()} د.ع</span>
                      </div>
                      <div className="flex justify-between py-2 border-t font-bold">
                        <span>المجموع النهائي:</span>
                        <span>{selectedInvoice.final_amount?.toLocaleString()} د.ع</span>
                      </div>
                    </div>
                  </div>
                </div>

                {selectedInvoice.notes && (
                  <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                    <h4 className="font-medium text-gray-900 mb-2">ملاحظات:</h4>
                    <p className="text-gray-700">{selectedInvoice.notes}</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </AppLayout>
  )
}
