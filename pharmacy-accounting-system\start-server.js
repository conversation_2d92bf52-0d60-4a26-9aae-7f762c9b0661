const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 بدء تشغيل نظام إدارة الصيدلية...');
console.log('📁 المجلد:', __dirname);

const nextProcess = spawn('npx', ['next', 'dev', '--port', '3000'], {
  cwd: __dirname,
  stdio: 'inherit',
  shell: true
});

nextProcess.on('error', (error) => {
  console.error('❌ خطأ في تشغيل الخادم:', error);
});

nextProcess.on('close', (code) => {
  console.log(`🔴 توقف الخادم برمز: ${code}`);
});

console.log('✅ تم بدء تشغيل الخادم على http://localhost:3000');
console.log('📝 للوصول لصفحة تسجيل الدخول: http://localhost:3000/login');
console.log('⏹️  لإيقاف الخادم اضغط Ctrl+C');
