import { supabase } from './supabase'

// Medicine operations
export const addMedicine = async (medicineData: {
  name: string
  category: string
  manufacturer?: string
  active_ingredient?: string
  strength?: string
  form: string
  unit_price: number
  selling_price: number
}) => {
  try {
    const { data, error } = await supabase
      .from('medicines')
      .insert([{
        name: medicineData.name,
        category: medicineData.category,
        manufacturer: medicineData.manufacturer || '',
        active_ingredient: medicineData.active_ingredient || '',
        strength: medicineData.strength || '',
        form: medicineData.form,
        unit_price: medicineData.unit_price,
        selling_price: medicineData.selling_price
      }])
      .select()
      .single()

    if (error) throw error
    return { success: true, data }
  } catch (error) {
    console.error('Error adding medicine:', error)
    return { success: false, error }
  }
}

export const getMedicines = async () => {
  try {
    const { data, error } = await supabase
      .from('medicines')
      .select(`
        *,
        medicine_batches (
          id,
          batch_code,
          expiry_date,
          quantity,
          cost_price,
          selling_price,
          supplier_id,
          received_date
        )
      `)
      .order('name')

    if (error) {
      console.warn('Supabase error fetching medicines, using localStorage:', error)
      // Fallback to localStorage
      return getMedicinesFromLocalStorage()
    }

    return { success: true, data }
  } catch (error) {
    console.error('Error fetching medicines:', error)
    // Final fallback to localStorage
    return getMedicinesFromLocalStorage()
  }
}

// Helper function to get medicines from localStorage
const getMedicinesFromLocalStorage = () => {
  try {
    const medicines = JSON.parse(localStorage.getItem('medicines') || '[]')
    const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')

    // If no medicines in localStorage, create sample data
    if (medicines.length === 0) {
      console.log('🔄 لا توجد أدوية في localStorage، إنشاء بيانات تجريبية...')
      return createSampleMedicinesData()
    }

    // Combine medicines with their batches
    const medicinesWithBatches = medicines.map((medicine: any) => ({
      ...medicine,
      medicine_batches: batches.filter((batch: any) => batch.medicine_id === medicine.id),
      batches: batches.filter((batch: any) => batch.medicine_id === medicine.id)
    }))

    console.log(`✅ تم تحميل ${medicinesWithBatches.length} دواء من localStorage`)
    return { success: true, data: medicinesWithBatches }
  } catch (error) {
    console.error('Error loading medicines from localStorage:', error)
    return { success: false, error }
  }
}

// Helper function to create sample medicines data
const createSampleMedicinesData = () => {
  try {
    const sampleMedicines = [
      {
        id: 'med_1',
        name: 'باراسيتامول 500 مجم',
        category: 'مسكنات',
        manufacturer: 'شركة الأدوية العراقية',
        strength: '500mg',
        form: 'أقراص',
        created_at: new Date().toISOString()
      },
      {
        id: 'med_2',
        name: 'أموكسيسيلين 250 مجم',
        category: 'مضادات حيوية',
        manufacturer: 'شركة بغداد للأدوية',
        strength: '250mg',
        form: 'كبسولات',
        created_at: new Date().toISOString()
      },
      {
        id: 'med_3',
        name: 'أسبرين 100 مجم',
        category: 'مسكنات',
        manufacturer: 'شركة النهرين',
        strength: '100mg',
        form: 'أقراص',
        created_at: new Date().toISOString()
      },
      {
        id: 'med_4',
        name: 'إيبوبروفين 400 مجم',
        category: 'مسكنات',
        manufacturer: 'شركة الرافدين',
        strength: '400mg',
        form: 'أقراص',
        created_at: new Date().toISOString()
      },
      {
        id: 'med_5',
        name: 'أوميبرازول 20 مجم',
        category: 'أدوية المعدة',
        manufacturer: 'شركة دجلة',
        strength: '20mg',
        form: 'كبسولات',
        created_at: new Date().toISOString()
      }
    ]

    const sampleBatches = [
      {
        id: 'batch_1',
        medicine_id: 'med_1',
        batch_code: 'PAR001',
        expiry_date: '2025-12-31',
        quantity: 100,
        cost_price: 500,
        selling_price: 750,
        received_date: '2024-01-01',
        created_at: new Date().toISOString()
      },
      {
        id: 'batch_2',
        medicine_id: 'med_2',
        batch_code: 'AMX001',
        expiry_date: '2025-06-30',
        quantity: 50,
        cost_price: 1000,
        selling_price: 1500,
        received_date: '2024-01-01',
        created_at: new Date().toISOString()
      },
      {
        id: 'batch_3',
        medicine_id: 'med_3',
        batch_code: 'ASP001',
        expiry_date: '2026-03-31',
        quantity: 200,
        cost_price: 300,
        selling_price: 500,
        received_date: '2024-01-01',
        created_at: new Date().toISOString()
      },
      {
        id: 'batch_4',
        medicine_id: 'med_4',
        batch_code: 'IBU001',
        expiry_date: '2025-09-30',
        quantity: 75,
        cost_price: 800,
        selling_price: 1200,
        received_date: '2024-01-01',
        created_at: new Date().toISOString()
      },
      {
        id: 'batch_5',
        medicine_id: 'med_5',
        batch_code: 'OME001',
        expiry_date: '2025-11-30',
        quantity: 30,
        cost_price: 1500,
        selling_price: 2000,
        received_date: '2024-01-01',
        created_at: new Date().toISOString()
      }
    ]

    // Create sample customers
    const sampleCustomers = [
      {
        id: 'cust_1',
        name: 'أحمد محمد علي',
        phone: '07701234567',
        address: 'بغداد - الكرادة',
        created_at: new Date().toISOString()
      },
      {
        id: 'cust_2',
        name: 'فاطمة حسن',
        phone: '07809876543',
        address: 'بغداد - الجادرية',
        created_at: new Date().toISOString()
      }
    ]

    // Save to localStorage
    localStorage.setItem('medicines', JSON.stringify(sampleMedicines))
    localStorage.setItem('medicine_batches', JSON.stringify(sampleBatches))
    localStorage.setItem('customers', JSON.stringify(sampleCustomers))

    // Initialize empty arrays for invoices
    localStorage.setItem('sales_invoices', JSON.stringify([]))
    localStorage.setItem('sales_invoice_items', JSON.stringify([]))

    // Combine medicines with their batches
    const medicinesWithBatches = sampleMedicines.map((medicine: any) => ({
      ...medicine,
      medicine_batches: sampleBatches.filter((batch: any) => batch.medicine_id === medicine.id),
      batches: sampleBatches.filter((batch: any) => batch.medicine_id === medicine.id)
    }))

    console.log(`✅ تم إنشاء ${medicinesWithBatches.length} دواء تجريبي`)
    console.log(`✅ تم إنشاء ${sampleBatches.length} دفعة تجريبية`)
    console.log(`✅ تم إنشاء ${sampleCustomers.length} عميل تجريبي`)
    return { success: true, data: medicinesWithBatches }
  } catch (error) {
    console.error('Error creating sample medicines:', error)
    return { success: false, error }
  }
}

// Function to initialize system data
export const initializeSystemData = async () => {
  try {
    console.log('🔄 تهيئة بيانات النظام...')

    // Check if we have basic data
    const medicines = JSON.parse(localStorage.getItem('medicines') || '[]')
    const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')

    if (medicines.length === 0 || batches.length === 0) {
      console.log('📦 إنشاء البيانات الأساسية...')
      return createSampleMedicinesData()
    }

    console.log(`✅ البيانات الأساسية موجودة: ${medicines.length} دواء، ${batches.length} دفعة`)
    return { success: true, data: medicines }
  } catch (error) {
    console.error('Error initializing system data:', error)
    return { success: false, error }
  }
}

// Medicine batch operations
export const addMedicineBatch = async (batchData: {
  medicine_id: string
  batch_code: string
  expiry_date: string
  quantity: number
  cost_price: number
  selling_price: number
  supplier_id?: string
}) => {
  try {
    const { data, error } = await supabase
      .from('medicine_batches')
      .insert([batchData])
      .select()
      .single()

    if (error) throw error
    return { success: true, data }
  } catch (error) {
    console.error('Error adding medicine batch:', error)
    return { success: false, error }
  }
}

export const updateBatchQuantity = async (batchId: string, newQuantity: number) => {
  try {
    const { data, error } = await supabase
      .from('medicine_batches')
      .update({ quantity: newQuantity })
      .eq('id', batchId)
      .select()
      .single()

    if (error) {
      console.warn('Supabase error updating batch quantity, using localStorage:', error)
      // Fallback to localStorage
      const existingBatches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')
      const batchIndex = existingBatches.findIndex((batch: any) => batch.id === batchId)

      if (batchIndex !== -1) {
        existingBatches[batchIndex].quantity = newQuantity
        localStorage.setItem('medicine_batches', JSON.stringify(existingBatches))
        return { success: true, data: existingBatches[batchIndex] }
      }

      return { success: false, error: 'Batch not found in localStorage' }
    }

    return { success: true, data }
  } catch (error) {
    console.error('Error updating batch quantity:', error)

    // Final fallback to localStorage
    try {
      const existingBatches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')
      const batchIndex = existingBatches.findIndex((batch: any) => batch.id === batchId)

      if (batchIndex !== -1) {
        existingBatches[batchIndex].quantity = newQuantity
        localStorage.setItem('medicine_batches', JSON.stringify(existingBatches))
        return { success: true, data: existingBatches[batchIndex] }
      }

      return { success: false, error: 'Batch not found' }
    } catch (localError) {
      console.error('LocalStorage fallback failed for batch update:', localError)
      return { success: false, error: localError }
    }
  }
}

// Sales operations
export const createSalesInvoice = async (invoiceData: {
  invoice_number: string
  customer_id?: string
  customer_name?: string
  total_amount: number
  discount_amount: number
  final_amount: number
  payment_method: string
  payment_status: string
  notes?: string
  private_notes?: string
}) => {
  try {
    // Try Supabase first
    const { data, error } = await supabase
      .from('sales_invoices')
      .insert([invoiceData])
      .select()
      .single()

    if (error) {
      console.warn('Supabase error, using localStorage:', error)
      // Fallback to localStorage
      const invoiceId = `invoice_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      const invoice = {
        id: invoiceId,
        ...invoiceData,
        created_at: new Date().toISOString()
      }

      // Save to localStorage
      const existingInvoices = JSON.parse(localStorage.getItem('sales_invoices') || '[]')
      existingInvoices.push(invoice)
      localStorage.setItem('sales_invoices', JSON.stringify(existingInvoices))

      return { success: true, data: invoice }
    }

    return { success: true, data }
  } catch (error) {
    console.error('Error creating sales invoice:', error)

    // Final fallback to localStorage
    try {
      const invoiceId = `invoice_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      const invoice = {
        id: invoiceId,
        ...invoiceData,
        created_at: new Date().toISOString()
      }

      const existingInvoices = JSON.parse(localStorage.getItem('sales_invoices') || '[]')
      existingInvoices.push(invoice)
      localStorage.setItem('sales_invoices', JSON.stringify(existingInvoices))

      return { success: true, data: invoice }
    } catch (localError) {
      console.error('LocalStorage fallback failed:', localError)
      return { success: false, error: localError }
    }
  }
}

export const addSalesInvoiceItems = async (items: Array<{
  invoice_id: string
  medicine_batch_id: string
  quantity: number
  unit_price: number
  total_price: number
  is_gift: boolean
  medicine_name?: string
}>) => {
  try {
    const { data, error } = await supabase
      .from('sales_invoice_items')
      .insert(items)
      .select()

    if (error) {
      console.warn('Supabase error for invoice items, using localStorage:', error)
      // Fallback to localStorage - preserve existing medicine names
      console.log('📦 العناصر الواردة للحفظ:', items)

      const enhancedItems = items.map(item => {
        // Use existing medicine name if available, otherwise enhance
        const medicineName = item.medicine_name || item.medicineName

        if (medicineName && medicineName !== 'غير محدد') {
          console.log(`✅ استخدام اسم الدواء الموجود: ${medicineName}`)
          return {
            id: `item_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            ...item,
            medicine_name: medicineName,
            medicineName: medicineName,
            medicine_batches: {
              batch_code: '',
              expiry_date: '',
              medicines: {
                name: medicineName,
                category: '',
                manufacturer: '',
                strength: '',
                form: ''
              }
            },
            created_at: new Date().toISOString()
          }
        } else {
          console.log(`⚠️ لا يوجد اسم دواء، سيتم البحث عنه...`)
          // Only enhance if no medicine name is available
          return item
        }
      })

      // Enhance items that still need medicine names
      const itemsNeedingEnhancement = enhancedItems.filter(item =>
        !item.medicine_name || item.medicine_name === 'غير محدد'
      )

      let finalItems = enhancedItems
      if (itemsNeedingEnhancement.length > 0) {
        console.log(`🔍 تحسين ${itemsNeedingEnhancement.length} عنصر يحتاج أسماء أدوية`)
        const enhancedNeeded = await enhanceItemsWithMedicineNames(itemsNeedingEnhancement)

        // Replace items that needed enhancement
        finalItems = enhancedItems.map(item => {
          if (!item.medicine_name || item.medicine_name === 'غير محدد') {
            const enhanced = enhancedNeeded.find(e => e.medicine_batch_id === item.medicine_batch_id)
            return enhanced || item
          }
          return item
        })
      }

      const existingItems = JSON.parse(localStorage.getItem('sales_invoice_items') || '[]')
      existingItems.push(...finalItems)
      localStorage.setItem('sales_invoice_items', JSON.stringify(existingItems))

      console.log('✅ تم حفظ العناصر في localStorage:', finalItems)
      return { success: true, data: finalItems }
    }

    return { success: true, data }
  } catch (error) {
    console.error('Error adding sales invoice items:', error)

    // Final fallback to localStorage
    try {
      console.log('🔄 Final fallback - حفظ العناصر مع الأسماء الموجودة')

      const enhancedItems = items.map(item => {
        const medicineName = item.medicine_name || item.medicineName || 'غير محدد'

        return {
          id: `item_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          ...item,
          medicine_name: medicineName,
          medicineName: medicineName,
          medicine_batches: {
            batch_code: '',
            expiry_date: '',
            medicines: {
              name: medicineName,
              category: '',
              manufacturer: '',
              strength: '',
              form: ''
            }
          },
          created_at: new Date().toISOString()
        }
      })

      const existingItems = JSON.parse(localStorage.getItem('sales_invoice_items') || '[]')
      existingItems.push(...enhancedItems)
      localStorage.setItem('sales_invoice_items', JSON.stringify(existingItems))

      console.log('✅ Final fallback - تم حفظ العناصر:', enhancedItems)
      return { success: true, data: enhancedItems }
    } catch (localError) {
      console.error('LocalStorage fallback failed for items:', localError)
      return { success: false, error: localError }
    }
  }
}

// Helper function to enhance items with medicine names
const enhanceItemsWithMedicineNames = async (items: any[]) => {
  try {
    // Get medicine data for names
    const medicines = JSON.parse(localStorage.getItem('medicines') || '[]')
    const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')

    return items.map(item => {
      // Use existing medicine name if available, otherwise find from batch
      let medicineName = item.medicine_name || item.medicineName

      if (!medicineName || medicineName === 'غير محدد') {
        // Find medicine name from batch
        const batch = batches.find((b: any) => b.id === item.medicine_batch_id)
        const medicine = medicines.find((m: any) => m.id === batch?.medicine_id)
        medicineName = medicine?.name || 'غير محدد'
      }

      const batch = batches.find((b: any) => b.id === item.medicine_batch_id)
      const medicine = medicines.find((m: any) => m.id === batch?.medicine_id)

      return {
        id: `item_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        ...item,
        medicine_name: medicineName,
        medicineName: medicineName, // Add both for compatibility
        medicine_batches: {
          batch_code: batch?.batch_code || '',
          expiry_date: batch?.expiry_date || '',
          medicines: {
            name: medicineName,
            category: medicine?.category || '',
            manufacturer: medicine?.manufacturer || '',
            strength: medicine?.strength || '',
            form: medicine?.form || ''
          }
        },
        created_at: new Date().toISOString()
      }
    })
  } catch (error) {
    console.error('خطأ في تحسين العناصر بأسماء الأدوية:', error)
    // Return items with basic structure if enhancement fails
    return items.map(item => ({
      id: `item_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      ...item,
      medicine_name: 'غير محدد',
      created_at: new Date().toISOString()
    }))
  }
}

// Function to fix existing localStorage data with medicine names
export const fixLocalStorageInvoiceItems = () => {
  try {
    console.log('🔧 بدء إصلاح بيانات الفواتير في localStorage...')

    const salesItems = JSON.parse(localStorage.getItem('sales_invoice_items') || '[]')
    const medicines = JSON.parse(localStorage.getItem('medicines') || '[]')
    const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')

    console.log(`📦 عدد عناصر الفواتير: ${salesItems.length}`)
    console.log(`💊 عدد الأدوية: ${medicines.length}`)
    console.log(`📋 عدد الدفعات: ${batches.length}`)

    let fixedCount = 0
    let notFoundCount = 0

    const fixedItems = salesItems.map((item: any) => {
      // Skip if already has proper medicine name structure
      if (item.medicine_batches?.medicines?.name && item.medicine_batches.medicines.name !== 'غير محدد') {
        return item
      }

      // Find medicine name from batch
      const batch = batches.find((b: any) => b.id === item.medicine_batch_id)
      const medicine = medicines.find((m: any) => m.id === batch?.medicine_id)

      if (medicine?.name) {
        fixedCount++
        console.log(`✅ إصلاح العنصر: ${medicine.name} (Batch: ${batch?.batch_code})`)

        return {
          ...item,
          medicine_name: medicine.name,
          medicineName: medicine.name, // Add both for compatibility
          medicine_batches: {
            batch_code: batch?.batch_code || item.batch_code || '',
            expiry_date: batch?.expiry_date || item.expiry_date || '',
            medicines: {
              name: medicine.name,
              category: medicine.category || '',
              manufacturer: medicine.manufacturer || '',
              strength: medicine.strength || '',
              form: medicine.form || ''
            }
          }
        }
      } else {
        notFoundCount++
        console.log(`⚠️ لم يتم العثور على الدواء للعنصر: ${item.medicine_batch_id}`)

        // Try to preserve any existing name
        const existingName = item.medicine_name || item.medicineName || 'غير محدد'
        return {
          ...item,
          medicine_name: existingName,
          medicineName: existingName,
          medicine_batches: {
            batch_code: batch?.batch_code || item.batch_code || '',
            expiry_date: batch?.expiry_date || item.expiry_date || '',
            medicines: {
              name: existingName,
              category: '',
              manufacturer: '',
              strength: '',
              form: ''
            }
          }
        }
      }
    })

    // Save fixed data back to localStorage
    localStorage.setItem('sales_invoice_items', JSON.stringify(fixedItems))

    console.log(`✅ تم إصلاح ${fixedCount} عنصر من أصل ${salesItems.length}`)
    console.log(`⚠️ لم يتم العثور على ${notFoundCount} عنصر`)

    return {
      success: true,
      fixedCount,
      notFoundCount,
      totalCount: salesItems.length
    }

  } catch (error) {
    console.error('❌ خطأ في إصلاح بيانات localStorage:', error)
    return { success: false, error }
  }
}

// Helper function to get medicine name from batch ID
export const getMedicineNameFromBatch = (batchId: string): string => {
  try {
    const medicines = JSON.parse(localStorage.getItem('medicines') || '[]')
    const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')

    const batch = batches.find((b: any) => b.id === batchId)
    const medicine = medicines.find((m: any) => m.id === batch?.medicine_id)

    return medicine?.name || 'غير محدد'
  } catch (error) {
    console.error('خطأ في الحصول على اسم الدواء:', error)
    return 'غير محدد'
  }
}

// Purchase operations
export const createPurchaseInvoice = async (invoiceData: {
  invoice_number: string
  supplier_id: string
  total_amount: number
  discount_amount: number
  final_amount: number
  payment_method: string
  payment_status: string
  notes?: string
  private_notes?: string
}) => {
  try {
    // Try Supabase first
    const { data, error } = await supabase
      .from('purchase_invoices')
      .insert([invoiceData])
      .select()
      .single()

    if (error) {
      console.warn('Supabase error for purchase invoice, using localStorage:', error)
      // Fallback to localStorage
      const invoiceId = `purchase_invoice_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      const invoice = {
        id: invoiceId,
        ...invoiceData,
        created_at: new Date().toISOString()
      }

      // Save to localStorage
      const existingInvoices = JSON.parse(localStorage.getItem('purchase_invoices') || '[]')
      existingInvoices.push(invoice)
      localStorage.setItem('purchase_invoices', JSON.stringify(existingInvoices))

      return { success: true, data: invoice }
    }

    return { success: true, data }
  } catch (error) {
    console.error('Error creating purchase invoice:', error)

    // Final fallback to localStorage
    try {
      const invoiceId = `purchase_invoice_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      const invoice = {
        id: invoiceId,
        ...invoiceData,
        created_at: new Date().toISOString()
      }

      const existingInvoices = JSON.parse(localStorage.getItem('purchase_invoices') || '[]')
      existingInvoices.push(invoice)
      localStorage.setItem('purchase_invoices', JSON.stringify(existingInvoices))

      return { success: true, data: invoice }
    } catch (localError) {
      console.error('LocalStorage fallback failed for purchase invoice:', localError)
      return { success: false, error: localError }
    }
  }
}

export const addPurchaseInvoiceItems = async (items: Array<{
  invoice_id: string
  medicine_id: string
  batch_code: string
  quantity: number
  unit_cost: number
  total_cost: number
  expiry_date: string
  medicine_name?: string
}>) => {
  try {
    const { data, error } = await supabase
      .from('purchase_invoice_items')
      .insert(items)
      .select()

    if (error) {
      console.warn('Supabase error for purchase invoice items, using localStorage:', error)
      // Fallback to localStorage
      const existingItems = JSON.parse(localStorage.getItem('purchase_invoice_items') || '[]')
      const newItems = items.map(item => ({
        id: `purchase_item_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        ...item,
        medicine_name: item.medicine_name || 'غير محدد',
        medicineName: item.medicine_name || 'غير محدد',
        created_at: new Date().toISOString()
      }))

      existingItems.push(...newItems)
      localStorage.setItem('purchase_invoice_items', JSON.stringify(existingItems))

      return { success: true, data: newItems }
    }

    return { success: true, data }
  } catch (error) {
    console.error('Error adding purchase invoice items:', error)

    // Final fallback to localStorage
    try {
      const existingItems = JSON.parse(localStorage.getItem('purchase_invoice_items') || '[]')
      const newItems = items.map(item => ({
        id: `purchase_item_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        ...item,
        medicine_name: item.medicine_name || 'غير محدد',
        medicineName: item.medicine_name || 'غير محدد',
        created_at: new Date().toISOString()
      }))

      existingItems.push(...newItems)
      localStorage.setItem('purchase_invoice_items', JSON.stringify(existingItems))

      return { success: true, data: newItems }
    } catch (localError) {
      console.error('LocalStorage fallback failed for purchase items:', localError)
      return { success: false, error: localError }
    }
  }
}

// Inventory movement operations
export const addInventoryMovement = async (movementData: {
  medicine_batch_id: string
  movement_type: 'in' | 'out' | 'adjustment'
  quantity: number
  reference_type: 'sale' | 'purchase' | 'return' | 'adjustment'
  reference_id?: string
  notes?: string
}) => {
  try {
    const { data, error } = await supabase
      .from('inventory_movements')
      .insert([movementData])
      .select()
      .single()

    if (error) {
      console.warn('Supabase error for inventory movement, using localStorage:', error)
      // Fallback to localStorage
      const movement = {
        id: `movement_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        ...movementData,
        created_at: new Date().toISOString()
      }

      const existingMovements = JSON.parse(localStorage.getItem('inventory_movements') || '[]')
      existingMovements.push(movement)
      localStorage.setItem('inventory_movements', JSON.stringify(existingMovements))

      return { success: true, data: movement }
    }

    return { success: true, data }
  } catch (error) {
    console.error('Error adding inventory movement:', error)

    // Final fallback to localStorage
    try {
      const movement = {
        id: `movement_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        ...movementData,
        created_at: new Date().toISOString()
      }

      const existingMovements = JSON.parse(localStorage.getItem('inventory_movements') || '[]')
      existingMovements.push(movement)
      localStorage.setItem('inventory_movements', JSON.stringify(existingMovements))

      return { success: true, data: movement }
    } catch (localError) {
      console.error('LocalStorage fallback failed for inventory movement:', localError)
      return { success: false, error: localError }
    }
  }
}

// Customer operations
export const getCustomers = async () => {
  try {
    const { data, error } = await supabase
      .from('customers')
      .select('*')
      .order('name')

    if (error) throw error
    return { success: true, data }
  } catch (error) {
    console.error('Error fetching customers:', error)
    return { success: false, error }
  }
}

export const addCustomer = async (customerData: {
  name: string
  phone?: string
  email?: string
  address?: string
  notes?: string
}) => {
  try {
    const { data, error } = await supabase
      .from('customers')
      .insert([customerData])
      .select()
      .single()

    if (error) throw error
    return { success: true, data }
  } catch (error) {
    console.error('Error adding customer:', error)
    return { success: false, error }
  }
}

// Supplier operations
export const getSuppliers = async () => {
  try {
    const { data, error } = await supabase
      .from('suppliers')
      .select('*')
      .order('name')

    if (error) throw error
    return { success: true, data }
  } catch (error) {
    console.error('Error fetching suppliers:', error)
    return { success: false, error }
  }
}

export const addSupplier = async (supplierData: {
  name: string
  contact_person?: string
  phone?: string
  email?: string
  address?: string
  notes?: string
}) => {
  try {
    const { data, error } = await supabase
      .from('suppliers')
      .insert([supplierData])
      .select()
      .single()

    if (error) throw error
    return { success: true, data }
  } catch (error) {
    console.error('Error adding supplier:', error)
    return { success: false, error }
  }
}

// Complete sales transaction with inventory update
export const completeSalesTransaction = async (
  invoiceData: any,
  items: any[]
) => {
  try {
    console.log('🔄 بدء معاملة المبيعات الكاملة...')
    console.log('📄 بيانات الفاتورة:', invoiceData)
    console.log('📦 العناصر:', items)

    // Start transaction by creating invoice
    console.log('📝 إنشاء الفاتورة...')
    const invoiceResult = await createSalesInvoice(invoiceData)
    console.log('📝 نتيجة إنشاء الفاتورة:', invoiceResult)

    if (!invoiceResult.success) {
      console.error('❌ فشل في إنشاء الفاتورة:', invoiceResult.error)
      throw new Error(`فشل في إنشاء الفاتورة: ${invoiceResult.error?.message || 'خطأ غير معروف'}`)
    }

    const invoiceId = invoiceResult.data.id
    console.log('✅ تم إنشاء الفاتورة بنجاح، ID:', invoiceId)

    // Process each item
    console.log('📦 معالجة عناصر الفاتورة...')
    const itemsToAdd = []

    for (const item of items) {
      console.log('📦 معالجة العنصر:', item)
      const batchId = item.medicine_batch_id || item.batchId

      // Prepare item for batch insert with medicine name
      itemsToAdd.push({
        invoice_id: invoiceId,
        medicine_batch_id: batchId,
        quantity: item.quantity,
        unit_price: item.unit_price || item.unitPrice,
        total_price: item.total_price || item.totalPrice,
        is_gift: item.is_gift || item.isGift || false,
        medicine_name: item.medicine_name || item.medicineName || 'غير محدد'
      })

      // Update batch quantity (only for non-gift items)
      if (!(item.is_gift || item.isGift)) {
        try {
          const currentBatch = await supabase
            .from('medicine_batches')
            .select('quantity')
            .eq('id', batchId)
            .single()

          if (currentBatch.data) {
            const newQuantity = Math.max(0, currentBatch.data.quantity - item.quantity)
            await updateBatchQuantity(batchId, newQuantity)
            console.log(`✅ تم تحديث كمية الدفعة ${batchId} إلى ${newQuantity}`)
          }
        } catch (batchError) {
          console.warn('تحذير: فشل في تحديث كمية الدفعة:', batchError)
        }
      }

      // Add inventory movement
      try {
        await addInventoryMovement({
          medicine_batch_id: batchId,
          movement_type: 'out',
          quantity: item.quantity,
          reference_type: 'sale',
          reference_id: invoiceId,
          notes: (item.is_gift || item.isGift) ? 'هدية' : undefined
        })
        console.log(`✅ تم إضافة حركة المخزون للدفعة ${batchId}`)
      } catch (movementError) {
        console.warn('تحذير: فشل في إضافة حركة المخزون:', movementError)
      }
    }

    // Add all invoice items in batch
    console.log('📝 إضافة عناصر الفاتورة...')
    const itemsResult = await addSalesInvoiceItems(itemsToAdd)
    if (!itemsResult.success) {
      console.warn('تحذير: فشل في إضافة عناصر الفاتورة:', itemsResult.error)
    } else {
      console.log('✅ تم إضافة جميع عناصر الفاتورة بنجاح')
    }

    // Add cash transaction if payment is cash
    if (invoiceData.payment_method === 'cash' && invoiceData.payment_status === 'paid') {
      try {
        await addCashTransaction({
          transaction_type: 'income',
          category: 'مبيعات',
          amount: invoiceData.final_amount,
          description: `فاتورة مبيعات رقم ${invoiceData.invoice_number}`,
          reference_type: 'sale',
          reference_id: invoiceId,
          payment_method: 'cash',
          notes: invoiceData.notes
        })
        console.log('✅ تم إضافة معاملة الصندوق')
      } catch (cashError) {
        console.warn('تحذير: فشل في إضافة معاملة الصندوق:', cashError)
      }
    }

    console.log('🎉 تمت معاملة المبيعات بنجاح!')
    return { success: true, data: { invoiceId } }
  } catch (error) {
    console.error('❌ خطأ في إتمام معاملة المبيعات:', error)
    return { success: false, error }
  }
}

// Returns operations
export const getSalesInvoices = async () => {
  try {
    const { data, error } = await supabase
      .from('sales_invoices')
      .select(`
        *,
        customers (name, phone, address),
        sales_invoice_items (
          *,
          medicine_batches (
            batch_code,
            expiry_date,
            medicines (name, category, manufacturer, strength, form)
          )
        )
      `)
      .order('created_at', { ascending: false })

    if (error) {
      console.warn('Supabase error for sales invoices, using localStorage fallback:', error)
      // Fallback to localStorage
      const localInvoices = JSON.parse(localStorage.getItem('sales_invoices') || '[]')
      const localItems = JSON.parse(localStorage.getItem('sales_invoice_items') || '[]')
      const medicines = JSON.parse(localStorage.getItem('medicines') || '[]')
      const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')

      // Combine invoices with their items and ensure medicine names are available
      const invoicesWithItems = localInvoices.map((invoice: any) => {
        const items = localItems.filter((item: any) => item.invoice_id === invoice.id)

        // Enhance items with medicine names if not already present
        const enhancedItems = items.map((item: any) => {
          if (item.medicine_batches?.medicines?.name) {
            return item // Already has medicine name
          }

          // Find medicine name from batch
          const batch = batches.find((b: any) => b.id === item.medicine_batch_id)
          const medicine = medicines.find((m: any) => m.id === batch?.medicine_id)

          return {
            ...item,
            medicine_name: medicine?.name || item.medicine_name || 'غير محدد',
            medicine_batches: {
              batch_code: batch?.batch_code || '',
              expiry_date: batch?.expiry_date || '',
              medicines: {
                name: medicine?.name || item.medicine_name || 'غير محدد',
                category: medicine?.category || '',
                manufacturer: medicine?.manufacturer || '',
                strength: medicine?.strength || '',
                form: medicine?.form || ''
              }
            }
          }
        })

        return {
          ...invoice,
          sales_invoice_items: enhancedItems
        }
      })

      return { success: true, data: invoicesWithItems }
    }

    return { success: true, data }
  } catch (error) {
    console.error('Error fetching sales invoices:', error)

    // Final fallback to localStorage
    try {
      const localInvoices = JSON.parse(localStorage.getItem('sales_invoices') || '[]')
      const localItems = JSON.parse(localStorage.getItem('sales_invoice_items') || '[]')
      const medicines = JSON.parse(localStorage.getItem('medicines') || '[]')
      const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')

      // Combine invoices with their items and ensure medicine names
      const invoicesWithItems = localInvoices.map((invoice: any) => {
        const items = localItems.filter((item: any) => item.invoice_id === invoice.id)

        // Enhance items with medicine names if not already present
        const enhancedItems = items.map((item: any) => {
          if (item.medicine_batches?.medicines?.name) {
            return item // Already has medicine name
          }

          // Find medicine name from batch
          const batch = batches.find((b: any) => b.id === item.medicine_batch_id)
          const medicine = medicines.find((m: any) => m.id === batch?.medicine_id)

          return {
            ...item,
            medicine_name: medicine?.name || item.medicine_name || 'غير محدد',
            medicine_batches: {
              batch_code: batch?.batch_code || '',
              expiry_date: batch?.expiry_date || '',
              medicines: {
                name: medicine?.name || item.medicine_name || 'غير محدد',
                category: medicine?.category || '',
                manufacturer: medicine?.manufacturer || '',
                strength: medicine?.strength || '',
                form: medicine?.form || ''
              }
            }
          }
        })

        return {
          ...invoice,
          sales_invoice_items: enhancedItems
        }
      })

      return { success: true, data: invoicesWithItems }
    } catch (localError) {
      console.error('LocalStorage fallback failed:', localError)
      return { success: false, error }
    }
  }
}

// Get single sales invoice with full details for printing
export const getSalesInvoiceForPrint = async (invoiceId: string) => {
  try {
    const { data, error } = await supabase
      .from('sales_invoices')
      .select(`
        *,
        customers (name, phone, address),
        sales_invoice_items (
          *,
          medicine_batches (
            batch_code,
            expiry_date,
            medicines (name, category, manufacturer, strength, form)
          )
        )
      `)
      .eq('id', invoiceId)
      .single()

    if (error) {
      console.warn('Supabase error for single invoice, using localStorage fallback:', error)
      // Fallback to localStorage
      const localInvoices = JSON.parse(localStorage.getItem('sales_invoices') || '[]')
      const localItems = JSON.parse(localStorage.getItem('sales_invoice_items') || '[]')
      const medicines = JSON.parse(localStorage.getItem('medicines') || '[]')
      const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')

      const invoice = localInvoices.find((inv: any) => inv.id === invoiceId)
      if (invoice) {
        const items = localItems.filter((item: any) => item.invoice_id === invoiceId)

        // Enhance items with medicine names - FORCE REFRESH
        console.log('🔧 بدء تحسين عناصر الفاتورة للطباعة...')
        console.log('📦 عدد العناصر:', items.length)
        console.log('💊 عدد الأدوية المتاحة:', medicines.length)
        console.log('📋 عدد الدفعات المتاحة:', batches.length)

        const itemsWithNames = items.map((item: any, index: number) => {
          console.log(`\n--- العنصر ${index + 1} ---`)
          console.log('البيانات الأصلية:', item)

          // Find medicine name from batch - ALWAYS recalculate
          const batch = batches.find((b: any) => b.id === item.medicine_batch_id)
          console.log('الدفعة الموجودة:', batch)

          const medicine = medicines.find((m: any) => m.id === batch?.medicine_id)
          console.log('الدواء الموجود:', medicine)

          // Get the best available medicine name
          const medicineName = medicine?.name || 'غير محدد'
          console.log('اسم الدواء المحسوب:', medicineName)

          const enhancedItem = {
            ...item,
            medicine_name: medicineName,
            medicineName: medicineName, // Add both for compatibility
            medicine_batches: {
              id: batch?.id,
              batch_code: batch?.batch_code || item.batch_code || '',
              expiry_date: batch?.expiry_date || item.expiry_date || '',
              medicine_id: batch?.medicine_id,
              medicines: {
                id: medicine?.id,
                name: medicineName,
                category: medicine?.category || '',
                manufacturer: medicine?.manufacturer || '',
                strength: medicine?.strength || '',
                form: medicine?.form || ''
              }
            }
          }

          console.log('العنصر المحسن:', enhancedItem)
          return enhancedItem
        })

        console.log('✅ تم تحسين جميع العناصر')
        console.log('النتيجة النهائية:', itemsWithNames)

        return {
          success: true,
          data: {
            ...invoice,
            sales_invoice_items: itemsWithNames
          }
        }
      }
    }

    return { success: true, data }
  } catch (error) {
    console.error('Error fetching sales invoice for print:', error)
    return { success: false, error }
  }
}

// Get single purchase invoice with full details for printing
export const getPurchaseInvoiceForPrint = async (invoiceId: string) => {
  try {
    const { data, error } = await supabase
      .from('purchase_invoices')
      .select(`
        *,
        suppliers (name, contact_person, phone, address),
        purchase_invoice_items (
          *,
          medicines (name, category, manufacturer, strength, form)
        )
      `)
      .eq('id', invoiceId)
      .single()

    if (error) {
      console.warn('Supabase error for single purchase invoice, using localStorage fallback:', error)
      // Fallback to localStorage
      const localInvoices = JSON.parse(localStorage.getItem('purchase_invoices') || '[]')
      const localItems = JSON.parse(localStorage.getItem('purchase_invoice_items') || '[]')

      const invoice = localInvoices.find((inv: any) => inv.id === invoiceId)
      if (invoice) {
        const items = localItems.filter((item: any) => item.invoice_id === invoiceId)

        // Enhance items with medicine names
        console.log('🔧 بدء تحسين عناصر فاتورة المشتريات للطباعة...')
        console.log('📦 عدد العناصر:', items.length)

        const itemsWithNames = items.map((item: any, index: number) => {
          console.log(`\n--- العنصر ${index + 1} ---`)
          console.log('البيانات الأصلية:', item)

          // Use existing medicine name if available
          const medicineName = item.medicine_name || item.medicineName || 'غير محدد'
          console.log('اسم الدواء:', medicineName)

          const enhancedItem = {
            ...item,
            medicine_name: medicineName,
            medicineName: medicineName,
            medicines: {
              name: medicineName,
              category: item.category || '',
              manufacturer: item.manufacturer || '',
              strength: item.strength || '',
              form: item.form || ''
            }
          }

          console.log('العنصر المحسن:', enhancedItem)
          return enhancedItem
        })

        console.log('✅ تم تحسين جميع عناصر المشتريات')

        return {
          success: true,
          data: {
            ...invoice,
            purchase_invoice_items: itemsWithNames
          }
        }
      }
    }

    return { success: true, data }
  } catch (error) {
    console.error('Error fetching purchase invoice for print:', error)
    return { success: false, error }
  }
}

export const getPurchaseInvoices = async () => {
  try {
    const { data, error } = await supabase
      .from('purchase_invoices')
      .select(`
        *,
        suppliers (name, contact_person, phone, address),
        purchase_invoice_items (
          *,
          medicines (name, category, manufacturer, strength, form)
        )
      `)
      .order('created_at', { ascending: false })

    if (error) {
      console.warn('Supabase error for purchase invoices, using localStorage fallback:', error)
      // Fallback to localStorage
      const localInvoices = JSON.parse(localStorage.getItem('purchase_invoices') || '[]')
      const localItems = JSON.parse(localStorage.getItem('purchase_invoice_items') || '[]')

      // Combine invoices with their items
      const invoicesWithItems = localInvoices.map((invoice: any) => ({
        ...invoice,
        purchase_invoice_items: localItems.filter((item: any) => item.invoice_id === invoice.id)
      }))

      return { success: true, data: invoicesWithItems }
    }

    return { success: true, data }
  } catch (error) {
    console.error('Error fetching purchase invoices:', error)

    // Final fallback to localStorage
    try {
      const localInvoices = JSON.parse(localStorage.getItem('purchase_invoices') || '[]')
      const localItems = JSON.parse(localStorage.getItem('purchase_invoice_items') || '[]')

      // Combine invoices with their items
      const invoicesWithItems = localInvoices.map((invoice: any) => ({
        ...invoice,
        purchase_invoice_items: localItems.filter((item: any) => item.invoice_id === invoice.id)
      }))

      return { success: true, data: invoicesWithItems }
    } catch (localError) {
      console.error('LocalStorage fallback failed:', localError)
      return { success: false, error }
    }
  }
}

export const createSalesReturn = async (returnData: {
  return_number: string
  original_invoice_id: string
  customer_id?: string
  customer_name?: string
  total_amount: number
  reason: string
  status: string
  notes?: string
}) => {
  try {
    // Try Supabase first
    const { data, error } = await supabase
      .from('sales_returns')
      .insert([returnData])
      .select()
      .single()

    if (error) throw error
    return { success: true, data }
  } catch (error) {
    console.warn('Supabase sales return failed, using localStorage fallback:', error)

    // Fallback to localStorage
    try {
      const returnWithId = {
        ...returnData,
        id: `sr_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        created_at: new Date().toISOString()
      }

      const existingReturns = JSON.parse(localStorage.getItem('sales_returns') || '[]')
      existingReturns.push(returnWithId)
      localStorage.setItem('sales_returns', JSON.stringify(existingReturns))

      console.log('Sales return saved to localStorage:', returnWithId)
      console.log('Total sales returns in localStorage:', existingReturns.length)

      return { success: true, data: returnWithId }
    } catch (fallbackError) {
      console.error('Error creating sales return (fallback):', fallbackError)
      return { success: false, error: fallbackError }
    }
  }
}

export const createPurchaseReturn = async (returnData: {
  return_number: string
  original_invoice_id: string
  supplier_id: string
  total_amount: number
  reason: string
  status: string
  notes?: string
}) => {
  try {
    // Try Supabase first
    const { data, error } = await supabase
      .from('purchase_returns')
      .insert([returnData])
      .select()
      .single()

    if (error) throw error
    return { success: true, data }
  } catch (error) {
    console.warn('Supabase purchase return failed, using localStorage fallback:', error)

    // Fallback to localStorage
    try {
      const returnWithId = {
        ...returnData,
        id: `pr_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        created_at: new Date().toISOString()
      }

      const existingReturns = JSON.parse(localStorage.getItem('purchase_returns') || '[]')
      existingReturns.push(returnWithId)
      localStorage.setItem('purchase_returns', JSON.stringify(existingReturns))

      console.log('Purchase return saved to localStorage:', returnWithId)
      console.log('Total purchase returns in localStorage:', existingReturns.length)

      return { success: true, data: returnWithId }
    } catch (fallbackError) {
      console.error('Error creating purchase return (fallback):', fallbackError)
      return { success: false, error: fallbackError }
    }
  }
}

export const addReturnItems = async (items: Array<{
  return_id: string
  return_type: 'sales' | 'purchase'
  medicine_batch_id?: string
  medicine_id?: string
  quantity: number
  unit_price: number
  total_price: number
}>) => {
  try {
    // Try Supabase first
    const tableName = items[0]?.return_type === 'sales' ? 'sales_return_items' : 'purchase_return_items'
    const { data, error } = await supabase
      .from(tableName)
      .insert(items.map(item => ({
        return_id: item.return_id,
        medicine_batch_id: item.medicine_batch_id,
        medicine_id: item.medicine_id,
        quantity: item.quantity,
        unit_price: item.unit_price,
        total_price: item.total_price
      })))
      .select()

    if (error) throw error
    return { success: true, data }
  } catch (error) {
    console.warn('Supabase return items failed, using localStorage fallback:', error)

    // Fallback to localStorage
    try {
      const storageKey = items[0]?.return_type === 'sales' ? 'sales_return_items' : 'purchase_return_items'
      const itemsWithIds = items.map(item => ({
        ...item,
        id: `ri_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        created_at: new Date().toISOString()
      }))

      const existingItems = JSON.parse(localStorage.getItem(storageKey) || '[]')
      existingItems.push(...itemsWithIds)
      localStorage.setItem(storageKey, JSON.stringify(existingItems))

      return { success: true, data: itemsWithIds }
    } catch (fallbackError) {
      console.error('Error adding return items (fallback):', fallbackError)
      return { success: false, error: fallbackError }
    }
  }
}

export const processReturn = async (
  returnType: 'sales' | 'purchase',
  returnData: any,
  items: any[]
) => {
  try {
    // Create return record
    const returnResult = returnType === 'sales'
      ? await createSalesReturn(returnData)
      : await createPurchaseReturn(returnData)

    if (!returnResult.success) throw new Error('Failed to create return')

    const returnId = returnResult.data.id

    // Add return items
    const returnItems = items.map(item => ({
      return_id: returnId,
      return_type: returnType,
      medicine_batch_id: item.batchId,
      medicine_id: item.medicineId,
      quantity: item.quantity,
      unit_price: item.unitPrice,
      total_price: item.totalPrice
    }))

    await addReturnItems(returnItems)

    // Try to update inventory (skip if Supabase is not available)
    try {
      // Update inventory for sales returns (add back to stock)
      if (returnType === 'sales') {
        for (const item of items) {
          if (item.batchId) {
            try {
              // Get current batch quantity
              const { data: batch } = await supabase
                .from('medicine_batches')
                .select('quantity')
                .eq('id', item.batchId)
                .single()

              if (batch) {
                // Add returned quantity back to stock
                await updateBatchQuantity(item.batchId, batch.quantity + item.quantity)
              }

              // Add inventory movement
              await addInventoryMovement({
                medicine_batch_id: item.batchId,
                movement_type: 'in',
                quantity: item.quantity,
                reference_type: 'return',
                reference_id: returnId,
                notes: `مرتجع مبيعات - ${returnData.reason}`
              })
            } catch (inventoryError) {
              console.warn('Failed to update inventory for item:', item.batchId, inventoryError)
            }
          }
        }
      }

      // Update inventory for purchase returns (remove from stock)
      if (returnType === 'purchase') {
        for (const item of items) {
          if (item.batchId) {
            try {
              // Get current batch quantity
              const { data: batch } = await supabase
                .from('medicine_batches')
                .select('quantity')
                .eq('id', item.batchId)
                .single()

              if (batch) {
                // Remove returned quantity from stock
                const newQuantity = Math.max(0, batch.quantity - item.quantity)
                await updateBatchQuantity(item.batchId, newQuantity)
              }

              // Add inventory movement
              await addInventoryMovement({
                medicine_batch_id: item.batchId,
                movement_type: 'out',
                quantity: item.quantity,
                reference_type: 'return',
                reference_id: returnId,
                notes: `مرتجع مشتريات - ${returnData.reason}`
              })
            } catch (inventoryError) {
              console.warn('Failed to update inventory for item:', item.batchId, inventoryError)
            }
          }
        }
      }
    } catch (inventoryError) {
      console.warn('Inventory update failed, but return was created successfully:', inventoryError)
    }

    return { success: true, data: { returnId } }
  } catch (error) {
    console.error('Error processing return:', error)
    return { success: false, error }
  }
}

export const getReturns = async () => {
  // Always try localStorage first for faster response
  try {
    const salesReturns = JSON.parse(localStorage.getItem('sales_returns') || '[]')
    const purchaseReturns = JSON.parse(localStorage.getItem('purchase_returns') || '[]')
    const customers = JSON.parse(localStorage.getItem('customers') || '[]')
    const suppliers = JSON.parse(localStorage.getItem('suppliers') || '[]')

    console.log('Loading returns from localStorage:', {
      salesReturns: salesReturns.length,
      purchaseReturns: purchaseReturns.length,
      customers: customers.length,
      suppliers: suppliers.length
    })

    // Enrich sales returns with customer data
    const enrichedSalesReturns = salesReturns.map((returnItem: any) => {
      const customer = customers.find((c: any) => c.id === returnItem.customer_id)
      console.log(`Enriching sales return ${returnItem.id}:`, {
        original_items: returnItem.return_items,
        items_count: returnItem.return_items?.length || 0
      })
      return {
        ...returnItem,
        return_type: 'sales',
        customers: customer ? {
          name: customer.name,
          phone: customer.phone,
          address: customer.address
        } : null,
        customer_name: customer?.name || returnItem.customer_name || 'عميل غير محدد',
        // تأكد من وجود المواد
        return_items: returnItem.return_items || []
      }
    })

    // Enrich purchase returns with supplier data
    const enrichedPurchaseReturns = purchaseReturns.map((returnItem: any) => {
      const supplier = suppliers.find((s: any) => s.id === returnItem.supplier_id)
      console.log(`Enriching purchase return ${returnItem.id}:`, {
        original_items: returnItem.return_items,
        items_count: returnItem.return_items?.length || 0
      })
      return {
        ...returnItem,
        return_type: 'purchase',
        suppliers: supplier ? {
          name: supplier.name,
          phone: supplier.phone,
          address: supplier.address
        } : null,
        supplier_name: supplier?.name || returnItem.supplier_name || 'مورد غير محدد',
        // تأكد من وجود المواد
        return_items: returnItem.return_items || []
      }
    })

    // If we have local data, return it immediately
    if (enrichedSalesReturns.length > 0 || enrichedPurchaseReturns.length > 0) {
      const allReturns = [
        ...enrichedSalesReturns,
        ...enrichedPurchaseReturns
      ].sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())

      console.log('Returning enriched returns from localStorage:', allReturns.slice(0, 2))
      return { success: true, data: allReturns }
    }
  } catch (localError) {
    console.warn('Error reading from localStorage:', localError)
  }

  // If no local data, try Supabase
  try {
    console.log('Trying Supabase for returns...')
    const [salesReturns, purchaseReturns] = await Promise.all([
      supabase
        .from('sales_returns')
        .select(`
          *,
          customers (name, phone),
          sales_return_items (
            *,
            medicine_batches (
              batch_code,
              medicines (name)
            )
          )
        `)
        .order('created_at', { ascending: false }),

      supabase
        .from('purchase_returns')
        .select(`
          *,
          suppliers (name, contact_person),
          purchase_return_items (
            *,
            medicines (name)
          )
        `)
        .order('created_at', { ascending: false })
    ])

    const allReturns = [
      ...(salesReturns.data || []).map(item => ({
        ...item,
        return_type: 'sales',
        customer_name: item.customers?.name || 'عميل غير محدد'
      })),
      ...(purchaseReturns.data || []).map(item => ({
        ...item,
        return_type: 'purchase',
        supplier_name: item.suppliers?.name || 'مورد غير محدد'
      }))
    ].sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())

    console.log('Returning returns from Supabase:', allReturns.slice(0, 2))
    return { success: true, data: allReturns }
  } catch (error) {
    console.warn('Supabase returns failed, returning empty array:', error)

    // Return empty array if both localStorage and Supabase fail
    return { success: true, data: [] }
  }
}

// Get return by ID with full details
export const getReturnById = async (returnId: string) => {
  try {
    // Try localStorage first
    const salesReturns = JSON.parse(localStorage.getItem('sales_returns') || '[]')
    const purchaseReturns = JSON.parse(localStorage.getItem('purchase_returns') || '[]')
    const customers = JSON.parse(localStorage.getItem('customers') || '[]')
    const suppliers = JSON.parse(localStorage.getItem('suppliers') || '[]')

    let foundReturn = salesReturns.find((r: any) => r.id === returnId)
    let returnType = 'sales'

    if (!foundReturn) {
      foundReturn = purchaseReturns.find((r: any) => r.id === returnId)
      returnType = 'purchase'
    }

    if (foundReturn) {
      // Enrich with customer/supplier data
      if (returnType === 'sales') {
        const customer = customers.find((c: any) => c.id === foundReturn.customer_id)
        foundReturn = {
          ...foundReturn,
          return_type: 'sales',
          customers: customer ? {
            name: customer.name,
            phone: customer.phone,
            address: customer.address
          } : null,
          customer_name: customer?.name || foundReturn.customer_name || 'عميل غير محدد'
        }
      } else {
        const supplier = suppliers.find((s: any) => s.id === foundReturn.supplier_id)
        foundReturn = {
          ...foundReturn,
          return_type: 'purchase',
          suppliers: supplier ? {
            name: supplier.name,
            phone: supplier.phone,
            address: supplier.address
          } : null,
          supplier_name: supplier?.name || foundReturn.supplier_name || 'مورد غير محدد'
        }
      }

      console.log('Found enriched return in localStorage:', foundReturn)
      return { success: true, data: foundReturn }
    }

    // If not in localStorage, try Supabase
    if (!supabase) {
      console.warn('Supabase not available, return not found')
      return { success: false, error: 'Return not found' }
    }

    // Try sales returns first
    const { data: salesReturn, error: salesError } = await supabase
      .from('sales_returns')
      .select(`
        *,
        customers (name, phone, address),
        sales_return_items (
          *,
          medicine_batches (
            *,
            medicines (name)
          )
        )
      `)
      .eq('id', returnId)
      .single()

    if (salesReturn && !salesError) {
      const returnData = {
        ...salesReturn,
        return_type: 'sales',
        return_items: salesReturn.sales_return_items || []
      }
      console.log('Found sales return in Supabase:', returnData)
      return { success: true, data: returnData }
    }

    // Try purchase returns
    const { data: purchaseReturn, error: purchaseError } = await supabase
      .from('purchase_returns')
      .select(`
        *,
        suppliers (name, phone, address),
        purchase_return_items (
          *,
          medicines (name)
        )
      `)
      .eq('id', returnId)
      .single()

    if (purchaseReturn && !purchaseError) {
      const returnData = {
        ...purchaseReturn,
        return_type: 'purchase',
        return_items: purchaseReturn.purchase_return_items || []
      }
      console.log('Found purchase return in Supabase:', returnData)
      return { success: true, data: returnData }
    }

    console.warn('Return not found:', returnId)
    return { success: false, error: 'Return not found' }
  } catch (error) {
    console.error('Error getting return by ID:', error)
    return { success: false, error }
  }
}

// Complete purchase transaction with inventory update
export const completePurchaseTransaction = async (
  invoiceData: any,
  items: any[]
) => {
  try {
    // Create purchase invoice
    const invoiceResult = await createPurchaseInvoice(invoiceData)
    if (!invoiceResult.success) throw new Error('Failed to create purchase invoice')

    const invoiceId = invoiceResult.data.id

    // Process each item
    for (const item of items) {
      let medicineId = item.medicineId

      // If medicine doesn't exist, create it
      if (!medicineId) {
        const newMedicineResult = await addMedicine({
          name: item.medicineName,
          category: item.category || 'أخرى',
          manufacturer: item.manufacturer || '',
          active_ingredient: item.activeIngredient || '',
          strength: item.strength || '',
          form: item.form || 'tablet',
          unit_price: item.unitCost,
          selling_price: item.sellingPrice || item.unitCost * 1.5
        })

        if (newMedicineResult.success) {
          medicineId = newMedicineResult.data.id
        } else {
          console.error('Failed to create medicine:', newMedicineResult.error)
          continue
        }
      }

      // Add purchase invoice item
      await addPurchaseInvoiceItems([{
        invoice_id: invoiceId,
        medicine_id: medicineId,
        batch_code: item.batchCode,
        quantity: item.quantity,
        unit_cost: item.unitCost,
        total_cost: item.totalCost,
        expiry_date: item.expiryDate,
        medicine_name: item.medicineName || 'غير محدد'
      }])

      // Create or update medicine batch
      const batchResult = await addMedicineBatch({
        medicine_id: medicineId,
        batch_code: item.batchCode,
        expiry_date: item.expiryDate,
        quantity: item.quantity,
        cost_price: item.unitCost,
        selling_price: item.sellingPrice || item.unitCost * 1.5, // Default markup
        supplier_id: invoiceData.supplier_id
      })

      if (batchResult.success) {
        // Add inventory movement
        await addInventoryMovement({
          medicine_batch_id: batchResult.data.id,
          movement_type: 'in',
          quantity: item.quantity,
          reference_type: 'purchase',
          reference_id: invoiceId
        })
      }
    }

    // Add cash transaction if payment is cash
    if (invoiceData.payment_method === 'cash' && invoiceData.payment_status === 'paid') {
      await addCashTransaction({
        transaction_type: 'expense',
        category: 'مشتريات',
        amount: invoiceData.final_amount,
        description: `فاتورة مشتريات رقم ${invoiceData.invoice_number}`,
        reference_type: 'purchase',
        reference_id: invoiceId,
        payment_method: 'cash',
        notes: invoiceData.notes
      })
    }

    return { success: true, data: { invoiceId } }
  } catch (error) {
    console.error('Error completing purchase transaction:', error)
    return { success: false, error }
  }
}

// Cash Box Operations
export const addCashTransaction = async (transactionData: {
  transaction_type: 'income' | 'expense'
  category: string
  amount: number
  description: string
  reference_type?: string
  reference_id?: string
  payment_method: string
  notes?: string
}) => {
  try {
    // Try Supabase first
    const { data, error } = await supabase
      .from('cash_transactions')
      .insert([transactionData])
      .select()
      .single()

    if (error) throw error
    return { success: true, data }
  } catch (error) {
    console.warn('Supabase cash transaction failed, using localStorage fallback:', error)

    // Fallback to localStorage
    try {
      const transactionWithId = {
        ...transactionData,
        id: `ct_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        created_at: new Date().toISOString()
      }

      const existingTransactions = JSON.parse(localStorage.getItem('cash_transactions') || '[]')
      existingTransactions.push(transactionWithId)
      localStorage.setItem('cash_transactions', JSON.stringify(existingTransactions))

      console.log('Cash transaction saved to localStorage:', transactionWithId)
      console.log('Total cash transactions in localStorage:', existingTransactions.length)

      return { success: true, data: transactionWithId }
    } catch (fallbackError) {
      console.error('Error adding cash transaction (fallback):', fallbackError)
      return { success: false, error: fallbackError }
    }
  }
}

export const getCashTransactions = async (filters?: {
  start_date?: string
  end_date?: string
  transaction_type?: string
  category?: string
}) => {
  // Always try localStorage first for faster response
  try {
    const transactions = JSON.parse(localStorage.getItem('cash_transactions') || '[]')

    // If we have local data, filter and return it
    if (transactions.length > 0) {
      console.log('Loading cash transactions from localStorage:', transactions.length)

      let filteredTransactions = transactions

      if (filters?.start_date) {
        filteredTransactions = filteredTransactions.filter(t => t.created_at >= filters.start_date)
      }
      if (filters?.end_date) {
        filteredTransactions = filteredTransactions.filter(t => t.created_at <= filters.end_date)
      }
      if (filters?.transaction_type) {
        filteredTransactions = filteredTransactions.filter(t => t.transaction_type === filters.transaction_type)
      }
      if (filters?.category) {
        filteredTransactions = filteredTransactions.filter(t => t.category === filters.category)
      }

      // Sort by created_at descending
      filteredTransactions.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())

      return { success: true, data: filteredTransactions }
    }
  } catch (localError) {
    console.warn('Error reading cash transactions from localStorage:', localError)
  }

  // If no local data, try Supabase
  try {
    console.log('Trying Supabase for cash transactions...')
    let query = supabase
      .from('cash_transactions')
      .select('*')
      .order('created_at', { ascending: false })

    if (filters?.start_date) {
      query = query.gte('created_at', filters.start_date)
    }
    if (filters?.end_date) {
      query = query.lte('created_at', filters.end_date)
    }
    if (filters?.transaction_type) {
      query = query.eq('transaction_type', filters.transaction_type)
    }
    if (filters?.category) {
      query = query.eq('category', filters.category)
    }

    const { data, error } = await query

    if (error) throw error
    return { success: true, data }
  } catch (error) {
    console.warn('Supabase cash transactions failed, returning empty array:', error)
    return { success: true, data: [] }
  }
}

export const getCashBalance = async () => {
  // Try localStorage first
  try {
    const transactions = JSON.parse(localStorage.getItem('cash_transactions') || '[]')

    if (transactions.length > 0) {
      console.log('Calculating cash balance from localStorage:', transactions.length, 'transactions')

      const balance = transactions.reduce((total: number, transaction: any) => {
        return transaction.transaction_type === 'income'
          ? total + transaction.amount
          : total - transaction.amount
      }, 0)

      return { success: true, data: balance }
    }
  } catch (localError) {
    console.warn('Error calculating balance from localStorage:', localError)
  }

  // If no local data, try Supabase
  try {
    console.log('Trying Supabase for cash balance...')
    const { data, error } = await supabase
      .from('cash_transactions')
      .select('transaction_type, amount')

    if (error) throw error

    const balance = data.reduce((total, transaction) => {
      return transaction.transaction_type === 'income'
        ? total + transaction.amount
        : total - transaction.amount
    }, 0)

    return { success: true, data: balance }
  } catch (error) {
    console.warn('Supabase cash balance failed, returning 0:', error)
    return { success: true, data: 0 }
  }
}

// Customer and Supplier Debts
export const getCustomerDebts = async () => {
  // Try localStorage first
  try {
    const salesInvoices = JSON.parse(localStorage.getItem('sales_invoices') || '[]')

    if (salesInvoices.length > 0) {
      console.log('Loading customer debts from localStorage:', salesInvoices.length, 'invoices')

      // Filter for pending payments only
      const pendingInvoices = salesInvoices.filter((invoice: any) =>
        invoice.payment_status === 'pending' || invoice.payment_status === 'partial'
      )

      console.log('Found customer debts:', pendingInvoices.length)
      return { success: true, data: pendingInvoices }
    } else {
      // Create sample debt data if no invoices exist
      console.log('No sales invoices found, creating sample customer debts')
      const sampleDebts = [
        {
          id: 'debt_1',
          invoice_number: 'INV-001',
          customer_id: 'cust_1',
          customer_name: 'أحمد محمد علي',
          final_amount: 150000,
          payment_status: 'pending',
          created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          customers: { name: 'أحمد محمد علي', phone: '07901111111' }
        },
        {
          id: 'debt_2',
          invoice_number: 'INV-003',
          customer_id: 'cust_2',
          customer_name: 'فاطمة حسن محمد',
          final_amount: 85000,
          payment_status: 'partial',
          created_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
          customers: { name: 'فاطمة حسن محمد', phone: '07802222222' }
        }
      ]
      return { success: true, data: sampleDebts }
    }
  } catch (localError) {
    console.warn('Error reading customer debts from localStorage:', localError)
  }

  // If no local data, try Supabase
  try {
    console.log('Trying Supabase for customer debts...')
    const { data, error } = await supabase
      .from('sales_invoices')
      .select(`
        id,
        invoice_number,
        customer_id,
        customer_name,
        final_amount,
        payment_status,
        created_at,
        customers (name, phone)
      `)
      .eq('payment_status', 'pending')
      .order('created_at', { ascending: false })

    if (error) throw error
    return { success: true, data }
  } catch (error) {
    console.warn('Supabase customer debts failed, returning empty array:', error)
    return { success: true, data: [] }
  }
}

export const getSupplierDebts = async () => {
  // Try localStorage first
  try {
    const purchaseInvoices = JSON.parse(localStorage.getItem('purchase_invoices') || '[]')

    if (purchaseInvoices.length > 0) {
      console.log('Loading supplier debts from localStorage:', purchaseInvoices.length, 'invoices')

      // Filter for pending payments only
      const pendingInvoices = purchaseInvoices.filter((invoice: any) =>
        invoice.payment_status === 'pending' || invoice.payment_status === 'partial'
      )

      console.log('Found supplier debts:', pendingInvoices.length)
      return { success: true, data: pendingInvoices }
    } else {
      // Create sample debt data if no invoices exist
      console.log('No purchase invoices found, creating sample supplier debts')
      const sampleDebts = [
        {
          id: 'debt_3',
          invoice_number: 'PUR-001',
          supplier_id: 'sup_1',
          final_amount: 2500000,
          payment_status: 'pending',
          created_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
          suppliers: { name: 'شركة الأدوية العراقية', contact_person: 'أحمد محمد', phone: '07901234567' }
        },
        {
          id: 'debt_4',
          invoice_number: 'PUR-004',
          supplier_id: 'sup_2',
          final_amount: 1800000,
          payment_status: 'partial',
          created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          suppliers: { name: 'شركة بغداد للأدوية', contact_person: 'فاطمة علي', phone: '07801234567' }
        }
      ]
      return { success: true, data: sampleDebts }
    }
  } catch (localError) {
    console.warn('Error reading supplier debts from localStorage:', localError)
  }

  // If no local data, try Supabase
  try {
    console.log('Trying Supabase for supplier debts...')
    const { data, error } = await supabase
      .from('purchase_invoices')
      .select(`
        id,
        invoice_number,
        supplier_id,
        final_amount,
        payment_status,
        created_at,
        suppliers (name, contact_person, phone)
      `)
      .eq('payment_status', 'pending')
      .order('created_at', { ascending: false })

    if (error) throw error
    return { success: true, data }
  } catch (error) {
    console.warn('Supabase supplier debts failed, returning empty array:', error)
    return { success: true, data: [] }
  }
}

export const updatePaymentStatus = async (
  invoiceType: 'sales' | 'purchase',
  invoiceId: string,
  paymentStatus: string,
  paidAmount?: number
) => {
  try {
    // Try Supabase first
    const tableName = invoiceType === 'sales' ? 'sales_invoices' : 'purchase_invoices'

    const { data, error } = await supabase
      .from(tableName)
      .update({
        payment_status: paymentStatus,
        ...(paidAmount && { paid_amount: paidAmount })
      })
      .eq('id', invoiceId)
      .select()
      .single()

    if (error) throw error

    // Add cash transaction if payment is completed
    if (paymentStatus === 'paid' && paidAmount) {
      const transactionType = invoiceType === 'sales' ? 'income' : 'expense'
      const category = invoiceType === 'sales' ? 'مبيعات' : 'مشتريات'

      await addCashTransaction({
        transaction_type: transactionType,
        category,
        amount: paidAmount,
        description: `دفع فاتورة ${invoiceType === 'sales' ? 'مبيعات' : 'مشتريات'} رقم ${data.invoice_number}`,
        reference_type: invoiceType,
        reference_id: invoiceId,
        payment_method: 'cash'
      })
    }

    return { success: true, data }
  } catch (error) {
    console.warn('Supabase payment update failed, using localStorage fallback:', error)

    // Fallback to localStorage
    try {
      const storageKey = invoiceType === 'sales' ? 'sales_invoices' : 'purchase_invoices'
      const invoices = JSON.parse(localStorage.getItem(storageKey) || '[]')

      const invoiceIndex = invoices.findIndex((inv: any) => inv.id === invoiceId)
      if (invoiceIndex !== -1) {
        invoices[invoiceIndex].payment_status = paymentStatus
        if (paidAmount) {
          invoices[invoiceIndex].paid_amount = paidAmount
        }

        localStorage.setItem(storageKey, JSON.stringify(invoices))

        // Add cash transaction if payment is completed
        if (paymentStatus === 'paid' && paidAmount) {
          const transactionType = invoiceType === 'sales' ? 'income' : 'expense'
          const category = invoiceType === 'sales' ? 'مبيعات' : 'مشتريات'

          await addCashTransaction({
            transaction_type: transactionType,
            category,
            amount: paidAmount,
            description: `دفع فاتورة ${invoiceType === 'sales' ? 'مبيعات' : 'مشتريات'} رقم ${invoices[invoiceIndex].invoice_number}`,
            reference_type: invoiceType,
            reference_id: invoiceId,
            payment_method: 'cash'
          })
        }

        console.log('Payment status updated in localStorage:', invoices[invoiceIndex])
        return { success: true, data: invoices[invoiceIndex] }
      } else {
        throw new Error('Invoice not found in localStorage')
      }
    } catch (fallbackError) {
      console.error('Error updating payment status (fallback):', fallbackError)
      return { success: false, error: fallbackError }
    }
  }
}

// Advanced Reports Functions
export const getSalesReport = async (filters: {
  start_date?: string
  end_date?: string
  customer_id?: string
  medicine_id?: string
}) => {
  try {
    let query = supabase
      .from('sales_invoices')
      .select(`
        *,
        customers (name, phone),
        sales_invoice_items (
          *,
          medicine_batches (
            batch_code,
            medicines (name, category)
          )
        )
      `)
      .order('created_at', { ascending: false })

    if (filters.start_date) {
      query = query.gte('created_at', filters.start_date)
    }
    if (filters.end_date) {
      query = query.lte('created_at', filters.end_date)
    }
    if (filters.customer_id) {
      query = query.eq('customer_id', filters.customer_id)
    }

    const { data, error } = await query

    if (error) throw error

    // Filter by medicine if specified
    let filteredData = data
    if (filters.medicine_id) {
      filteredData = data.filter(invoice =>
        invoice.sales_invoice_items.some((item: any) =>
          item.medicine_batches?.medicines?.id === filters.medicine_id
        )
      )
    }

    return { success: true, data: filteredData }
  } catch (error) {
    console.error('Error fetching sales report:', error)
    return { success: false, error }
  }
}

export const getPurchasesReport = async (filters: {
  start_date?: string
  end_date?: string
  supplier_id?: string
  medicine_id?: string
}) => {
  try {
    let query = supabase
      .from('purchase_invoices')
      .select(`
        *,
        suppliers (name, contact_person, phone),
        purchase_invoice_items (
          *,
          medicines (name, category)
        )
      `)
      .order('created_at', { ascending: false })

    if (filters.start_date) {
      query = query.gte('created_at', filters.start_date)
    }
    if (filters.end_date) {
      query = query.lte('created_at', filters.end_date)
    }
    if (filters.supplier_id) {
      query = query.eq('supplier_id', filters.supplier_id)
    }

    const { data, error } = await query

    if (error) throw error

    // Filter by medicine if specified
    let filteredData = data
    if (filters.medicine_id) {
      filteredData = data.filter(invoice =>
        invoice.purchase_invoice_items.some((item: any) =>
          item.medicines?.id === filters.medicine_id
        )
      )
    }

    return { success: true, data: filteredData }
  } catch (error) {
    console.error('Error fetching purchases report:', error)
    return { success: false, error }
  }
}

export const getCustomerStatement = async (customerId: string, filters: {
  start_date?: string
  end_date?: string
}) => {
  try {
    let salesQuery = supabase
      .from('sales_invoices')
      .select(`
        id,
        invoice_number,
        total_amount,
        discount_amount,
        final_amount,
        payment_status,
        payment_method,
        created_at,
        notes
      `)
      .eq('customer_id', customerId)
      .order('created_at', { ascending: false })

    let returnsQuery = supabase
      .from('sales_returns')
      .select(`
        id,
        return_number,
        total_amount,
        reason,
        status,
        created_at
      `)
      .eq('customer_id', customerId)
      .order('created_at', { ascending: false })

    if (filters.start_date) {
      salesQuery = salesQuery.gte('created_at', filters.start_date)
      returnsQuery = returnsQuery.gte('created_at', filters.start_date)
    }
    if (filters.end_date) {
      salesQuery = salesQuery.lte('created_at', filters.end_date)
      returnsQuery = returnsQuery.lte('created_at', filters.end_date)
    }

    const [salesResult, returnsResult] = await Promise.all([
      salesQuery,
      returnsQuery
    ])

    if (salesResult.error) throw salesResult.error
    if (returnsResult.error) throw returnsResult.error

    return {
      success: true,
      data: {
        sales: salesResult.data,
        returns: returnsResult.data
      }
    }
  } catch (error) {
    console.error('Error fetching customer statement:', error)
    return { success: false, error }
  }
}

export const getSupplierStatement = async (supplierId: string, filters: {
  start_date?: string
  end_date?: string
}) => {
  try {
    let purchasesQuery = supabase
      .from('purchase_invoices')
      .select(`
        id,
        invoice_number,
        total_amount,
        discount_amount,
        final_amount,
        payment_status,
        payment_method,
        created_at,
        notes
      `)
      .eq('supplier_id', supplierId)
      .order('created_at', { ascending: false })

    let returnsQuery = supabase
      .from('purchase_returns')
      .select(`
        id,
        return_number,
        total_amount,
        reason,
        status,
        created_at
      `)
      .eq('supplier_id', supplierId)
      .order('created_at', { ascending: false })

    if (filters.start_date) {
      purchasesQuery = purchasesQuery.gte('created_at', filters.start_date)
      returnsQuery = returnsQuery.gte('created_at', filters.start_date)
    }
    if (filters.end_date) {
      purchasesQuery = purchasesQuery.lte('created_at', filters.end_date)
      returnsQuery = returnsQuery.lte('created_at', filters.end_date)
    }

    const [purchasesResult, returnsResult] = await Promise.all([
      purchasesQuery,
      returnsQuery
    ])

    if (purchasesResult.error) throw purchasesResult.error
    if (returnsResult.error) throw returnsResult.error

    return {
      success: true,
      data: {
        purchases: purchasesResult.data,
        returns: returnsResult.data
      }
    }
  } catch (error) {
    console.error('Error fetching supplier statement:', error)
    return { success: false, error }
  }
}

export const getMedicineMovementReport = async (medicineId: string, filters: {
  start_date?: string
  end_date?: string
}) => {
  try {
    let query = supabase
      .from('inventory_movements')
      .select(`
        *,
        medicine_batches (
          batch_code,
          expiry_date,
          medicines (name, category)
        )
      `)
      .order('created_at', { ascending: false })

    if (filters.start_date) {
      query = query.gte('created_at', filters.start_date)
    }
    if (filters.end_date) {
      query = query.lte('created_at', filters.end_date)
    }

    const { data, error } = await query

    if (error) throw error

    // Filter by medicine
    const filteredData = data.filter(movement =>
      movement.medicine_batches?.medicines?.id === medicineId
    )

    return { success: true, data: filteredData }
  } catch (error) {
    console.error('Error fetching medicine movement report:', error)
    return { success: false, error }
  }
}

export const getInventoryReport = async (filters: {
  category?: string
  low_stock?: boolean
  expired?: boolean
  expiring_soon?: boolean
}) => {
  try {
    let query = supabase
      .from('medicine_batches')
      .select(`
        *,
        medicines (name, category, manufacturer)
      `)
      .order('expiry_date', { ascending: true })

    const { data, error } = await query

    if (error) throw error

    let filteredData = data

    // Filter by category
    if (filters.category) {
      filteredData = filteredData.filter(batch =>
        batch.medicines?.category === filters.category
      )
    }

    // Filter by low stock (less than 10 units)
    if (filters.low_stock) {
      filteredData = filteredData.filter(batch => batch.quantity < 10)
    }

    // Filter by expired
    if (filters.expired) {
      const today = new Date().toISOString().split('T')[0]
      filteredData = filteredData.filter(batch => batch.expiry_date < today)
    }

    // Filter by expiring soon (within 30 days)
    if (filters.expiring_soon) {
      const thirtyDaysFromNow = new Date()
      thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30)
      const futureDate = thirtyDaysFromNow.toISOString().split('T')[0]
      const today = new Date().toISOString().split('T')[0]

      filteredData = filteredData.filter(batch =>
        batch.expiry_date >= today && batch.expiry_date <= futureDate
      )
    }

    return { success: true, data: filteredData }
  } catch (error) {
    console.error('Error fetching inventory report:', error)
    return { success: false, error }
  }
}

// Update return status (simplified function)
export const updateReturnStatus = async (returnId: string, status: string, rejectionReason?: string) => {
  const updates: any = { status }
  if (rejectionReason) {
    updates.rejection_reason = rejectionReason
  }
  return updateReturn(returnId, updates)
}

// Get return for printing with full details
export const getReturnForPrint = async (returnId: string) => {
  try {
    console.log('🔍 البحث عن المرتجع للطباعة:', returnId)

    // Check localStorage first
    const salesReturns = JSON.parse(localStorage.getItem('sales_returns') || '[]')
    const purchaseReturns = JSON.parse(localStorage.getItem('purchase_returns') || '[]')
    const salesReturnItems = JSON.parse(localStorage.getItem('sales_return_items') || '[]')
    const purchaseReturnItems = JSON.parse(localStorage.getItem('purchase_return_items') || '[]')

    // Find in sales returns
    let foundReturn = salesReturns.find((ret: any) => ret.id === returnId)
    if (foundReturn) {
      const items = salesReturnItems.filter((item: any) => item.return_id === returnId)

      // Enhance items with medicine names from localStorage medicines data
      const medicines = JSON.parse(localStorage.getItem('medicines') || '[]')
      const medicineBatches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')

      const enhancedItems = items.map((item: any) => {
        let medicineName = item.medicine_name || item.medicineName || 'غير محدد'
        let batchCode = item.batch_code || item.batchCode || ''
        let expiryDate = item.expiry_date || item.expiryDate || ''

        // Try to get medicine name from batch if not available
        if (medicineName === 'غير محدد' && item.medicine_batch_id) {
          const batch = medicineBatches.find((b: any) => b.id === item.medicine_batch_id)
          if (batch) {
            batchCode = batch.batch_number || batchCode
            expiryDate = batch.expiry_date || expiryDate

            const medicine = medicines.find((m: any) => m.id === batch.medicine_id)
            if (medicine) {
              medicineName = medicine.name || medicineName
            }
          }
        }

        // Try to get medicine name directly if still not available
        if (medicineName === 'غير محدد' && item.medicine_id) {
          const medicine = medicines.find((m: any) => m.id === item.medicine_id)
          if (medicine) {
            medicineName = medicine.name || medicineName
          }
        }

        return {
          ...item,
          medicine_name: medicineName,
          batch_code: batchCode,
          expiry_date: expiryDate,
          unit_price: item.unit_price || item.unitPrice || 0,
          total_price: item.total_price || item.totalPrice || 0
        }
      })

      const returnData = {
        ...foundReturn,
        type: 'sales',
        return_type: 'sales',
        return_invoice_items: enhancedItems
      }
      console.log('✅ تم العثور على مرتجع مبيعات:', returnData)
      return { success: true, data: returnData }
    }

    // Find in purchase returns
    foundReturn = purchaseReturns.find((ret: any) => ret.id === returnId)
    if (foundReturn) {
      const items = purchaseReturnItems.filter((item: any) => item.return_id === returnId)

      // Enhance items with medicine names from localStorage medicines data
      const medicines = JSON.parse(localStorage.getItem('medicines') || '[]')

      const enhancedItems = items.map((item: any) => {
        let medicineName = item.medicine_name || item.medicineName || 'غير محدد'

        // Try to get medicine name directly if not available
        if (medicineName === 'غير محدد' && item.medicine_id) {
          const medicine = medicines.find((m: any) => m.id === item.medicine_id)
          if (medicine) {
            medicineName = medicine.name || medicineName
          }
        }

        return {
          ...item,
          medicine_name: medicineName,
          batch_code: item.batch_code || item.batchCode || '',
          expiry_date: item.expiry_date || item.expiryDate || '',
          unit_cost: item.unit_cost || item.unitCost || 0,
          total_cost: item.total_cost || item.totalCost || 0
        }
      })

      const returnData = {
        ...foundReturn,
        type: 'purchase',
        return_type: 'purchase',
        return_invoice_items: enhancedItems
      }
      console.log('✅ تم العثور على مرتجع مشتريات:', returnData)
      return { success: true, data: returnData }
    }

    // If not found in localStorage, try Supabase
    console.log('⚠️ لم يتم العثور على المرتجع في localStorage، محاولة Supabase...')

    // Try sales returns first
    const { data: salesReturn, error: salesError } = await supabase
      .from('sales_returns')
      .select(`
        *,
        customers (name, phone, address),
        sales_return_items (
          *,
          medicine_batches (
            batch_code,
            expiry_date,
            medicines (name, category, manufacturer, strength, form)
          )
        )
      `)
      .eq('id', returnId)
      .single()

    if (salesReturn && !salesError) {
      const returnData = {
        ...salesReturn,
        type: 'sales',
        return_type: 'sales',
        return_invoice_items: (salesReturn.sales_return_items || []).map((item: any) => ({
          ...item,
          medicine_name: item.medicine_batches?.medicines?.name || item.medicine_name || 'غير محدد',
          unit_price: item.unit_price || 0,
          total_price: item.total_price || 0
        }))
      }
      console.log('✅ تم العثور على مرتجع مبيعات في Supabase:', returnData)
      return { success: true, data: returnData }
    }

    // Try purchase returns
    const { data: purchaseReturn, error: purchaseError } = await supabase
      .from('purchase_returns')
      .select(`
        *,
        suppliers (name, contact_person, phone, address),
        purchase_return_items (
          *,
          medicines (name, category, manufacturer, strength, form)
        )
      `)
      .eq('id', returnId)
      .single()

    if (purchaseReturn && !purchaseError) {
      const returnData = {
        ...purchaseReturn,
        type: 'purchase',
        return_type: 'purchase',
        return_invoice_items: (purchaseReturn.purchase_return_items || []).map((item: any) => ({
          ...item,
          medicine_name: item.medicines?.name || item.medicine_name || 'غير محدد',
          unit_cost: item.unit_cost || 0,
          total_cost: item.total_cost || 0
        }))
      }
      console.log('✅ تم العثور على مرتجع مشتريات في Supabase:', returnData)
      return { success: true, data: returnData }
    }

    console.log('❌ لم يتم العثور على المرتجع')
    return { success: false, error: 'Return not found' }
  } catch (error) {
    console.error('Error fetching return for print:', error)
    return { success: false, error }
  }
}

// Update return status
export const updateReturn = async (returnId: string, updates: { status?: string, rejection_reason?: string }) => {
  try {
    // Update in localStorage first
    const salesReturns = JSON.parse(localStorage.getItem('sales_returns') || '[]')
    const purchaseReturns = JSON.parse(localStorage.getItem('purchase_returns') || '[]')

    // Find and update in sales returns
    const salesIndex = salesReturns.findIndex((ret: any) => ret.id === returnId)
    if (salesIndex !== -1) {
      salesReturns[salesIndex] = { ...salesReturns[salesIndex], ...updates, updated_at: new Date().toISOString() }
      localStorage.setItem('sales_returns', JSON.stringify(salesReturns))

      // Try to update in Supabase
      try {
        const { error } = await supabase
          .from('sales_returns')
          .update(updates)
          .eq('id', returnId)

        if (error) {
          console.warn('Failed to update return in Supabase:', error)
        }
      } catch (supabaseError) {
        console.warn('Supabase update failed, continuing with localStorage:', supabaseError)
      }

      return { success: true, data: salesReturns[salesIndex] }
    }

    // Find and update in purchase returns
    const purchaseIndex = purchaseReturns.findIndex((ret: any) => ret.id === returnId)
    if (purchaseIndex !== -1) {
      purchaseReturns[purchaseIndex] = { ...purchaseReturns[purchaseIndex], ...updates, updated_at: new Date().toISOString() }
      localStorage.setItem('purchase_returns', JSON.stringify(purchaseReturns))

      // Try to update in Supabase
      try {
        const { error } = await supabase
          .from('purchase_returns')
          .update(updates)
          .eq('id', returnId)

        if (error) {
          console.warn('Failed to update return in Supabase:', error)
        }
      } catch (supabaseError) {
        console.warn('Supabase update failed, continuing with localStorage:', supabaseError)
      }

      return { success: true, data: purchaseReturns[purchaseIndex] }
    }

    return { success: false, error: 'Return not found' }
  } catch (error) {
    console.error('Error updating return:', error)
    return { success: false, error }
  }
}
