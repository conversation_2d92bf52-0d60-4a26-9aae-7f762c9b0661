'use client'

import { useState } from 'react'
import AppLayout from '@/components/AppLayout'
import { fixLocalStorageInvoiceItems } from '@/lib/database'
import { Wrench, CheckCircle, AlertCircle, RefreshCw } from 'lucide-react'

export default function FixDataPage() {
  const [isFixing, setIsFixing] = useState(false)
  const [result, setResult] = useState<any>(null)
  const [logs, setLogs] = useState<string[]>([])

  const addLog = (message: string) => {
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`])
  }

  const handleFixData = async () => {
    setIsFixing(true)
    setResult(null)
    setLogs([])
    
    addLog('🔧 بدء عملية إصلاح البيانات...')
    
    try {
      // Capture console.log messages
      const originalLog = console.log
      console.log = (...args) => {
        addLog(args.join(' '))
        originalLog(...args)
      }
      
      const fixResult = fixLocalStorageInvoiceItems()
      
      // Restore original console.log
      console.log = originalLog
      
      setResult(fixResult)
      
      if (fixResult.success) {
        addLog(`✅ تم إصلاح ${fixResult.fixedCount} عنصر من أصل ${fixResult.totalCount}`)
      } else {
        addLog(`❌ فشل في إصلاح البيانات: ${fixResult.error}`)
      }
      
    } catch (error) {
      addLog(`❌ خطأ غير متوقع: ${error}`)
      setResult({ success: false, error: error })
    } finally {
      setIsFixing(false)
    }
  }

  const checkCurrentData = () => {
    addLog('🔍 فحص البيانات الحالية...')
    
    try {
      const salesItems = JSON.parse(localStorage.getItem('sales_invoice_items') || '[]')
      const medicines = JSON.parse(localStorage.getItem('medicines') || '[]')
      const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')
      
      addLog(`📦 عدد عناصر الفواتير: ${salesItems.length}`)
      addLog(`💊 عدد الأدوية: ${medicines.length}`)
      addLog(`📋 عدد الدفعات: ${batches.length}`)
      
      let itemsWithNames = 0
      let itemsWithoutNames = 0
      
      salesItems.forEach((item: any, index: number) => {
        const hasName = item.medicine_batches?.medicines?.name && item.medicine_batches.medicines.name !== 'غير محدد'
        if (hasName) {
          itemsWithNames++
          addLog(`✅ العنصر ${index + 1}: ${item.medicine_batches.medicines.name}`)
        } else {
          itemsWithoutNames++
          addLog(`❌ العنصر ${index + 1}: اسم الدواء مفقود (${item.medicine_name || 'غير محدد'})`)
        }
      })
      
      addLog(`📊 الملخص: ${itemsWithNames} عنصر بأسماء صحيحة، ${itemsWithoutNames} عنصر بأسماء مفقودة`)
      
    } catch (error) {
      addLog(`❌ خطأ في فحص البيانات: ${error}`)
    }
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center gap-3 mb-6">
            <div className="bg-orange-50 p-3 rounded-lg">
              <Wrench className="h-6 w-6 text-orange-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">إصلاح بيانات الفواتير</h1>
              <p className="text-gray-600">إصلاح أسماء الأدوية المفقودة في الفواتير المحفوظة</p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <button
              onClick={checkCurrentData}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 flex items-center gap-2 justify-center"
            >
              <AlertCircle className="h-5 w-5" />
              فحص البيانات الحالية
            </button>
            
            <button
              onClick={handleFixData}
              disabled={isFixing}
              className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 justify-center"
            >
              {isFixing ? (
                <RefreshCw className="h-5 w-5 animate-spin" />
              ) : (
                <CheckCircle className="h-5 w-5" />
              )}
              {isFixing ? 'جاري الإصلاح...' : 'إصلاح البيانات'}
            </button>
          </div>

          {result && (
            <div className={`p-4 rounded-lg mb-6 ${
              result.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
            }`}>
              <div className="flex items-center gap-2">
                {result.success ? (
                  <CheckCircle className="h-5 w-5 text-green-600" />
                ) : (
                  <AlertCircle className="h-5 w-5 text-red-600" />
                )}
                <span className={`font-medium ${
                  result.success ? 'text-green-800' : 'text-red-800'
                }`}>
                  {result.success
                    ? `تم إصلاح ${result.fixedCount} عنصر من أصل ${result.totalCount} بنجاح!`
                    : 'فشل في إصلاح البيانات'
                  }
                </span>
              </div>
              {result.success && result.notFoundCount > 0 && (
                <div className="mt-2 text-sm text-yellow-700">
                  تحذير: لم يتم العثور على أسماء الأدوية لـ {result.notFoundCount} عنصر
                </div>
              )}
            </div>
          )}

          {logs.length > 0 && (
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="font-medium text-gray-900 mb-3">سجل العمليات:</h3>
              <div className="bg-black text-green-400 p-4 rounded font-mono text-sm max-h-96 overflow-y-auto">
                {logs.map((log, index) => (
                  <div key={index} className="mb-1">
                    {log}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" />
            <div>
              <h3 className="font-medium text-yellow-800 mb-2">ملاحظات مهمة:</h3>
              <ul className="text-yellow-700 space-y-1 text-sm">
                <li>• هذه الأداة تصلح أسماء الأدوية المفقودة في الفواتير المحفوظة محلياً</li>
                <li>• يُنصح بعمل نسخة احتياطية من البيانات قبل تشغيل الإصلاح</li>
                <li>• العملية آمنة ولا تحذف أي بيانات موجودة</li>
                <li>• بعد الإصلاح، ستظهر أسماء الأدوية بشكل صحيح في الطباعة</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </AppLayout>
  )
}
