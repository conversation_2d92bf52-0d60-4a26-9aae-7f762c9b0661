'use client'

import { useState } from 'react'
import Sidebar from './Sidebar'
import Header from './Header'
import ErrorBoundary from './ErrorBoundary'
import ToastNotifications from './ToastNotifications'

interface AppLayoutProps {
  children: React.ReactNode
}

export default function AppLayout({ children }: AppLayoutProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Sidebar isOpen={isMobileMenuOpen} onClose={() => setIsMobileMenuOpen(false)} />
      <Header
        onMobileMenuToggle={toggleMobileMenu}
        isMobileMenuOpen={isMobileMenuOpen}
      />
      <main className="mr-0 md:mr-64 mt-16 p-3 md:p-6">
        <ErrorBoundary>
          <div className="max-w-full overflow-x-auto">
            {children}
          </div>
        </ErrorBoundary>
      </main>
      <ToastNotifications />
    </div>
  )
}
