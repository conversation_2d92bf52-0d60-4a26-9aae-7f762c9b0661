'use client'

import { useState } from 'react'
import AppLayout from '@/components/AppLayout'
import {
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  Package,
  AlertTriangle,
  Calendar,
  Pill
} from 'lucide-react'
import { getMedicines, addMedicine } from '@/lib/database'
import { useEffect } from 'react'

// Sample data - في التطبيق الحقيقي سيتم جلبها من قاعدة البيانات
const medicines = [
  {
    id: '1',
    name: 'باراسيتامول',
    category: 'مسكنات',
    manufacturer: 'شركة الأدوية العراقية',
    strength: '500mg',
    form: 'tablet',
    batches: [
      { id: 'b1', code: 'B001', quantity: 150, expiry: '2024-12-15', costPrice: 500, sellingPrice: 750 },
      { id: 'b2', code: 'B002', quantity: 75, expiry: '2024-10-20', costPrice: 500, sellingPrice: 750 }
    ]
  },
  {
    id: '2',
    name: 'أموكسيسيلين',
    category: 'مضادات حيوية',
    manufacturer: 'شركة بغداد للأدوية',
    strength: '250mg',
    form: 'capsule',
    batches: [
      { id: 'b3', code: 'B003', quantity: 200, expiry: '2025-03-10', costPrice: 1200, sellingPrice: 1800 }
    ]
  },
  {
    id: '3',
    name: 'إيبوبروفين',
    category: 'مسكنات',
    manufacturer: 'شركة الأدوية العراقية',
    strength: '400mg',
    form: 'tablet',
    batches: [
      { id: 'b4', code: 'B004', quantity: 50, expiry: '2024-08-25', costPrice: 800, sellingPrice: 1200 }
    ]
  }
]

export default function InventoryPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [editingBatch, setEditingBatch] = useState<any>(null)
  const [medicines, setMedicines] = useState<any[]>([])
  const [loading, setLoading] = useState(false)

  // Create dynamic categories from medicines data
  const getCategories = () => {
    const baseCategories = ['all', 'مسكنات', 'مضادات حيوية', 'فيتامينات', 'أخرى']
    const medicineCategories = medicines.map(med => med.category).filter(Boolean)
    const allCategories = [...baseCategories, ...medicineCategories]
    // Remove duplicates and return unique categories
    return [...new Set(allCategories)]
  }

  const categories = getCategories()

  // Load data on component mount
  useEffect(() => {
    loadMedicines()
  }, [])

  const loadMedicines = async () => {
    setLoading(true)
    try {
      const result = await getMedicines()
      if (result.success && result.data) {
        setMedicines(result.data)
      } else {
        console.error('Failed to load medicines:', result.error)
        // Use sample data as fallback
        setMedicines([
          {
            id: '1',
            name: 'باراسيتامول',
            category: 'مسكنات',
            manufacturer: 'شركة الأدوية العراقية',
            strength: '500mg',
            form: 'tablet',
            medicine_batches: [
              { id: 'b1', batch_code: 'B001', quantity: 150, expiry_date: '2024-12-15', selling_price: 750 }
            ]
          }
        ])
      }
    } catch (error) {
      console.error('Error loading medicines:', error)
      // Use sample data as fallback
      setMedicines([
        {
          id: '1',
          name: 'باراسيتامول',
          category: 'مسكنات',
          manufacturer: 'شركة الأدوية العراقية',
          strength: '500mg',
          form: 'tablet',
          medicine_batches: [
            { id: 'b1', batch_code: 'B001', quantity: 150, expiry_date: '2024-12-15', selling_price: 750 }
          ]
        }
      ])
    } finally {
      setLoading(false)
    }
  }

  const filteredMedicines = medicines.filter(medicine => {
    const matchesSearch = medicine.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (medicine.manufacturer && medicine.manufacturer.toLowerCase().includes(searchTerm.toLowerCase()))
    const matchesCategory = selectedCategory === 'all' || medicine.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  const getTotalQuantity = (batches: any[]) => {
    if (!batches || !Array.isArray(batches)) return 0
    return batches.reduce((total, batch) => total + (batch.quantity || 0), 0)
  }

  const getExpiringBatches = (batches: any[]) => {
    if (!batches || !Array.isArray(batches)) return []
    const today = new Date()
    const threeMonthsFromNow = new Date()
    threeMonthsFromNow.setMonth(today.getMonth() + 3)

    return batches.filter(batch => {
      const expiryDate = new Date(batch.expiry_date)
      return expiryDate <= threeMonthsFromNow
    })
  }



  const handleEditBatch = (medicine: any, batch: any) => {
    setEditingBatch({ ...batch, medicineId: medicine.id })
  }

  const handleDeleteBatch = (medicineId: string, batchId: string) => {
    if (confirm('هل أنت متأكد من حذف هذه الوجبة؟')) {
      // هنا سيتم حذف الوجبة من قاعدة البيانات
      console.log('Deleting batch:', { medicineId, batchId })
      alert('تم حذف الوجبة بنجاح!')
    }
  }

  const closeBatchModal = () => {
    setEditingBatch(null)
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">إدارة المخزون</h1>
            <p className="text-gray-600 mt-1">عرض الأدوية والوجبات المتوفرة في المخزون</p>
          </div>
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-md">
            <div className="flex items-start gap-3">
              <div className="bg-blue-100 p-2 rounded-full">
                <Package className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <h3 className="font-medium text-blue-900">إضافة أدوية جديدة</h3>
                <p className="text-blue-700 text-sm mt-1">
                  لإضافة أدوية جديدة للمخزون، يرجى استخدام صفحة المشتريات
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="text"
                  placeholder="البحث عن دواء..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-4 pr-10 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
            <div className="md:w-48">
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {categories.map((category, index) => (
                  <option key={`category_${index}_${category}`} value={category}>
                    {category === 'all' ? 'جميع الفئات' : category}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Medicines List */}
        <div className="space-y-4">
          {loading ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-gray-500 mt-4">جاري تحميل البيانات...</p>
            </div>
          ) : filteredMedicines.map((medicine) => {
            const totalQuantity = getTotalQuantity(medicine.medicine_batches)
            const expiringBatches = getExpiringBatches(medicine.medicine_batches)
            
            return (
              <div key={medicine.id} className="bg-white rounded-lg shadow-sm border border-gray-200">
                <div className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-4">
                      <div className="bg-blue-50 p-3 rounded-lg">
                        <Pill className="h-6 w-6 text-blue-600" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">{medicine.name}</h3>
                        <p className="text-sm text-gray-500">
                          {medicine.strength} • {medicine.form} • {medicine.manufacturer}
                        </p>
                        <span className="inline-block bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full mt-1">
                          {medicine.category}
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="text-left">
                        <p className="text-sm text-gray-500">إجمالي الكمية</p>
                        <p className="text-xl font-bold text-gray-900">{totalQuantity}</p>
                      </div>
                      {expiringBatches.length > 0 && (
                        <div className="bg-orange-100 p-2 rounded-lg">
                          <AlertTriangle className="h-5 w-5 text-orange-600" />
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Batches */}
                  <div className="space-y-2">
                    <h4 className="font-medium text-gray-900 mb-3">الوجبات المتوفرة:</h4>
                    {medicine.medicine_batches && medicine.medicine_batches.length > 0 ? (
                      medicine.medicine_batches.map((batch: any) => {
                        const isExpiring = getExpiringBatches([batch]).length > 0

                        return (
                          <div key={batch.id} className={`flex items-center justify-between p-3 rounded-lg border ${
                            isExpiring ? 'border-orange-200 bg-orange-50' : 'border-gray-200 bg-gray-50'
                          }`}>
                            <div className="flex items-center gap-4">
                              <div>
                                <p className="font-medium text-gray-900">وجبة: {batch.batch_code}</p>
                                <div className="flex items-center gap-4 text-sm text-gray-500">
                                  <span>الكمية: {batch.quantity}</span>
                                  <span className="flex items-center gap-1">
                                    <Calendar className="h-3 w-3" />
                                    انتهاء: {batch.expiry_date}
                                  </span>
                                </div>
                              </div>
                            </div>
                            <div className="flex items-center gap-4">
                              <div className="text-left">
                                <p className="text-sm text-gray-500">سعر البيع</p>
                                <p className="font-medium text-gray-900">{batch.selling_price} د.ع</p>
                              </div>
                              <div className="flex items-center gap-2">
                                <button
                                  onClick={() => handleEditBatch(medicine, batch)}
                                  className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg"
                                >
                                  <Edit className="h-4 w-4" />
                                </button>
                                <button
                                  onClick={() => handleDeleteBatch(medicine.id, batch.id)}
                                  className="p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </button>
                              </div>
                            </div>
                          </div>
                        )
                      })
                    ) : (
                      <p className="text-gray-500 text-center py-4">لا توجد وجبات متوفرة</p>
                    )}
                  </div>
                </div>
              </div>
            )
          })}
        </div>

        {filteredMedicines.length === 0 && (
          <div className="text-center py-12">
            <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد أدوية</h3>
            <p className="text-gray-500">لم يتم العثور على أدوية تطابق معايير البحث</p>
          </div>
        )}
      </div>
    </AppLayout>
  )
}
