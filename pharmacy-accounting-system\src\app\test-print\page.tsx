'use client'

import { useState, useEffect } from 'react'
import AppLayout from '@/components/AppLayout'
import { getSalesInvoices, getSalesInvoiceForPrint } from '@/lib/database'
import { Printer, Eye, Search } from 'lucide-react'

export default function TestPrintPage() {
  const [invoices, setInvoices] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedInvoice, setSelectedInvoice] = useState<any>(null)
  const [printData, setPrintData] = useState<any>(null)

  useEffect(() => {
    loadInvoices()
  }, [])

  const loadInvoices = async () => {
    try {
      const result = await getSalesInvoices()
      if (result.success) {
        setInvoices(result.data || [])
      }
    } catch (error) {
      console.error('خطأ في تحميل الفواتير:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleViewInvoice = async (invoiceId: string) => {
    try {
      console.log('🔍 جلب بيانات الفاتورة للطباعة:', invoiceId)
      const result = await getSalesInvoiceForPrint(invoiceId)
      
      if (result.success) {
        console.log('✅ بيانات الفاتورة:', result.data)
        setSelectedInvoice(result.data)
        setPrintData(result.data)
        
        // Log medicine names for debugging - DETAILED
        console.log('🔍 تحليل مفصل لعناصر الفاتورة:')
        result.data.sales_invoice_items?.forEach((item: any, index: number) => {
          console.log(`\n--- العنصر ${index + 1} ---`)
          console.log('البيانات الكاملة:', item)
          console.log('أسماء الأدوية المختلفة:', {
            medicine_name: item.medicine_name,
            medicineName: item.medicineName,
            medicine_batches_name: item.medicine_batches?.medicines?.name,
            batch_id: item.medicine_batch_id,
            batch_data: item.medicine_batches
          })

          // Check localStorage for this batch
          const batches = JSON.parse(localStorage.getItem('medicine_batches') || '[]')
          const medicines = JSON.parse(localStorage.getItem('medicines') || '[]')
          const batch = batches.find((b: any) => b.id === item.medicine_batch_id)
          const medicine = medicines.find((m: any) => m.id === batch?.medicine_id)

          console.log('بيانات من localStorage:', {
            batch_found: !!batch,
            medicine_found: !!medicine,
            batch_data: batch,
            medicine_data: medicine,
            calculated_name: medicine?.name || 'غير موجود'
          })
        })
      } else {
        console.error('❌ فشل في جلب بيانات الفاتورة:', result.error)
      }
    } catch (error) {
      console.error('❌ خطأ في جلب بيانات الفاتورة:', error)
    }
  }

  const handlePrint = () => {
    if (!printData) return
    
    const printWindow = window.open('', '_blank')
    if (!printWindow) return

    const printContent = `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <title>فاتورة مبيعات - ${printData.invoice_number}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
          .header { text-align: center; margin-bottom: 20px; }
          .invoice-info { margin-bottom: 20px; }
          .items-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
          .items-table th, .items-table td { border: 1px solid #ddd; padding: 8px; text-align: center; }
          .items-table th { background-color: #f5f5f5; }
          .total { text-align: left; font-weight: bold; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>فاتورة مبيعات</h1>
          <h2>رقم الفاتورة: ${printData.invoice_number}</h2>
        </div>
        
        <div class="invoice-info">
          <p><strong>العميل:</strong> ${printData.customers?.name || 'عميل نقدي'}</p>
          <p><strong>التاريخ:</strong> ${new Date(printData.created_at).toLocaleDateString('ar-EG')}</p>
          <p><strong>طريقة الدفع:</strong> ${printData.payment_method === 'cash' ? 'نقدي' : 'آجل'}</p>
        </div>
        
        <table class="items-table">
          <thead>
            <tr>
              <th>اسم الدواء</th>
              <th>الكمية</th>
              <th>سعر الوحدة</th>
              <th>الإجمالي</th>
              <th>هدية</th>
            </tr>
          </thead>
          <tbody>
            ${printData.sales_invoice_items?.map((item: any) => {
              const medicineName = item.medicine_batches?.medicines?.name || item.medicine_name || item.medicineName || 'غير محدد'
              return `
                <tr>
                  <td>${medicineName}</td>
                  <td>${item.quantity}</td>
                  <td>${item.unit_price.toFixed(2)}</td>
                  <td>${item.total_price.toFixed(2)}</td>
                  <td>${item.is_gift ? 'نعم' : 'لا'}</td>
                </tr>
              `
            }).join('') || '<tr><td colspan="5">لا توجد عناصر</td></tr>'}
          </tbody>
        </table>
        
        <div class="total">
          <p><strong>المجموع الفرعي:</strong> ${printData.subtotal?.toFixed(2) || '0.00'} جنيه</p>
          <p><strong>الخصم:</strong> ${printData.discount?.toFixed(2) || '0.00'} جنيه</p>
          <p><strong>الضريبة:</strong> ${printData.tax?.toFixed(2) || '0.00'} جنيه</p>
          <p><strong>المجموع النهائي:</strong> ${printData.final_amount?.toFixed(2) || '0.00'} جنيه</p>
        </div>
      </body>
      </html>
    `

    printWindow.document.write(printContent)
    printWindow.document.close()
    printWindow.print()
  }

  if (loading) {
    return (
      <AppLayout>
        <div className="flex justify-center items-center h-64">
          <div className="text-lg">جاري التحميل...</div>
        </div>
      </AppLayout>
    )
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center gap-3 mb-6">
            <div className="bg-blue-50 p-3 rounded-lg">
              <Printer className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">اختبار طباعة الفواتير</h1>
              <p className="text-gray-600">اختبار عرض أسماء الأدوية في الفواتير المطبوعة</p>
            </div>
          </div>

          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    رقم الفاتورة
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    العميل
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    التاريخ
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    المبلغ
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الإجراءات
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {invoices.map((invoice) => (
                  <tr key={invoice.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {invoice.invoice_number}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {invoice.customers?.name || 'عميل نقدي'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(invoice.created_at).toLocaleDateString('ar-EG')}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {invoice.final_amount?.toFixed(2)} جنيه
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                      <button
                        onClick={() => handleViewInvoice(invoice.id)}
                        className="text-blue-600 hover:text-blue-900 ml-2"
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                      {selectedInvoice?.id === invoice.id && (
                        <button
                          onClick={handlePrint}
                          className="text-green-600 hover:text-green-900"
                        >
                          <Printer className="h-4 w-4" />
                        </button>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {selectedInvoice && (
            <div className="mt-6 bg-gray-50 rounded-lg p-4">
              <h3 className="font-medium text-gray-900 mb-3">تفاصيل الفاتورة المحددة:</h3>
              <div className="space-y-2 text-sm">
                <p><strong>رقم الفاتورة:</strong> {selectedInvoice.invoice_number}</p>
                <p><strong>العميل:</strong> {selectedInvoice.customers?.name || 'عميل نقدي'}</p>
                <p><strong>عدد العناصر:</strong> {selectedInvoice.sales_invoice_items?.length || 0}</p>
                
                <div className="mt-4">
                  <h4 className="font-medium mb-2">عناصر الفاتورة:</h4>
                  {selectedInvoice.sales_invoice_items?.map((item: any, index: number) => {
                    const medicineName = item.medicine_batches?.medicines?.name || item.medicine_name || item.medicineName || 'غير محدد'
                    return (
                      <div key={index} className="bg-white p-2 rounded border">
                        <p><strong>اسم الدواء:</strong> {medicineName}</p>
                        <p><strong>الكمية:</strong> {item.quantity}</p>
                        <p><strong>سعر الوحدة:</strong> {item.unit_price} جنيه</p>
                      </div>
                    )
                  })}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </AppLayout>
  )
}
