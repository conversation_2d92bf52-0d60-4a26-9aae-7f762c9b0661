'use client'

import { useState, useEffect } from 'react'
import AppLayout from '@/components/AppLayout'
import {
  Plus,
  Search,
  ShoppingCart,
  Building,
  Calculator,
  Percent,
  Printer,
  Save,
  X,
  Calendar,
  Package
} from 'lucide-react'
import PrintTemplate, { InvoicePrint } from '@/components/PrintTemplate'
import { usePrintSettings, printInvoice } from '@/hooks/usePrintSettings'
import { getMedicines, getSuppliers, completePurchaseTransaction } from '@/lib/database'
import { useClientDate } from '@/hooks/useClientDate'

interface PurchaseItem {
  id: string
  medicineId?: string
  medicineName: string
  category: string
  manufacturer: string
  activeIngredient: string
  strength: string
  form: string
  batchCode: string
  quantity: number
  unitCost: number
  sellingPrice: number
  totalCost: number
  expiryDate: string
  receivedDate: string
}

export default function PurchasesPage() {
  const [purchaseItems, setPurchaseItems] = useState<PurchaseItem[]>([])
  const [selectedSupplier, setSelectedSupplier] = useState<any>(null)
  const [discountAmount, setDiscountAmount] = useState(0)
  const [searchTerm, setSearchTerm] = useState('')
  const [showSupplierModal, setShowSupplierModal] = useState(false)
  const [showAddMedicineModal, setShowAddMedicineModal] = useState(false)
  const [newMedicineData, setNewMedicineData] = useState({
    name: '',
    category: '',
    manufacturer: '',
    activeIngredient: '',
    strength: '',
    form: '',
    batchCode: '',
    quantity: 1,
    unitCost: 0,
    sellingPrice: 0,
    expiryDate: '',
    receivedDate: ''
  })
  const [availableMedicines, setAvailableMedicines] = useState<any[]>([])
  const [suppliers, setSuppliers] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [paymentMethod, setPaymentMethod] = useState<'cash' | 'credit'>('cash')
  const [notes, setNotes] = useState('')
  const [privateNotes, setPrivateNotes] = useState('')
  const [showPrintPreview, setShowPrintPreview] = useState(false)
  const [currentInvoice, setCurrentInvoice] = useState<any>(null)
  const [previousPurchases, setPreviousPurchases] = useState<any[]>([])
  const [medicineSearchTerm, setMedicineSearchTerm] = useState('')
  const [showMedicineSuggestions, setShowMedicineSuggestions] = useState(false)
  const [filteredSuggestions, setFilteredSuggestions] = useState<any[]>([])
  const { settings: printSettings } = usePrintSettings()
  const { mounted, currentDate, generateInvoiceNumber, getCurrentDateISO, formatNumber } = useClientDate()

  // Load data on component mount
  useEffect(() => {
    loadMedicines()
    loadSuppliers()
    loadPreviousPurchases()
  }, [])

  // Set default date when component mounts
  useEffect(() => {
    if (mounted && !newMedicineData.receivedDate) {
      setNewMedicineData(prev => ({
        ...prev,
        receivedDate: getCurrentDateISO()
      }))
    }
  }, [mounted, getCurrentDateISO])

  // Filter suggestions when search term changes
  useEffect(() => {
    if (medicineSearchTerm.length >= 2) {
      const filtered = previousPurchases.filter(item =>
        item.medicineName?.toLowerCase().includes(medicineSearchTerm.toLowerCase()) ||
        item.category?.toLowerCase().includes(medicineSearchTerm.toLowerCase()) ||
        item.manufacturer?.toLowerCase().includes(medicineSearchTerm.toLowerCase()) ||
        item.activeIngredient?.toLowerCase().includes(medicineSearchTerm.toLowerCase())
      )
      setFilteredSuggestions(filtered)
      setShowMedicineSuggestions(filtered.length > 0)
    } else {
      setShowMedicineSuggestions(false)
      setFilteredSuggestions([])
    }
  }, [medicineSearchTerm, previousPurchases])

  const loadMedicines = async () => {
    try {
      const result = await getMedicines()
      if (result.success && result.data) {
        setAvailableMedicines(result.data)
      } else {
        console.error('Failed to load medicines:', result.error)
        setAvailableMedicines([
          { id: '1', name: 'باراسيتامول 500mg', category: 'مسكنات' },
          { id: '2', name: 'أموكسيسيلين 250mg', category: 'مضادات حيوية' }
        ])
      }
    } catch (error) {
      console.error('Error loading medicines:', error)
      setAvailableMedicines([
        { id: '1', name: 'باراسيتامول 500mg', category: 'مسكنات' },
        { id: '2', name: 'أموكسيسيلين 250mg', category: 'مضادات حيوية' }
      ])
    }
  }

  const loadSuppliers = async () => {
    try {
      const result = await getSuppliers()
      if (result.success && result.data) {
        setSuppliers(result.data)
      } else {
        console.error('Failed to load suppliers:', result.error)
        setSuppliers([
          { id: '1', name: 'شركة الأدوية العراقية', contact_person: 'أحمد محمد', phone: '07901234567' },
          { id: '2', name: 'شركة بغداد للأدوية', contact_person: 'فاطمة علي', phone: '07801234567' }
        ])
      }
    } catch (error) {
      console.error('Error loading suppliers:', error)
      setSuppliers([
        { id: '1', name: 'شركة الأدوية العراقية', contact_person: 'أحمد محمد', phone: '07901234567' },
        { id: '2', name: 'شركة بغداد للأدوية', contact_person: 'فاطمة علي', phone: '07801234567' }
      ])
    }
  }

  const selectSupplier = (supplier: any) => {
    setSelectedSupplier(supplier)
    setShowSupplierModal(false)
  }

  const loadPreviousPurchases = async () => {
    try {
      // Load from localStorage first
      const localPurchases = JSON.parse(localStorage.getItem('purchase_invoice_items') || '[]')

      // Create unique medicine entries from previous purchases
      const uniqueMedicines = new Map()

      localPurchases.forEach((item: any) => {
        const key = `${item.medicineName}_${item.manufacturer}_${item.activeIngredient}`
        if (!uniqueMedicines.has(key)) {
          uniqueMedicines.set(key, {
            medicineName: item.medicineName,
            category: item.category,
            manufacturer: item.manufacturer,
            activeIngredient: item.activeIngredient,
            strength: item.strength,
            form: item.form,
            lastUnitCost: item.unitCost,
            lastSellingPrice: item.sellingPrice,
            lastPurchaseDate: item.created_at,
            purchaseCount: 1
          })
        } else {
          const existing = uniqueMedicines.get(key)
          existing.purchaseCount += 1
          // Keep the most recent prices
          if (new Date(item.created_at) > new Date(existing.lastPurchaseDate)) {
            existing.lastUnitCost = item.unitCost
            existing.lastSellingPrice = item.sellingPrice
            existing.lastPurchaseDate = item.created_at
          }
        }
      })

      setPreviousPurchases(Array.from(uniqueMedicines.values()))
    } catch (error) {
      console.error('Error loading previous purchases:', error)
    }
  }

  const selectMedicineFromSuggestion = (suggestion: any) => {
    setNewMedicineData({
      ...newMedicineData,
      name: suggestion.medicineName,
      category: suggestion.category,
      manufacturer: suggestion.manufacturer,
      activeIngredient: suggestion.activeIngredient,
      strength: suggestion.strength,
      form: suggestion.form,
      unitCost: suggestion.lastUnitCost || 0,
      sellingPrice: suggestion.lastSellingPrice || 0
    })
    setMedicineSearchTerm(suggestion.medicineName)
    setShowMedicineSuggestions(false)
  }

  const resetMedicineForm = () => {
    setNewMedicineData({
      name: '',
      category: '',
      manufacturer: '',
      activeIngredient: '',
      strength: '',
      form: '',
      batchCode: '',
      quantity: 1,
      unitCost: 0,
      sellingPrice: 0,
      expiryDate: '',
      receivedDate: getCurrentDateISO()
    })
    setMedicineSearchTerm('')
    setShowMedicineSuggestions(false)
  }

  const handleAddMedicine = () => {
    if (!newMedicineData.name || !newMedicineData.category || !newMedicineData.form || 
        !newMedicineData.batchCode || !newMedicineData.expiryDate || 
        newMedicineData.unitCost <= 0 || newMedicineData.sellingPrice <= 0) {
      alert('يرجى ملء جميع الحقول المطلوبة')
      return
    }

    const existingMedicine = availableMedicines.find(med => 
      med.name.toLowerCase() === newMedicineData.name.toLowerCase()
    )

    const newItem: PurchaseItem = {
      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      medicineId: existingMedicine?.id || null,
      medicineName: newMedicineData.name,
      category: newMedicineData.category,
      manufacturer: newMedicineData.manufacturer,
      activeIngredient: newMedicineData.activeIngredient,
      strength: newMedicineData.strength,
      form: newMedicineData.form,
      batchCode: newMedicineData.batchCode,
      quantity: newMedicineData.quantity,
      unitCost: newMedicineData.unitCost,
      sellingPrice: newMedicineData.sellingPrice,
      totalCost: newMedicineData.quantity * newMedicineData.unitCost,
      expiryDate: newMedicineData.expiryDate,
      receivedDate: newMedicineData.receivedDate
    }

    setPurchaseItems([...purchaseItems, newItem])
    setShowAddMedicineModal(false)
    resetMedicineForm()
  }

  const updateQuantity = (id: string, newQuantity: number) => {
    // السماح بالقيم الفارغة أو الصفر دون حذف العنصر
    const quantity = Math.max(0, newQuantity || 0)

    setPurchaseItems(items =>
      items.map(item =>
        item.id === id
          ? { ...item, quantity: quantity, totalCost: quantity * item.unitCost }
          : item
      )
    )
  }

  const updateUnitCost = (id: string, newCost: number) => {
    setPurchaseItems(items =>
      items.map(item =>
        item.id === id
          ? { ...item, unitCost: newCost, totalCost: item.quantity * newCost }
          : item
      )
    )
  }

  const updateSellingPrice = (id: string, newPrice: number) => {
    setPurchaseItems(items =>
      items.map(item =>
        item.id === id
          ? { ...item, sellingPrice: newPrice }
          : item
      )
    )
  }

  const removeItem = (id: string) => {
    setPurchaseItems(items => items.filter(item => item.id !== id))
  }

  const calculateSubtotal = () => {
    return purchaseItems.reduce((total, item) => total + item.totalCost, 0)
  }

  const calculateFinalAmount = () => {
    return calculateSubtotal() - discountAmount
  }

  const handleSaveInvoice = async () => {
    if (!selectedSupplier) {
      alert('يرجى اختيار المورد')
      return
    }

    if (purchaseItems.length === 0) {
      alert('يرجى إضافة عناصر للفاتورة')
      return
    }

    setLoading(true)
    try {
      const invoiceNumber = `PUR-${Date.now()}`
      
      const invoiceData = {
        invoice_number: invoiceNumber,
        supplier_id: selectedSupplier.id,
        total_amount: calculateSubtotal(),
        discount_amount: discountAmount,
        final_amount: calculateFinalAmount(),
        payment_method: paymentMethod,
        payment_status: paymentMethod === 'cash' ? 'paid' : 'pending',
        notes: notes,
        private_notes: privateNotes
      }

      const dbItems = purchaseItems.map(item => ({
        medicineId: item.medicineId || null,
        medicineName: item.medicineName,
        category: item.category,
        manufacturer: item.manufacturer,
        activeIngredient: item.activeIngredient,
        strength: item.strength,
        form: item.form,
        batchCode: item.batchCode,
        quantity: item.quantity,
        unitCost: item.unitCost,
        totalCost: item.totalCost,
        expiryDate: item.expiryDate,
        receivedDate: item.receivedDate,
        sellingPrice: item.sellingPrice
      }))

      const result = await completePurchaseTransaction(invoiceData, dbItems)
      
      if (result.success) {
        const completeInvoiceData = {
          ...invoiceData,
          invoiceNumber,
          date: getCurrentDateISO(),
          supplierName: selectedSupplier?.name,
          supplierPhone: selectedSupplier?.phone,
          supplierAddress: selectedSupplier?.address,
          items: purchaseItems,
          subtotal: calculateSubtotal(),
          discount: discountAmount,
          finalAmount: calculateFinalAmount(),
          purchase_invoice_items: dbItems.map(item => ({
            ...item,
            unit_cost: item.unitCost,
            total_cost: item.totalCost,
            batch_code: item.batchCode,
            expiry_date: item.expiryDate,
            medicine_name: item.medicineName,
            medicines: {
              name: item.medicineName || 'غير محدد',
              category: item.category || '',
              manufacturer: item.manufacturer || '',
              strength: item.strength || '',
              form: item.form || ''
            }
          }))
        }

        setCurrentInvoice(completeInvoiceData)
        alert('تم حفظ فاتورة الشراء بنجاح!')

        // Auto print invoice
        setTimeout(() => {
          printInvoice(completeInvoiceData, 'purchase', printSettings)
        }, 500)

        // Save items to localStorage for future suggestions
        const existingItems = JSON.parse(localStorage.getItem('purchase_invoice_items') || '[]')
        const newItems = dbItems.map(item => ({
          ...item,
          created_at: new Date().toISOString()
        }))
        existingItems.push(...newItems)
        localStorage.setItem('purchase_invoice_items', JSON.stringify(existingItems))

        // Reload previous purchases for updated suggestions
        await loadPreviousPurchases()

        setPurchaseItems([])
        setSelectedSupplier(null)
        setDiscountAmount(0)
        setPaymentMethod('cash')
        setNotes('')
        setPrivateNotes('')
        await loadMedicines()
      } else {
        alert('حدث خطأ أثناء حفظ الفاتورة: ' + (result.error?.message || 'خطأ غير معروف'))
      }
    } catch (error) {
      console.error('Error saving purchase invoice:', error)
      alert('حدث خطأ أثناء حفظ الفاتورة')
    } finally {
      setLoading(false)
    }
  }

  const handlePrintInvoice = () => {
    if (!currentInvoice && purchaseItems.length > 0) {
      handleSaveInvoice()
    }
    setShowPrintPreview(true)
  }

  const handleDirectPrint = () => {
    if (currentInvoice) {
      printInvoice(currentInvoice, 'purchase', printSettings)
    } else {
      alert('لا توجد فاتورة للطباعة')
    }
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">فواتير المشتريات</h1>
            <p className="text-gray-600 mt-1">إنشاء وإدارة فواتير شراء الأدوية من الموردين</p>
          </div>
          <div className="flex items-center gap-3">
            <div className="bg-green-50 border border-green-200 rounded-lg px-4 py-2">
              <p className="text-green-800 text-sm font-medium">
                💡 الطريقة الوحيدة لإضافة أدوية للمخزون
              </p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 space-y-6">
            {/* Supplier Selection */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">اختيار المورد</h2>

              {selectedSupplier ? (
                <div className="flex items-center justify-between p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-center gap-3">
                    <Building className="h-5 w-5 text-blue-600" />
                    <div>
                      <p className="font-medium text-blue-900">{selectedSupplier.name}</p>
                      <p className="text-sm text-blue-700">
                        {selectedSupplier.contact_person} • {selectedSupplier.phone}
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() => setSelectedSupplier(null)}
                    className="text-blue-600 hover:text-blue-800"
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>
              ) : (
                <button
                  onClick={() => setShowSupplierModal(true)}
                  className="w-full border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 hover:bg-blue-50 transition-colors"
                >
                  <Building className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-gray-600">انقر لاختيار مورد</p>
                </button>
              )}
            </div>

            {/* Add Medicine Button */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold text-gray-900">إضافة الأدوية</h2>
                <button
                  onClick={() => setShowAddMedicineModal(true)}
                  className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center gap-2"
                >
                  <Plus className="h-4 w-4" />
                  إضافة دواء
                </button>
              </div>

              {/* Purchase Items List */}
              {purchaseItems.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Package className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>لم يتم إضافة أي أدوية بعد</p>
                  <p className="text-sm">انقر على "إضافة دواء" لبدء إنشاء الفاتورة</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {purchaseItems.map((item) => (
                    <div key={item.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1">
                          <h3 className="font-medium text-gray-900 text-lg">{item.medicineName}</h3>
                          <div className="grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600">
                            <div>
                              <span className="font-medium">الفئة:</span> {item.category}
                            </div>
                            <div>
                              <span className="font-medium">الشركة المصنعة:</span> {item.manufacturer}
                            </div>
                            <div>
                              <span className="font-medium">المادة الفعالة:</span> {item.activeIngredient}
                            </div>
                            <div>
                              <span className="font-medium">التركيز:</span> {item.strength}
                            </div>
                            <div>
                              <span className="font-medium">الشكل:</span> {item.form}
                            </div>
                            <div>
                              <span className="font-medium">رقم الوجبة:</span> {item.batchCode}
                            </div>
                          </div>
                        </div>
                        <button
                          onClick={() => removeItem(item.id)}
                          className="p-2 bg-red-100 text-red-600 rounded-lg hover:bg-red-200"
                        >
                          <X className="h-4 w-4" />
                        </button>
                      </div>

                      <div className="grid grid-cols-2 md:grid-cols-5 gap-4 p-3 bg-gray-50 rounded-lg">
                        <div className="text-center">
                          <p className="text-sm text-gray-500 mb-1">الكمية</p>
                          <div className="flex items-center gap-2 justify-center">
                            <button
                              onClick={() => updateQuantity(item.id, item.quantity - 1)}
                              className="w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center text-gray-600 hover:bg-gray-300"
                            >
                              -
                            </button>
                            <input
                              type="number"
                              value={item.quantity || ''}
                              onChange={(e) => {
                                const value = e.target.value
                                if (value === '') {
                                  updateQuantity(item.id, 0)
                                } else {
                                  const numValue = parseInt(value)
                                  if (!isNaN(numValue)) {
                                    updateQuantity(item.id, numValue)
                                  }
                                }
                              }}
                              onBlur={(e) => {
                                if (e.target.value === '' || parseInt(e.target.value) === 0) {
                                  updateQuantity(item.id, 1)
                                }
                              }}
                              className="w-16 text-center font-medium border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
                              min="0"
                              placeholder="1"
                            />
                            <button
                              onClick={() => updateQuantity(item.id, item.quantity + 1)}
                              className="w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center text-gray-600 hover:bg-gray-300"
                            >
                              +
                            </button>
                          </div>
                        </div>
                        <div className="text-center">
                          <p className="text-sm text-gray-500 mb-1">سعر الشراء</p>
                          <input
                            type="number"
                            value={item.unitCost}
                            onChange={(e) => updateUnitCost(item.id, Number(e.target.value))}
                            className="w-full px-2 py-1 text-sm border border-gray-300 rounded text-center"
                            placeholder="0"
                          />
                        </div>
                        <div className="text-center">
                          <p className="text-sm text-gray-500 mb-1">سعر البيع</p>
                          <input
                            type="number"
                            value={item.sellingPrice}
                            onChange={(e) => updateSellingPrice(item.id, Number(e.target.value))}
                            className="w-full px-2 py-1 text-sm border border-gray-300 rounded text-center"
                            placeholder="0"
                          />
                        </div>
                        <div className="text-center">
                          <p className="text-sm text-gray-500 mb-1">الربح</p>
                          <p className="font-medium text-green-600">
                            {formatNumber((item.sellingPrice - item.unitCost) * item.quantity)} د.ع
                          </p>
                        </div>
                        <div className="text-center">
                          <p className="text-sm text-gray-500 mb-1">المجموع</p>
                          <p className="font-medium text-blue-600">{formatNumber(item.totalCost)} د.ع</p>
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-4 mt-3 text-sm text-gray-600">
                        <div>
                          <span className="font-medium">تاريخ الانتهاء:</span> {item.expiryDate}
                        </div>
                        <div>
                          <span className="font-medium">تاريخ الاستلام:</span> {item.receivedDate}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Right Column - Summary */}
          <div className="space-y-6">
            {/* Invoice Summary */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">ملخص الفاتورة</h2>

              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">المجموع الفرعي:</span>
                  <span className="font-medium">{formatNumber(calculateSubtotal())} د.ع</span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-600">الخصم:</span>
                  <div className="flex items-center gap-2">
                    <input
                      type="number"
                      value={discountAmount}
                      onChange={(e) => setDiscountAmount(Number(e.target.value))}
                      className="w-20 px-2 py-1 text-sm border border-gray-300 rounded text-center"
                      placeholder="0"
                      min="0"
                    />
                    <span className="text-sm text-gray-500">د.ع</span>
                  </div>
                </div>

                <div className="border-t pt-3">
                  <div className="flex justify-between text-lg font-semibold">
                    <span>المجموع النهائي:</span>
                    <span className="text-blue-600">{formatNumber(calculateFinalAmount())} د.ع</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Payment Method */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">طريقة الدفع</h2>

              <div className="grid grid-cols-2 gap-3">
                <button
                  onClick={() => setPaymentMethod('cash')}
                  className={`p-3 border-2 rounded-lg text-center transition-colors ${
                    paymentMethod === 'cash'
                      ? 'border-green-500 bg-green-50 text-green-700'
                      : 'border-gray-300 text-gray-600 hover:border-green-300'
                  }`}
                >
                  <div className="font-medium">نقداً</div>
                  <div className="text-sm">دفع فوري</div>
                </button>

                <button
                  onClick={() => setPaymentMethod('credit')}
                  className={`p-3 border-2 rounded-lg text-center transition-colors ${
                    paymentMethod === 'credit'
                      ? 'border-orange-500 bg-orange-50 text-orange-700'
                      : 'border-gray-300 text-gray-600 hover:border-orange-300'
                  }`}
                >
                  <div className="font-medium">آجل</div>
                  <div className="text-sm">دفع لاحق</div>
                </button>
              </div>

              {paymentMethod === 'credit' && (
                <div className="mt-3 p-3 bg-orange-50 border border-orange-200 rounded-lg">
                  <p className="text-orange-800 text-sm">
                    ⚠️ سيتم إضافة هذا المبلغ لحساب المورد كدين
                  </p>
                </div>
              )}
            </div>

            {/* Notes */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">الملاحظات</h2>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    ملاحظات عامة (تظهر في الطباعة)
                  </label>
                  <textarea
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    rows={2}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="ملاحظات للمورد..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    ملاحظات خاصة (للنظام فقط)
                  </label>
                  <textarea
                    value={privateNotes}
                    onChange={(e) => setPrivateNotes(e.target.value)}
                    rows={2}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50"
                    placeholder="ملاحظات داخلية..."
                  />
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="space-y-3">
              <button
                onClick={handleSaveInvoice}
                disabled={purchaseItems.length === 0 || !selectedSupplier || loading}
                className="w-full bg-blue-600 text-white px-4 py-3 rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center justify-center gap-2"
              >
                <Save className="h-4 w-4" />
                {loading ? 'جاري الحفظ...' : 'حفظ فاتورة الشراء'}
              </button>

              <div className="grid grid-cols-2 gap-3">
                <button
                  onClick={handlePrintInvoice}
                  disabled={purchaseItems.length === 0}
                  className="bg-green-600 text-white px-4 py-3 rounded-lg hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                >
                  <Printer className="h-4 w-4" />
                  معاينة وطباعة
                </button>

                <button
                  onClick={handleDirectPrint}
                  disabled={!currentInvoice}
                  className="bg-purple-600 text-white px-4 py-3 rounded-lg hover:bg-purple-700 disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                >
                  <Printer className="h-4 w-4" />
                  طباعة مباشرة
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Supplier Selection Modal */}
        {showSupplierModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold text-gray-900">اختيار المورد</h2>
                <button
                  onClick={() => setShowSupplierModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>

              <div className="space-y-3">
                {suppliers.map((supplier) => (
                  <button
                    key={supplier.id}
                    onClick={() => selectSupplier(supplier)}
                    className="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-blue-50 hover:border-blue-300 transition-colors"
                  >
                    <div className="flex items-center gap-3">
                      <Building className="h-5 w-5 text-gray-400" />
                      <div>
                        <p className="font-medium text-gray-900">{supplier.name}</p>
                        <p className="text-sm text-gray-500">
                          {supplier.contact_person} • {supplier.phone}
                        </p>
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Add Medicine Modal */}
        {showAddMedicineModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-4xl max-h-screen overflow-y-auto">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h2 className="text-lg font-semibold text-gray-900">إضافة دواء للفاتورة</h2>
                  <p className="text-sm text-blue-600 mt-1">
                    💡 ابدأ بكتابة اسم الدواء للبحث في {previousPurchases.length} دواء من المشتريات السابقة
                  </p>
                </div>
                <button
                  onClick={() => {
                    setShowAddMedicineModal(false)
                    resetMedicineForm()
                  }}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Basic Information */}
                <div className="space-y-4">
                  <h3 className="text-md font-semibold text-gray-800 border-b pb-2">المعلومات الأساسية</h3>

                  <div className="relative">
                    <label className="block text-sm font-medium text-gray-700 mb-1">اسم الدواء *</label>
                    <input
                      type="text"
                      value={medicineSearchTerm || newMedicineData.name}
                      onChange={(e) => {
                        const value = e.target.value
                        setMedicineSearchTerm(value)
                        setNewMedicineData({ ...newMedicineData, name: value })
                      }}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="ابدأ بكتابة اسم الدواء للبحث في المشتريات السابقة..."
                    />

                    {/* Suggestions dropdown */}
                    {showMedicineSuggestions && filteredSuggestions.length > 0 && (
                      <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                        {filteredSuggestions.map((suggestion, index) => (
                          <div
                            key={index}
                            onClick={() => selectMedicineFromSuggestion(suggestion)}
                            className="p-3 hover:bg-blue-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                          >
                            <div className="font-medium text-gray-900">{suggestion.medicineName}</div>
                            <div className="text-sm text-gray-600">
                              {suggestion.manufacturer} - {suggestion.category}
                            </div>
                            <div className="text-xs text-blue-600 mt-1">
                              آخر سعر شراء: {formatNumber(suggestion.lastUnitCost || 0)} د.ع |
                              آخر سعر بيع: {formatNumber(suggestion.lastSellingPrice || 0)} د.ع |
                              تم شراؤه {suggestion.purchaseCount} مرة
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">الفئة *</label>
                    <select
                      value={newMedicineData.category}
                      onChange={(e) => setNewMedicineData({ ...newMedicineData, category: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="">اختر الفئة</option>
                      <option value="مسكنات">مسكنات</option>
                      <option value="مضادات حيوية">مضادات حيوية</option>
                      <option value="فيتامينات">فيتامينات</option>
                      <option value="أدوية القلب">أدوية القلب</option>
                      <option value="أدوية الجهاز الهضمي">أدوية الجهاز الهضمي</option>
                      <option value="أدوية الجهاز التنفسي">أدوية الجهاز التنفسي</option>
                      <option value="أخرى">أخرى</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">الشركة المصنعة</label>
                    <input
                      type="text"
                      value={newMedicineData.manufacturer}
                      onChange={(e) => setNewMedicineData({ ...newMedicineData, manufacturer: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="اسم الشركة المصنعة"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">المادة الفعالة</label>
                    <input
                      type="text"
                      value={newMedicineData.activeIngredient}
                      onChange={(e) => setNewMedicineData({ ...newMedicineData, activeIngredient: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="المادة الفعالة"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">التركيز</label>
                      <input
                        type="text"
                        value={newMedicineData.strength}
                        onChange={(e) => setNewMedicineData({ ...newMedicineData, strength: e.target.value })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="500mg"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">الشكل *</label>
                      <select
                        value={newMedicineData.form}
                        onChange={(e) => setNewMedicineData({ ...newMedicineData, form: e.target.value })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="">اختر الشكل</option>
                        <option value="tablet">قرص</option>
                        <option value="capsule">كبسولة</option>
                        <option value="syrup">شراب</option>
                        <option value="injection">حقنة</option>
                        <option value="cream">كريم</option>
                        <option value="drops">قطرة</option>
                        <option value="powder">بودرة</option>
                      </select>
                    </div>
                  </div>
                </div>

                {/* Purchase Details */}
                <div className="space-y-4">
                  <h3 className="text-md font-semibold text-gray-800 border-b pb-2">تفاصيل الشراء</h3>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">كود الوجبة *</label>
                    <input
                      type="text"
                      value={newMedicineData.batchCode}
                      onChange={(e) => setNewMedicineData({ ...newMedicineData, batchCode: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="B001"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">الكمية *</label>
                      <input
                        type="number"
                        value={newMedicineData.quantity}
                        onChange={(e) => setNewMedicineData({ ...newMedicineData, quantity: Number(e.target.value) })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        min="1"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">تاريخ الاستلام</label>
                      <input
                        type="date"
                        value={newMedicineData.receivedDate}
                        onChange={(e) => setNewMedicineData({ ...newMedicineData, receivedDate: e.target.value })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">تاريخ الانتهاء *</label>
                    <input
                      type="date"
                      value={newMedicineData.expiryDate}
                      onChange={(e) => setNewMedicineData({ ...newMedicineData, expiryDate: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">سعر الشراء *</label>
                      <input
                        type="number"
                        value={newMedicineData.unitCost}
                        onChange={(e) => setNewMedicineData({ ...newMedicineData, unitCost: Number(e.target.value) })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="0"
                        min="0"
                        step="0.01"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">سعر البيع *</label>
                      <input
                        type="number"
                        value={newMedicineData.sellingPrice}
                        onChange={(e) => setNewMedicineData({ ...newMedicineData, sellingPrice: Number(e.target.value) })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="0"
                        min="0"
                        step="0.01"
                      />
                    </div>
                  </div>

                  <div className="p-3 bg-green-50 rounded-lg">
                    <div className="text-sm text-green-800">
                      <div className="flex justify-between">
                        <span>إجمالي التكلفة:</span>
                        <span className="font-medium">{formatNumber(newMedicineData.quantity * newMedicineData.unitCost)} د.ع</span>
                      </div>
                      <div className="flex justify-between">
                        <span>الربح المتوقع:</span>
                        <span className="font-medium">{formatNumber((newMedicineData.sellingPrice - newMedicineData.unitCost) * newMedicineData.quantity)} د.ع</span>
                      </div>
                      <div className="flex justify-between">
                        <span>نسبة الربح:</span>
                        <span className="font-medium">
                          {newMedicineData.unitCost > 0 ?
                            `${(((newMedicineData.sellingPrice - newMedicineData.unitCost) / newMedicineData.unitCost) * 100).toFixed(1)}%`
                            : '0%'
                          }
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex justify-end gap-3 mt-6">
                <button
                  type="button"
                  onClick={() => {
                    setShowAddMedicineModal(false)
                    resetMedicineForm()
                  }}
                  className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
                >
                  إلغاء
                </button>
                <button
                  type="button"
                  onClick={handleAddMedicine}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  إضافة للفاتورة
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Print Preview Modal */}
        {showPrintPreview && currentInvoice && (
          <PrintTemplate
            title="فاتورة مشتريات"
            data={currentInvoice}
            type="invoice"
            settings={printSettings}
            onClose={() => setShowPrintPreview(false)}
          >
            <InvoicePrint
              invoice={currentInvoice}
              type="purchase"
              settings={printSettings}
            />
          </PrintTemplate>
        )}
      </div>
    </AppLayout>
  )
}
