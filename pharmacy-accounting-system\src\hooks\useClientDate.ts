'use client'

import { useState, useEffect } from 'react'

export function useClientDate() {
  const [mounted, setMounted] = useState(false)
  const [currentDate, setCurrentDate] = useState('')

  useEffect(() => {
    setMounted(true)
    setCurrentDate(new Date().toLocaleDateString('ar-EG'))
  }, [])

  return {
    mounted,
    currentDate,
    generateInvoiceNumber: () => `INV-${Date.now()}`,
    getCurrentDateISO: () => new Date().toISOString().split('T')[0],
    formatNumber: (num: number) => mounted ? num.toLocaleString() : num.toString()
  }
}
